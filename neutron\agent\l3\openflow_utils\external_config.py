# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

FIP_EXT_DEV_PREFIX = 'fg-'
DVR_BRIDGE_EXT_DEV_PREFIX = 'ex-'


class OpenFlowExternalRouterMixin(object):

    def get_dvr_bridge_ext_dev_name(self, interface_name):
        return (DVR_BRIDGE_EXT_DEV_PREFIX +
                interface_name.partition('-')[2])[:14]

    def external_gateway_added(self, ex_gw_port, interface_name):
        gateway_mac = ex_gw_port['mac_address']

        br_ex_interface_name = interface_name
        br_dvrx_interface_name = self.get_dvr_bridge_ext_dev_name(
            interface_name)
        self.br.add_patch_port(br_dvrx_interface_name, br_ex_interface_name)
        attrs = [('type', 'patch'),
                 ('options', {'peer': br_dvrx_interface_name}),
                 ('external_ids', {'iface-id': ex_gw_port['id'],
                                   'iface-status': 'active',
                                   'attached-mac': gateway_mac})]

        self.int_br.add_port(br_ex_interface_name, *attrs)

        of_port = self.br.get_port_ofport(br_dvrx_interface_name)

        for fixed_ip in ex_gw_port['fixed_ips']:
            ip = fixed_ip['ip_address']
            self.inf_mac_addresses[ip] = gateway_mac
            self.inf_ip_of_ports[ip] = of_port

        self.br.install_learn_action_flows(of_port)

    def get_ext_device_name(self, port_id):
        return (FIP_EXT_DEV_PREFIX + port_id)[:self.driver.DEV_NAME_LEN]

    def ensure_fg_gateway_port_binding(self, fip_agent_port):
        fg_dev = self.get_ext_device_name(fip_agent_port['id'])
        attrs = [('type', 'internal'),
                 ('external_ids', {
                     'iface-id': fip_agent_port['id'],
                     'iface-status': 'active',
                     'attached-mac': fip_agent_port['mac_address']})]

        self.int_br.add_port(fg_dev, *attrs)

    def external_gateway_updated(self, ex_gw_port, interface_name):
        self.external_gateway_added(ex_gw_port, interface_name)

    def external_gateway_removed(self, ex_gw_port, interface_name):
        br_ex_interface_name = interface_name
        br_dvrx_interface_name = self.get_dvr_bridge_ext_dev_name(
            interface_name)
        self.br.remove_external_gateway_flows(ex_gw_port)
        self.br.delete_port(br_dvrx_interface_name)
        self.int_br.delete_port(br_ex_interface_name)

    def remove_external_gateway_ip(self, device, ip_cidr):
        pass
