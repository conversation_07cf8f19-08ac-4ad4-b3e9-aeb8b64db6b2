..
      Licensed under the Apache License, Version 2.0 (the "License"); you may
      not use this file except in compliance with the License. You may obtain
      a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
      WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
      License for the specific language governing permissions and limitations
      under the License.


      Convention for heading levels in Neutron devref:
      =======  Heading 0 (reserved for the title in a document)
      -------  Heading 1
      ~~~~~~~  Heading 2
      +++++++  Heading 3
      '''''''  Heading 4
      (Avoid deeper levels because they do not render well.)


Neutron Stadium i18n
====================

* Refer to oslo_i18n documentation for the general mechanisms that should
  be used: https://docs.openstack.org/oslo.i18n/latest/user/usage.html

* Each stadium project should NOT consume _i18n module from neutron-lib
  or neutron.

* It is recommended that you create a {package_name}/_i18n.py file
  in your repo, and use that. Your localization strings will also live
  in your repo.
