Edit the ``/etc/hosts`` file to contain the following:

.. path /etc/hosts
.. code-block:: none

   # controller
   *********       controller

   # compute1
   *********       compute1

   # block1
   *********       block1

   # object1
   *********       object1

   # object2
   *********       object2

.. end

.. warning::

   Some distributions add an extraneous entry in the ``/etc/hosts``
   file that resolves the actual hostname to another loopback IP
   address such as ``*********``. You must comment out or remove this
   entry to prevent name resolution problems.
   **Do not remove the 127.0.0.1 entry.**

.. note::

   This guide includes host entries for optional services in order to reduce
   complexity should you choose to deploy them.
