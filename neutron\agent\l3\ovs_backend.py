# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from oslo_log import log as logging

from neutron.agent.l3 import backend
from neutron.agent.l3.openflow_utils import mock_utils
from neutron.agent.linux import external_process
from neutron.agent.metadata import driver as metadata_driver

LOG = logging.getLogger(__name__)


class OVSBackend(backend.Backend):

    def __init__(self,
                 agent,
                 use_ipv6=False):
        super(OVSBackend, self).__init__(agent, use_ipv6)
        if use_ipv6:
            msg = ("IPv6 support is not implemented "
                   "for the OVS L3 agent backend. IPv6 will be ignored "
                   "for any routers deployed with this agent backend.")
            LOG.warning(msg)
        self.process_monitor = external_process.ProcessMonitor(
            config=agent.conf,
            resource_type='router')
        self.metadata_driver = None
        if agent.conf.enable_metadata_proxy:  # TODO(igordc): not implemented?
            self.metadata_driver = metadata_driver.MetadataDriver(agent)
        self.interface_driver = mock_utils.OVSInterfaceDriver(agent.conf)
        # FIXME(igordc): drivers have moved to RouterInfo, should this be here?
        # FIXME(igordc): what metadata driver was used before?
        self.dvr_bridge_manager = mock_utils.NameSpaceManagerMock(
            agent.conf,
            self.interface_driver,
            self.metadata_driver)

    def process_prefix_update(self):
        pass

    def after_start(self):
        pass

    def process_ha_state(self, router_id, state):
        pass

    def spawn_monitored_metadata_proxy(self, router_info):
        pass

    def get_router_pd(self, router_id):
        pass

    def set_router_pd(self, router_id, router):
        pass

    def sync_router_pd(self, router_id):
        pass

    def delete_router_pd(self, router_id):
        pass

    def destroy_monitored_metadata_proxy(self, router_info):
        pass

    def ensure_router_cleanup(self, router_id):
        pass

    def check_ha_router_status(self, context):
        pass

    def update_metadata_rules(self, router_info):
        # TODO(igordc): figure out what was done before OVSBackend existed
        pass

    @property
    def routers_manager(self):
        return self.dvr_bridge_manager
