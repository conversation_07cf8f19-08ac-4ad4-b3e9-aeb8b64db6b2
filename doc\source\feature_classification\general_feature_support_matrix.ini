# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

[target.ovs]
label=networking-ovs
title=Open vSwitch
link=

[target.linuxbridge]
label=networking-linux-bridge
title=Linux Bridge
link=

[target.odl]
label=networking-odl
title=Networking ODL
link=https://docs.openstack.org/networking-odl/latest/

[target.midonet]
label=networking-midonet
title=Networking MidoNet
link=https://docs.openstack.org/networking-midonet/latest/

[target.ovn]
label=networking-ovn
title=Networking OVN
link=https://docs.openstack.org/networking-ovn/latest/

[operation.Networks]
title=Networks
status=required
api=core
cli=openstack network *
notes=The ability to create, modify and delete networks.
    https://developer.openstack.org/api-ref/networking/v2/#networks
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=complete
networking-ovn=complete

[operation.Subnets]
title=Subnets
status=required
api=core
cli=openstack subnet *
notes=The ability to create and manipulate subnets and subnet pools.
    https://developer.openstack.org/api-ref/networking/v2/#subnets
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=complete
networking-ovn=complete

[operation.Ports]
title=Ports
status=required
api=core
cli=openstack port *
notes=The ability to create and manipulate ports.
    https://developer.openstack.org/api-ref/networking/v2/#ports
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=complete
networking-ovn=complete

[operation.Router]
title=Routers
status=required
api=router
cli=openstack router *
notes=The ability to create and manipulate routers.
    https://developer.openstack.org/api-ref/networking/v2/#routers-routers
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=complete
networking-ovn=complete

[operation.Security_Groups]
title=Security Groups
status=mature
api=security-group
cli=openstack security group *
notes=Security groups are set by default, and can be modified to control
    ingress & egress traffic.
    https://developer.openstack.org/api-ref/networking/v2/#security-groups-security-groups
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=complete
networking-ovn=complete

[operation.External_Nets]
title=External Networks
status=mature
api=external-net
notes=The ability to create an external network to provide internet access
    to and from instances using floating IP addresses and security group rules.
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=complete
networking-ovn=complete

[operation.DVR]
title=Distributed Virtual Routers
status=immature
api=dvr
notes=The ability to support the distributed virtual routers.
    https://wiki.openstack.org/wiki/Neutron/DVR
networking-ovs=complete
networking-linux-bridge=incomplete
networking-odl=partial
networking-midonet=complete
networking-ovn=partial

[operation.L3_HA]
title=L3 High Availability
status=immature
api=l3-ha
notes=The ability to support the High Availability features and extensions.
    https://wiki.openstack.org/wiki/Neutron/L3_High_Availability_VRRP.
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=partial
networking-midonet=incomplete
networking-ovn=partial

[operation.QoS]
title=Quality of Service
status=mature
api=qos
notes=Support for Neutron Quality of Service policies and API.
    https://developer.openstack.org/api-ref/networking/v2/#qos-policies-qos
networking-ovs=complete
networking-linux-bridge=partial
networking-odl=partial
networking-midonet=complete
networking-ovn=complete

[operation.BGP]
title=Border Gateway Protocol
status=immature
notes=https://developer.openstack.org/api-ref/networking/v2/#bgp-mpls-vpn-interconnection
networking-ovs=complete
networking-linux-bridge=unknown
networking-odl=unknown
networking-midonet=complete
networking-ovn=unknown

[operation.DNS]
title=DNS
status=mature
api=dns-integration
notes=The ability to integrate with an external DNS
    as a Service. https://docs.openstack.org/neutron/latest/admin/config-dns-int.html
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=incomplete
networking-ovn=complete

[operation.Trunk_Ports]
title=Trunk Ports
status=mature
api=trunk
notes=Neutron extension to access lots of neutron networks over
    a single vNIC as tagged/encapsulated traffic.
    https://developer.openstack.org/api-ref/networking/v2/#trunk-networking
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=incomplete
networking-midonet=incomplete
networking-ovn=complete

[operation.Metering]
title=Metering
status=mature
api=metering
notes=Meter traffic at the L3 router levels.
    https://developer.openstack.org/api-ref/networking/v2/#metering-labels-and-rules-metering-labels-metering-label-rules
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=incomplete
networking-midonet=incomplete
networking-ovn=unknown

[operations.Routed_Provider_Networks]
title=Routed Provider Networks
status=immature
notes=The ability to present a multi-segment layer-3 network as a
    single entity. https://docs.openstack.org/neutron/latest/admin/config-routed-networks.html
networking-ovs=partial
networking-linux-bridge=partial
networking-odl=incomplete
networking-midonet=incomplete
networking-ovn=partial
