# Copyright 2023 Acronis
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import netaddr

from neutron_lib.agent import topics
from neutron_lib import exceptions as n_exc
from neutron_lib.utils import helpers
from oslo_config import cfg

from neutron._i18n import _
from neutron.agent.common import ovs_lib as ovs_lib
from neutron.agent.common import utils
from neutron.agent import rpc as agent_rpc
from neutron.objects import ports as port_obj


class FlowTracer(object):
    def _run_trace(self, bridge, in_port, protocol,
                   ip_src, ip_dst, mac_src, mac_dst, dl_vlan=None, **kwargs):
        port_src = kwargs.get('port_src', 57120)
        port_dst = kwargs.get('port_dst', 8000)

        if netaddr.IPNetwork(ip_src).version == 6:
            if not protocol.endswith('6'):
                raise n_exc.InvalidInput(
                    error_message=_("IP version and protocol mismatch"))
            address = (('ipv6_src=%(ipv6_src)s,ipv6_dst=%(ipv6_dst)s,'
                        'dl_src=%(dl_src)s,dl_dst=%(dl_dst)s') %
                       {'ipv6_src': ip_src, 'ipv6_dst': ip_dst,
                        'dl_src': mac_src, 'dl_dst': mac_dst})
            if protocol == 'icmp6':
                address += ',icmpv6_type=128'
            if protocol == 'tcp6':
                address += ',tcp_src=%s,tcp_dst=%s' % (port_src, port_dst)
            if protocol == 'udp6':
                address += ',udp_src=%s,udp_dst=%s' % (port_src, port_dst)
        else:
            if protocol == 'arp':
                address = ('arp_sha=%(sha)s,arp_spa=%(spa)s,'
                           'arp_tpa=%(tpa)s,arp_tha=%(tha)s,'
                           'dl_src=%(src)s,dl_dst=%(dst)s' %
                           {'sha': mac_src, 'spa': ip_src,
                            'tha': mac_dst, 'tpa': ip_dst,
                            'src': mac_src, 'dst': mac_dst})
                address += ',arp_op=%s' % kwargs.get('arp_op', 1)
            else:
                address = (('dl_src=%(dl_src)s,dl_dst=%(dl_dst)s,'
                            'nw_src=%(nw_src)s,nw_dst=%(nw_dst)s') %
                           {'dl_src': mac_src, 'dl_dst': mac_dst,
                            'nw_src': ip_src, 'nw_dst': ip_dst})
                if protocol == 'tcp':
                    address += ',tcp_src=%s,tcp_dst=%s' % (port_src, port_dst)
                if protocol == 'udp':
                    address += ',udp_src=%s,udp_dst=%s' % (port_src, port_dst)

        trace = ('in_port=%(ofport)s,%(proto)s,%(address)s' %
                 {'ofport': in_port, 'proto': protocol, 'address': address})
        if protocol != 'arp':
            trace += ',nw_ttl=64'
        if dl_vlan:
            trace += ',dl_vlan=%s' % dl_vlan
        cmd = ['ovs-appctl', 'ofproto/trace', bridge, trace]
        output = utils.execute(cmd, run_as_root=True)
        return output

    def _flow_trace(self, bridge, ofport, protocol, direction, dl_vlan=None,
                    **kwargs):
        result = {}
        ip_src = kwargs.pop('ip_src')
        ip_dst = kwargs.pop('ip_dst')
        mac_src = kwargs.pop('mac_src')
        mac_dst = kwargs.pop('mac_dst')

        if direction == 'egress':
            output = self._run_trace(bridge, ofport, protocol,
                                     ip_src, ip_dst, mac_src, mac_dst,
                                     **kwargs)
        else:
            output = self._run_trace(bridge, ofport, protocol,
                                     ip_src, ip_dst, mac_src, mac_dst,
                                     dl_vlan, **kwargs)
        result['trace'] = output
        return result

    def flow_trace(self, context, port, **kwargs):
        port = port_obj.Port.obj_from_primitive(port)

        int_bridge = cfg.CONF.ovs_integration_bridge
        br_int = ovs_lib.OVSBridge(int_bridge)
        port_name = utils.get_port_name_by_id(br_int, port.id)
        if not port_name:
            raise n_exc.PortNotFound(port_id=port.id)
        port_ofport = br_int.get_port_ofport(port_name)

        # 'ip,arp,tcp,udp,icmp,tcp6,udp6,icmp6'
        protocol = kwargs.pop('protocol', 'ip')
        direction = kwargs.pop('direction', 'egress')
        if direction == 'egress':
            result = self._flow_trace(
                br_int.br_name, port_ofport, protocol, direction, **kwargs)
        else:
            phy_ofport = kwargs.pop('physical_dev', None)
            dl_vlan = kwargs.pop('dl_vlan', None)

            # get physical network bridge and network segment id
            ovs_plugin_rpc = agent_rpc.CacheBackedPluginApi(topics.PLUGIN)
            p_info = ovs_plugin_rpc.get_device_details(
                context, port['id'], None, host=cfg.CONF.host)
            port_physical_network = p_info['physical_network']
            bridge_mappings = helpers.parse_mappings(
                cfg.CONF.OVS.bridge_mappings, unique_values=False)
            bridge = bridge_mappings.get(port_physical_network)
            if not dl_vlan:
                dl_vlan = p_info['segmentation_id']

            result = self._flow_trace(
                bridge, phy_ofport, protocol, direction, dl_vlan, **kwargs)
        return result
