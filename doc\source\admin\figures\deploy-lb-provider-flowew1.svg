<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -6 320 459" width="320pt" height="459pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:02:59 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><text transform="translate(53.307085 8.8503935)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".21875" y="17" textLength="122.64844">Linux Bridge - Pr</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="122.546875" y="17" textLength="9.6640625">o</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="131.890625" y="17" textLength="112.890625">vider Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="2.2041016" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="59.157227" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="63.81543" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="81.674805" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="106.74121" y="35" textLength="58.253906">w - East/W</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="164.75488" y="35" textLength="78.041016">est Scenario 1</tspan></text><circle cx="155.90551" cy="425.19685" r="8.5039505" fill="#738a05"/><text transform="translate(169.40945 413.18897)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="72.700195">vider network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="94.91406">VLAN 101, 203.0.113.0/24</tspan></text><path d="M 36.346457 56.692913 L 317.98425 56.692913 C 322.40253 56.692913 325.98425 60.274635 325.98425 64.692913 L 325.98425 145.070865 C 325.98425 149.48914 322.40253 153.070865 317.98425 153.070865 L 36.346457 153.070865 C 31.928179 153.070865 28.346457 149.48914 28.346457 145.070865 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="96.24663" y="13" textLength="95.14453">Compute Node 1</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.6003628" y="9" textLength="39.492188">Instance 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" fill="#fdf5dd"/><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="127.559054" x2="155.90551" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="127.559054" x2="198.4252" y2="127.559054" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="127.559054" x2="240.94488" y2="127.559054" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><text transform="translate(104.88583 127.559054)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".38476562" y="9" textLength="16.230469">veth</tspan></text><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#738a05"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" fill="#738a05"/><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" fill="#738a05"/><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><line x1="240.94488" y1="127.559054" x2="297.6378" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" fill="#738a05"/><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><text transform="translate(197.9835 195.17492) rotate(-20.869346)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text><path d="M 36.346457 297.6378 L 317.98425 297.6378 C 322.40253 297.6378 325.98425 301.21952 325.98425 305.6378 L 325.98425 386.01575 C 325.98425 390.43402 322.40253 394.01575 317.98425 394.01575 L 36.346457 394.01575 C 31.928179 394.01575 28.346457 390.43402 28.346457 386.01575 L 28.346457 305.6378 C 28.346457 301.21952 31.928179 297.6378 36.346457 297.6378 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 302.6378)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="96.24663" y="13" textLength="95.14453">Compute Node 2</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 325.98425 L 91.2126 325.98425 C 95.630876 325.98425 99.2126 329.56597 99.2126 333.98425 L 99.2126 380.34645 C 99.2126 384.76473 95.630876 388.34645 91.2126 388.34645 L 50.519685 388.34645 C 46.101407 388.34645 42.519685 384.76473 42.519685 380.34645 L 42.519685 333.98425 C 42.519685 329.56597 46.101407 325.98425 50.519685 325.98425 Z" fill="#fdf5dd"/><path d="M 50.519685 325.98425 L 91.2126 325.98425 C 95.630876 325.98425 99.2126 329.56597 99.2126 333.98425 L 99.2126 380.34645 C 99.2126 384.76473 95.630876 388.34645 91.2126 388.34645 L 50.519685 388.34645 C 46.101407 388.34645 42.519685 384.76473 42.519685 380.34645 L 42.519685 333.98425 C 42.519685 329.56597 46.101407 325.98425 50.519685 325.98425 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 330.98425)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.6003628" y="9" textLength="39.492188">Instance 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 325.98425 L 261.29134 325.98425 C 265.70962 325.98425 269.29134 329.56597 269.29134 333.98425 L 269.29134 380.34645 C 269.29134 384.76473 265.70962 388.34645 261.29134 388.34645 L 135.559054 388.34645 C 131.14078 388.34645 127.559054 384.76473 127.559054 380.34645 L 127.559054 333.98425 C 127.559054 329.56597 131.14078 325.98425 135.559054 325.98425 Z" fill="#fdf5dd"/><path d="M 135.559054 325.98425 L 261.29134 325.98425 C 265.70962 325.98425 269.29134 329.56597 269.29134 333.98425 L 269.29134 380.34645 C 269.29134 384.76473 265.70962 388.34645 261.29134 388.34645 L 135.559054 388.34645 C 131.14078 388.34645 127.559054 384.76473 127.559054 380.34645 L 127.559054 333.98425 C 127.559054 329.56597 131.14078 325.98425 135.559054 325.98425 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 330.98425)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="368.50393" x2="155.90551" y2="368.50393" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="368.50393" x2="198.4252" y2="368.50393" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="368.50393" x2="240.94488" y2="368.50393" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><text transform="translate(104.88583 368.50393)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".38476562" y="9" textLength="16.230469">veth</tspan></text><g filter="url(#Shadow)"><path d="M 64.692913 354.3307 L 77.03937 354.3307 C 81.45765 354.3307 85.03937 357.91243 85.03937 362.3307 L 85.03937 374.67716 C 85.03937 379.09544 81.45765 382.67716 77.03937 382.67716 L 64.692913 382.67716 C 60.274635 382.67716 56.692913 379.09544 56.692913 374.67716 L 56.692913 362.3307 C 56.692913 357.91243 60.274635 354.3307 64.692913 354.3307 Z" fill="#738a05"/><path d="M 64.692913 354.3307 L 77.03937 354.3307 C 81.45765 354.3307 85.03937 357.91243 85.03937 362.3307 L 85.03937 374.67716 C 85.03937 379.09544 81.45765 382.67716 77.03937 382.67716 L 64.692913 382.67716 C 60.274635 382.67716 56.692913 379.09544 56.692913 374.67716 L 56.692913 362.3307 C 56.692913 357.91243 60.274635 354.3307 64.692913 354.3307 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 363.00393)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 370.02642 C 177.34252 368.50393 179.90022 355.68708 190.13182 357.87401 C 191.08108 353.611 202.97905 354.30293 202.90127 357.87401 C 210.36166 353.30655 219.8956 362.41398 213.50075 366.98145 C 221.17425 369.19587 213.40392 381.1269 207.1063 379.13386 C 206.6023 382.45578 195.34405 383.61827 194.35589 379.13386 C 187.98089 383.923 174.68799 376.55943 183.75641 370.02642 Z" fill="#738a05"/><path d="M 183.75641 370.02642 C 177.34252 368.50393 179.90022 355.68708 190.13182 357.87401 C 191.08108 353.611 202.97905 354.30293 202.90127 357.87401 C 210.36166 353.30655 219.8956 362.41398 213.50075 366.98145 C 221.17425 369.19587 213.40392 381.1269 207.1063 379.13386 C 206.6023 382.45578 195.34405 383.61827 194.35589 379.13386 C 187.98089 383.923 174.68799 376.55943 183.75641 370.02642 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 363.00393)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7882012" y="9" textLength="14.203125">(10)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 354.3307 L 162.07874 354.3307 C 166.49702 354.3307 170.07874 357.91243 170.07874 362.3307 L 170.07874 374.67716 C 170.07874 379.09544 166.49702 382.67716 162.07874 382.67716 L 149.73228 382.67716 C 145.314005 382.67716 141.73228 379.09544 141.73228 374.67716 L 141.73228 362.3307 C 141.73228 357.91243 145.314005 354.3307 149.73228 354.3307 Z" fill="#738a05"/><path d="M 149.73228 354.3307 L 162.07874 354.3307 C 166.49702 354.3307 170.07874 357.91243 170.07874 362.3307 L 170.07874 374.67716 C 170.07874 379.09544 166.49702 382.67716 162.07874 382.67716 L 149.73228 382.67716 C 145.314005 382.67716 141.73228 379.09544 141.73228 374.67716 L 141.73228 362.3307 C 141.73228 357.91243 145.314005 354.3307 149.73228 354.3307 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 363.00393)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><line x1="240.94488" y1="368.50393" x2="297.6378" y2="368.50393" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 354.3307 L 247.11811 354.3307 C 251.53639 354.3307 255.11811 357.91243 255.11811 362.3307 L 255.11811 374.67716 C 255.11811 379.09544 251.53639 382.67716 247.11811 382.67716 L 234.77165 382.67716 C 230.35337 382.67716 226.77165 379.09544 226.77165 374.67716 L 226.77165 362.3307 C 226.77165 357.91243 230.35337 354.3307 234.77165 354.3307 Z" fill="#738a05"/><path d="M 234.77165 354.3307 L 247.11811 354.3307 C 251.53639 354.3307 255.11811 357.91243 255.11811 362.3307 L 255.11811 374.67716 C 255.11811 379.09544 251.53639 382.67716 247.11811 382.67716 L 234.77165 382.67716 C 230.35337 382.67716 226.77165 379.09544 226.77165 374.67716 L 226.77165 362.3307 C 226.77165 357.91243 230.35337 354.3307 234.77165 354.3307 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 363.00393)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><text transform="translate(180.16118 264.89117) rotate(21.496949)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text><path d="M 36.346457 170.07874 L 147.90551 170.07874 C 152.32379 170.07874 155.90551 173.66046 155.90551 178.07874 L 155.90551 272.62992 C 155.90551 277.0482 152.32379 280.62992 147.90551 280.62992 L 36.346457 280.62992 C 31.928179 280.62992 28.346457 277.0482 28.346457 272.62992 L 28.346457 178.07874 C 28.346457 173.66046 31.928179 170.07874 36.346457 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="10.914293" y="13" textLength="98.847656">Physical Network </tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="20.259996" y="30" textLength="19.675781">Infr</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="39.695543" y="30" textLength="51.111328">astructur</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="90.566637" y="30" textLength="6.732422">e</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 212.59842 L 133.73228 212.59842 C 138.15056 212.59842 141.73228 216.18015 141.73228 220.59842 L 141.73228 266.96063 C 141.73228 271.3789 138.15056 274.96063 133.73228 274.96063 L 50.519685 274.96063 C 46.101407 274.96063 42.519685 271.3789 42.519685 266.96063 L 42.519685 220.59842 C 42.519685 216.18015 46.101407 212.59842 50.519685 212.59842 Z" fill="#fdf5dd"/><path d="M 50.519685 212.59842 L 133.73228 212.59842 C 138.15056 212.59842 141.73228 216.18015 141.73228 220.59842 L 141.73228 266.96063 C 141.73228 271.3789 138.15056 274.96063 133.73228 274.96063 L 50.519685 274.96063 C 46.101407 274.96063 42.519685 271.3789 42.519685 266.96063 L 42.519685 220.59842 C 42.519685 216.18015 46.101407 212.59842 50.519685 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 217.59842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="31.93247" y="9" textLength="25.347656">Switch</tspan></text></g><path d="M 292.45814 141.73229 C 287.01922 148.8548 276.24465 158.45914 255.11811 170.07874 C 206.93395 196.58003 179.75856 197.01028 127.559054 221.10236 C 75.35955 245.19444 70.86614 255.11811 70.86614 255.11811" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 290.87697 354.35196 C 281.48442 337.13644 261.58612 307.7235 226.77165 286.29921 C 174.57215 254.17644 113.385826 255.11811 113.385826 255.11811" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 291.46457 354.3307 L 303.81102 354.3307 C 308.2293 354.3307 311.81102 357.91243 311.81102 362.3307 L 311.81102 374.67716 C 311.81102 379.09544 308.2293 382.67716 303.81102 382.67716 L 291.46457 382.67716 C 287.04629 382.67716 283.46457 379.09544 283.46457 374.67716 L 283.46457 362.3307 C 283.46457 357.91243 287.04629 354.3307 291.46457 354.3307 Z" fill="#bd3612"/><path d="M 291.46457 354.3307 L 303.81102 354.3307 C 308.2293 354.3307 311.81102 357.91243 311.81102 362.3307 L 311.81102 374.67716 C 311.81102 379.09544 308.2293 382.67716 303.81102 382.67716 L 291.46457 382.67716 C 287.04629 382.67716 283.46457 379.09544 283.46457 374.67716 L 283.46457 362.3307 C 283.46457 357.91243 287.04629 354.3307 291.46457 354.3307 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 363.00393)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><line x1="113.385826" y1="255.11811" x2="70.86614" y2="255.11811" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 107.2126 240.94488 L 119.559054 240.94488 C 123.97733 240.94488 127.559054 244.5266 127.559054 248.94488 L 127.559054 261.29134 C 127.559054 265.70962 123.97733 269.29134 119.559054 269.29134 L 107.2126 269.29134 C 102.79432 269.29134 99.2126 265.70962 99.2126 261.29134 L 99.2126 248.94488 C 99.2126 244.5266 102.79432 240.94488 107.2126 240.94488 Z" fill="#bd3612"/><path d="M 107.2126 240.94488 L 119.559054 240.94488 C 123.97733 240.94488 127.559054 244.5266 127.559054 248.94488 L 127.559054 261.29134 C 127.559054 265.70962 123.97733 269.29134 119.559054 269.29134 L 107.2126 269.29134 C 102.79432 269.29134 99.2126 265.70962 99.2126 261.29134 L 99.2126 248.94488 C 99.2126 244.5266 102.79432 240.94488 107.2126 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 249.61811)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 240.94488 L 77.03937 240.94488 C 81.45765 240.94488 85.03937 244.5266 85.03937 248.94488 L 85.03937 261.29134 C 85.03937 265.70962 81.45765 269.29134 77.03937 269.29134 L 64.692913 269.29134 C 60.274635 269.29134 56.692913 265.70962 56.692913 261.29134 L 56.692913 248.94488 C 56.692913 244.5266 60.274635 240.94488 64.692913 240.94488 Z" fill="#bd3612"/><path d="M 64.692913 240.94488 L 77.03937 240.94488 C 81.45765 240.94488 85.03937 244.5266 85.03937 248.94488 L 85.03937 261.29134 C 85.03937 265.70962 81.45765 269.29134 77.03937 269.29134 L 64.692913 269.29134 C 60.274635 269.29134 56.692913 265.70962 56.692913 261.29134 L 56.692913 248.94488 C 56.692913 244.5266 60.274635 240.94488 64.692913 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 249.61811)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" fill="#bd3612"/><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><circle cx="42.519685" cy="425.19685" r="8.5039505" fill="#bd3612"/><text transform="translate(55.732283 412.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text></g></g></svg>
