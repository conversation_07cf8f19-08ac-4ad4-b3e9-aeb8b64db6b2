#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
import netaddr
from neutron_lib import constants as lib_constants
from neutron_lib import context
from oslo_utils import uuidutils

from neutron.agent.l3 import agent as l3_agent
from neutron.agent.l3.extensions import route_table
from neutron.agent.l3 import l3_agent_extension_api as l3_ext_api
from neutron.agent.l3 import router_info as l3router
from neutron.tests.unit.agent.l3 import test_agent


_uuid = uuidutils.generate_uuid

HOSTNAME = 'testhost'


class RouteTableAgentExtensionBase(
    test_agent.BasicRouterOperationsFramework):

    def setUp(self):
        super(RouteTableAgentExtensionBase, self).setUp()

        self.route_table_ext = route_table.RouteTableAgentExtension()
        self.ctx = context.get_admin_context()
        self.connection = mock.Mock()
        self.agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)

        self.router_id = _uuid()
        self.gateway_port_id = _uuid()
        self.ex_gw_port = {'id': self.gateway_port_id,
                           'fixed_ips': [{'ip_address': '************',
                                          'visible': True,
                                          'prefixlen': 24}],
                           'subnets': [{
                               "gateway_ip": "**********",
                               "cidr": "**********/24"}]}
        self._interfaces = [
            {"id": _uuid(),
             "subnets": [{"gateway_ip": "*********",
                          "cidr": "*********/24"}],
             "fixed_ips": [{"visible": True,
                            "prefixlen": 24,
                            "ip_address": "*********0"}]},
            {"id": _uuid(),
             "subnets": [{"gateway_ip": "*********",
                          "cidr": "*********/24"}],
             "fixed_ips": [{"visible": True,
                            "prefixlen": 24,
                            "ip_address": "*********"}]}]

        self.route_table = {'id': _uuid(),
                            'name': 'rt',
                            'router_id': self.router_id,
                            'table_id': 201}

        self.default_route_table = {'id': _uuid(),
                            'name': 'rt',
                            'router_id': self.router_id,
                            'table_id': 200}

        self.route1 = {'type': 'ecs',
                       'destination': '********/24',
                       'nexthop': '*********',
                       'routetable_id': self.route_table['id']}
        self.route2 = {'type': 'ecs',
                       'destination': '********/24',
                       'nexthop': '*********',
                       'routetable_id': self.route_table['id']}
        self.bindings = [{'subnet_id': _uuid(),
                          'routetable_id': self.route_table['id'],
                          'cidr': '*********/24'}]

        self.router = {'id': self.router_id,
                       'gw_port': self.ex_gw_port,
                       '_interfaces': self._interfaces,
                       'ha': False,
                       'distributed': False,
                       'route_tables': {
                           self.route_table['id']:
                               self.route_table['table_id'],
                           self.default_route_table['id']:
                               self.default_route_table['table_id']},
                       'default_route_table': self.default_route_table['id'],
                       'route_table_routes': [self.route1, self.route2],
                       'route_table_subnet_bindings': self.bindings}
        self.router_info = l3router.RouterInfo(
            self.agent, self.router_id, self.router,
            **self.ri_kwargs)
        self.router_info.ex_gw_port = self.ex_gw_port
        self.router_info.internal_ports = self._interfaces
        self.agent.router_info[self.router['id']] = self.router_info

        def _get_router_info(router_id):
            return self.agent.router_info.get(router_id)

        self.get_router_info = mock.patch(
            'neutron.agent.l3.l3_agent_extension_api.'
            'L3AgentExtensionAPI.get_router_info').start()
        self.get_router_info.side_effect = _get_router_info

        self.agent_api = l3_ext_api.L3AgentExtensionAPI(None, None)
        self.route_table_ext.consume_api(self.agent_api)


class RouteTableAgentExtensionTestCase(RouteTableAgentExtensionBase):

    def setUp(self):
        super(RouteTableAgentExtensionTestCase, self).setUp()
        self.route_table_ext.initialize(
            self.connection, lib_constants.L3_AGENT_MODE)

    def _check_agent_method_called(self, calls):
        self.mock_ip.netns.execute.assert_has_calls(
            [mock.call(call, log_fail_as_error=False) for call in calls],
            any_order=True)

    def test_add_update_router(self):
        self.route_table_ext.add_router(self.ctx, self.router)
        expected = [['ip', 'route', 'replace', 'to', '********/24',
                     'via', '*********', 'table', 201],
                    ['ip', 'route', 'replace', 'to', '********/24',
                     'via', '*********', 'table', 201]]
        self._check_agent_method_called(expected)
        # default route table binging rules
        expected_rules = [
            mock.call(ip='*********/24', table=200, priority=500),
            mock.call(ip='*********/24', table=200, priority=500)]
        self.mock_rule.rule.add.assert_has_calls(
            expected_rules, any_order=True)
        # custom route table binging rules
        self.mock_rule.rule.delete.assert_has_calls(
            [mock.call(ip='*********/24', table=200, priority=500)])
        self.mock_rule.rule.add.assert_has_calls(
            [mock.call(ip='*********/24', table=201, priority=100)])

        router = {'id': self.router_id,
                  'gw_port': self.ex_gw_port,
                  '_interfaces': self._interfaces,
                  'ha': False,
                  'distributed': False,
                  'route_tables': {
                      self.route_table['id']:
                          self.route_table['table_id'],
                      self.default_route_table['id']:
                          self.default_route_table['table_id']},
                  'route_table_routes': [self.route1],
                  'default_route_table': self.default_route_table['id'],
                  'route_table_subnet_bindings': []}
        router_info = l3router.RouterInfo(
            self.agent, self.router_id, router,
            **self.ri_kwargs)
        self.agent.router_info[self.router_id] = router_info
        self.route_table_ext.update_router(self.ctx, router)
        expected = [['ip', 'route', 'delete', 'to', '********/24',
                     'via', '*********', 'table', 201]]
        self._check_agent_method_called(expected)

        self.mock_rule.rule.delete.assert_has_calls(
            [mock.call(ip='*********/24', table=201, priority=100)])

    def test_process_external_port_routes(self):
        self.route_table_ext.add_router(self.ctx, self.router)
        expected = [netaddr.IPNetwork('**********/24'), ]
        self.mock_ip_dev.route.add_route.assert_has_calls(
            [mock.call(call, proto='kernel', scope='link', src='************',
                       table=200) for call in expected], any_order=True)

        expected = ['**********', ]
        self.mock_ip_dev.route.add_gateway.assert_has_calls(
            [mock.call(call, table=200) for call in expected],
            any_order=True)

    def test_process_internal_port_routes(self):
        self.route_table_ext.add_router(self.ctx, self.router)
        expected = [{'ip': '*********0',
                     'cidr': netaddr.IPNetwork('*********/24')},
                    {'ip': '*********',
                     'cidr': netaddr.IPNetwork('*********/24')}]

        self.mock_ip_dev.route.add_route.assert_has_calls(
            [mock.call(call['cidr'], proto='kernel', scope='link',
                       src=call['ip'], table=200)
             for call in expected], any_order=True)

    def test_remove_route_table(self):
        self.route_table_ext.add_router(self.ctx, self.router)
        expected = [['ip', 'route', 'replace', 'to', '********/24',
                     'via', '*********', 'table', 201],
                    ['ip', 'route', 'replace', 'to', '********/24',
                     'via', '*********', 'table', 201]]
        self._check_agent_method_called(expected)

        router = {'id': self.router_id,
                  'gw_port': self.ex_gw_port,
                  '_interfaces': self._interfaces,
                  'ha': False,
                  'distributed': False,
                  'route_tables': {
                      self.default_route_table['id']:
                          self.default_route_table['table_id']},
                  'route_table_routes': [],
                  'default_route_table': self.default_route_table['id'],
                  'route_table_subnet_bindings': []}
        router_info = l3router.RouterInfo(
            self.agent, self.router_id, router,
            **self.ri_kwargs)
        self.agent.router_info[self.router_id] = router_info
        self.route_table_ext.update_router(self.ctx, router)

        expected = [['ip', 'route', 'flush', 'table', 201]]
        self._check_agent_method_called(expected)

        expected = [['ip', 'route', 'delete', 'to', '********/24',
                     'via', '*********', 'table', 201],
                    ['ip', 'route', 'delete', 'to', '********/24',
                     'via', '*********', 'table', 201]]
        self._check_agent_method_called(expected)

        self.mock_rule.rule.delete.assert_has_calls(
            [mock.call(ip='*********/24', table=201, priority=100)])
