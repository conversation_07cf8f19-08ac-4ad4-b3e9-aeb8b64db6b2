.. _config:

=============
Configuration
=============

.. toctree::
   :maxdepth: 2

   config-services-agent
   config-ml2
   config-address-scopes
   config-auto-allocation
   config-az
   config-bgp-dynamic-routing
   config-dhcp-ha
   config-dns-int
   config-dns-int-ext-serv
   config-dns-res
   config-dvr-ha-snat
   config-fip-port-forwardings
   config-ipam
   config-ipv6
   config-lbaas
   config-logging
   config-macvtap
   config-mtu
   config-network-segment-ranges
   config-ovs-dpdk
   config-ovs-offload
   config-ovsfwdriver
   config-qos
   config-rbac
   config-routed-networks
   config-sfc
   config-sriov
   config-subnet-pools
   config-service-subnets
   config-trunking
   config-wsgi

.. note::

   For general configuration, see the `Configuration Reference
   <../configuration/>`_.
