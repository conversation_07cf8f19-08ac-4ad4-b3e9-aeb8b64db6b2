<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 369 444" width="369pt" height="37pc" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:00:08 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><text transform="translate(64.52756 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".4140625" y="17" textLength="266.17188">Linux Bridge - Self-service Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="5.841797" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="62.794922" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="67.453125" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="85.3125" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="110.378906" y="35" textLength="150.7793">w - North/South Scenario 2</tspan></text><path d="M 36.346457 56.692913 L 317.98425 56.692913 C 322.40253 56.692913 325.98425 60.274635 325.98425 64.692913 L 325.98425 145.070865 C 325.98425 149.48914 322.40253 153.070865 317.98425 153.070865 L 36.346457 153.070865 C 31.928179 153.070865 28.346457 149.48914 28.346457 145.070865 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="101.23589" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" fill="#fdf5dd"/><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="127.559054" x2="155.90551" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="127.559054" x2="198.4252" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="127.559054" x2="240.94488" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(15)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" fill="#2076c8"/><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7882012" y="9" textLength="14.203125">(13)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" fill="#2076c8"/><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(14)</tspan></text></g><line x1="240.94488" y1="127.559054" x2="297.6378" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" fill="#2076c8"/><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><path d="M 36.346457 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 343.49606 C 283.46457 347.91434 279.88284 351.49606 275.46457 351.49606 L 36.346457 351.49606 C 31.928179 351.49606 28.346457 347.91434 28.346457 343.49606 L 28.346457 178.07874 C 28.346457 173.66046 31.928179 170.07874 36.346457 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="81.91257" y="13" textLength="81.29297">Network Node</tspan></text><g filter="url(#Shadow)"><path d="M 135.559054 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 252.7874 C 226.77165 257.20568 223.18993 260.7874 218.77165 260.7874 L 135.559054 260.7874 C 131.14078 260.7874 127.559054 257.20568 127.559054 252.7874 L 127.559054 206.4252 C 127.559054 202.00692 131.14078 198.4252 135.559054 198.4252 Z" fill="#fdf5dd"/><path d="M 135.559054 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 252.7874 C 226.77165 257.20568 223.18993 260.7874 218.77165 260.7874 L 135.559054 260.7874 C 131.14078 260.7874 127.559054 257.20568 127.559054 252.7874 L 127.559054 206.4252 C 127.559054 202.00692 131.14078 198.4252 135.559054 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 203.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="20.50083" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.806006" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.045264" y="18" textLength="4.361328">q</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 283.46457 L 218.77165 283.46457 C 223.18993 283.46457 226.77165 287.04629 226.77165 291.46457 L 226.77165 337.82677 C 226.77165 342.24505 223.18993 345.82677 218.77165 345.82677 L 135.559054 345.82677 C 131.14078 345.82677 127.559054 342.24505 127.559054 337.82677 L 127.559054 291.46457 C 127.559054 287.04629 131.14078 283.46457 135.559054 283.46457 Z" fill="#fdf5dd"/><path d="M 135.559054 283.46457 L 218.77165 283.46457 C 223.18993 283.46457 226.77165 287.04629 226.77165 291.46457 L 226.77165 337.82677 C 226.77165 342.24505 223.18993 345.82677 218.77165 345.82677 L 135.559054 345.82677 C 131.14078 345.82677 127.559054 342.24505 127.559054 337.82677 L 127.559054 291.46457 C 127.559054 287.04629 131.14078 283.46457 135.559054 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 288.46457)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="20.50083" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.806006" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.045264" y="18" textLength="4.361328">q</tspan></text></g><g filter="url(#Shadow)"><path d="M 44.850392 198.4252 L 96.88189 198.4252 C 101.30017 198.4252 104.88189 202.00692 104.88189 206.4252 L 104.88189 309.48031 C 104.88189 313.8986 101.30017 317.48031 96.88189 317.48031 L 44.850392 317.48031 C 40.432114 317.48031 36.850392 313.8986 36.850392 309.48031 L 36.850392 206.4252 C 36.850392 202.00692 40.432114 198.4252 44.850392 198.4252 Z" fill="#fdf5dd"/><path d="M 44.850392 198.4252 L 96.88189 198.4252 C 101.30017 198.4252 104.88189 202.00692 104.88189 206.4252 L 104.88189 309.48031 C 104.88189 313.8986 101.30017 317.48031 96.88189 317.48031 L 44.850392 317.48031 C 40.432114 317.48031 36.850392 313.8986 36.850392 309.48031 L 36.850392 206.4252 C 36.850392 202.00692 40.432114 198.4252 44.850392 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(41.850392 203.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.875124" y="9" textLength="28.359375">Router </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.388796" y="20" textLength="45.253906">Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="16.135134" y="29" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="23.374392" y="29" textLength="18.521973">outer</tspan></text></g><line x1="155.90551" y1="240.94488" x2="70.86614" y2="252.28346" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="294.80315" x2="155.90551" y2="325.98425" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="198.4252" y1="325.98425" x2="255.11811" y2="325.98425" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="198.4252" y1="240.94488" x2="255.11811" y2="240.94488" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="255.11811" y1="240.94488" x2="354.3307" y2="198.4252" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="297.6378" y1="127.559054" x2="354.3307" y2="198.4252" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" fill="#a57706"/><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 226.77165 L 162.07874 226.77165 C 166.49702 226.77165 170.07874 230.35337 170.07874 234.77165 L 170.07874 247.11811 C 170.07874 251.53639 166.49702 255.11811 162.07874 255.11811 L 149.73228 255.11811 C 145.314005 255.11811 141.73228 251.53639 141.73228 247.11811 L 141.73228 234.77165 C 141.73228 230.35337 145.314005 226.77165 149.73228 226.77165 Z" fill="#2076c8"/><path d="M 149.73228 226.77165 L 162.07874 226.77165 C 166.49702 226.77165 170.07874 230.35337 170.07874 234.77165 L 170.07874 247.11811 C 170.07874 251.53639 166.49702 255.11811 162.07874 255.11811 L 149.73228 255.11811 C 145.314005 255.11811 141.73228 251.53639 141.73228 247.11811 L 141.73228 234.77165 C 141.73228 230.35337 145.314005 226.77165 149.73228 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 226.77165 L 204.59842 226.77165 C 209.0167 226.77165 212.59842 230.35337 212.59842 234.77165 L 212.59842 247.11811 C 212.59842 251.53639 209.0167 255.11811 204.59842 255.11811 L 192.25197 255.11811 C 187.83369 255.11811 184.25197 251.53639 184.25197 247.11811 L 184.25197 234.77165 C 184.25197 230.35337 187.83369 226.77165 192.25197 226.77165 Z" fill="#2076c8"/><path d="M 192.25197 226.77165 L 204.59842 226.77165 C 209.0167 226.77165 212.59842 230.35337 212.59842 234.77165 L 212.59842 247.11811 C 212.59842 251.53639 209.0167 255.11811 204.59842 255.11811 L 192.25197 255.11811 C 187.83369 255.11811 184.25197 251.53639 184.25197 247.11811 L 184.25197 234.77165 C 184.25197 230.35337 187.83369 226.77165 192.25197 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 311.81102 L 162.07874 311.81102 C 166.49702 311.81102 170.07874 315.39274 170.07874 319.81102 L 170.07874 332.15748 C 170.07874 336.57576 166.49702 340.15748 162.07874 340.15748 L 149.73228 340.15748 C 145.314005 340.15748 141.73228 336.57576 141.73228 332.15748 L 141.73228 319.81102 C 141.73228 315.39274 145.314005 311.81102 149.73228 311.81102 Z" fill="#738a05"/><path d="M 149.73228 311.81102 L 162.07874 311.81102 C 166.49702 311.81102 170.07874 315.39274 170.07874 319.81102 L 170.07874 332.15748 C 170.07874 336.57576 166.49702 340.15748 162.07874 340.15748 L 149.73228 340.15748 C 145.314005 340.15748 141.73228 336.57576 141.73228 332.15748 L 141.73228 319.81102 C 141.73228 315.39274 145.314005 311.81102 149.73228 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 320.48425)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 311.81102 L 204.59842 311.81102 C 209.0167 311.81102 212.59842 315.39274 212.59842 319.81102 L 212.59842 332.15748 C 212.59842 336.57576 209.0167 340.15748 204.59842 340.15748 L 192.25197 340.15748 C 187.83369 340.15748 184.25197 336.57576 184.25197 332.15748 L 184.25197 319.81102 C 184.25197 315.39274 187.83369 311.81102 192.25197 311.81102 Z" fill="#738a05"/><path d="M 192.25197 311.81102 L 204.59842 311.81102 C 209.0167 311.81102 212.59842 315.39274 212.59842 319.81102 L 212.59842 332.15748 C 212.59842 336.57576 209.0167 340.15748 204.59842 340.15748 L 192.25197 340.15748 C 187.83369 340.15748 184.25197 336.57576 184.25197 332.15748 L 184.25197 319.81102 C 184.25197 315.39274 187.83369 311.81102 192.25197 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 320.48425)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 238.11023 L 77.03937 238.11023 C 81.45765 238.11023 85.03937 241.69196 85.03937 246.11023 L 85.03937 258.45669 C 85.03937 262.87497 81.45765 266.45669 77.03937 266.45669 L 64.692913 266.45669 C 60.274635 266.45669 56.692913 262.87497 56.692913 258.45669 L 56.692913 246.11023 C 56.692913 241.69196 60.274635 238.11023 64.692913 238.11023 Z" fill="#2076c8"/><path d="M 64.692913 238.11023 L 77.03937 238.11023 C 81.45765 238.11023 85.03937 241.69196 85.03937 246.11023 L 85.03937 258.45669 C 85.03937 262.87497 81.45765 266.45669 77.03937 266.45669 L 64.692913 266.45669 C 60.274635 266.45669 56.692913 262.87497 56.692913 258.45669 L 56.692913 246.11023 C 56.692913 241.69196 60.274635 238.11023 64.692913 238.11023 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 246.78346)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 280.62992 L 77.03937 280.62992 C 81.45765 280.62992 85.03937 284.21164 85.03937 288.62992 L 85.03937 300.97638 C 85.03937 305.39465 81.45765 308.97638 77.03937 308.97638 L 64.692913 308.97638 C 60.274635 308.97638 56.692913 305.39465 56.692913 300.97638 L 56.692913 288.62992 C 56.692913 284.21164 60.274635 280.62992 64.692913 280.62992 Z" fill="#738a05"/><path d="M 64.692913 280.62992 L 77.03937 280.62992 C 81.45765 280.62992 85.03937 284.21164 85.03937 288.62992 L 85.03937 300.97638 C 85.03937 305.39465 81.45765 308.97638 77.03937 308.97638 L 64.692913 308.97638 C 60.274635 308.97638 56.692913 305.39465 56.692913 300.97638 L 56.692913 288.62992 C 56.692913 284.21164 60.274635 280.62992 64.692913 280.62992 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 289.30315)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 226.77165 L 261.29134 226.77165 C 265.70962 226.77165 269.29134 230.35337 269.29134 234.77165 L 269.29134 247.11811 C 269.29134 251.53639 265.70962 255.11811 261.29134 255.11811 L 248.94488 255.11811 C 244.5266 255.11811 240.94488 251.53639 240.94488 247.11811 L 240.94488 234.77165 C 240.94488 230.35337 244.5266 226.77165 248.94488 226.77165 Z" fill="#a57706"/><path d="M 248.94488 226.77165 L 261.29134 226.77165 C 265.70962 226.77165 269.29134 230.35337 269.29134 234.77165 L 269.29134 247.11811 C 269.29134 251.53639 265.70962 255.11811 261.29134 255.11811 L 248.94488 255.11811 C 244.5266 255.11811 240.94488 251.53639 240.94488 247.11811 L 240.94488 234.77165 C 240.94488 230.35337 244.5266 226.77165 248.94488 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><path d="M 339.66193 199.94768 C 333.24803 198.4252 335.80573 185.60835 346.03733 187.79527 C 346.9866 183.53225 358.88456 184.22419 358.80678 187.79527 C 366.26717 183.22781 375.8011 192.33524 369.40626 196.90271 C 377.07976 199.11713 369.30943 211.04816 363.0118 209.05512 C 362.5078 212.37704 351.24956 213.53953 350.2614 209.05512 C 343.8864 213.84425 330.5935 206.48069 339.66193 199.94768 Z" fill="#a57706"/><path d="M 339.66193 199.94768 C 333.24803 198.4252 335.80573 185.60835 346.03733 187.79527 C 346.9866 183.53225 358.88456 184.22419 358.80678 187.79527 C 366.26717 183.22781 375.8011 192.33524 369.40626 196.90271 C 377.07976 199.11713 369.30943 211.04816 363.0118 209.05512 C 362.5078 212.37704 351.24956 213.53953 350.2614 209.05512 C 343.8864 213.84425 330.5935 206.48069 339.66193 199.94768 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(345.44094 192.9252)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="9" textLength="14.203125">(10)</tspan></text><line x1="354.3307" y1="325.98425" x2="255.11811" y2="325.98425" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 311.81102 L 261.29134 311.81102 C 265.70962 311.81102 269.29134 315.39274 269.29134 319.81102 L 269.29134 332.15748 C 269.29134 336.57576 265.70962 340.15748 261.29134 340.15748 L 248.94488 340.15748 C 244.5266 340.15748 240.94488 336.57576 240.94488 332.15748 L 240.94488 319.81102 C 240.94488 315.39274 244.5266 311.81102 248.94488 311.81102 Z" fill="#bd3612"/><path d="M 248.94488 311.81102 L 261.29134 311.81102 C 265.70962 311.81102 269.29134 315.39274 269.29134 319.81102 L 269.29134 332.15748 C 269.29134 336.57576 265.70962 340.15748 261.29134 340.15748 L 248.94488 340.15748 C 244.5266 340.15748 240.94488 336.57576 240.94488 332.15748 L 240.94488 319.81102 C 240.94488 315.39274 244.5266 311.81102 248.94488 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 320.48425)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><path d="M 339.66193 327.50674 C 333.24803 325.98425 335.80573 313.1674 346.03733 315.35433 C 346.9866 311.0913 358.88456 311.78324 358.80678 315.35433 C 366.26717 310.78686 375.8011 319.8943 369.40626 324.46176 C 377.07976 326.67619 369.30943 338.60721 363.0118 336.61417 C 362.5078 339.9361 351.24956 341.09858 350.2614 336.61417 C 343.8864 341.4033 330.5935 334.03975 339.66193 327.50674 Z" fill="#bd3612"/><path d="M 339.66193 327.50674 C 333.24803 325.98425 335.80573 313.1674 346.03733 315.35433 C 346.9866 311.0913 358.88456 311.78324 358.80678 315.35433 C 366.26717 310.78686 375.8011 319.8943 369.40626 324.46176 C 377.07976 326.67619 369.30943 338.60721 363.0118 336.61417 C 362.5078 339.9361 351.24956 341.09858 350.2614 336.61417 C 343.8864 341.4033 330.5935 334.03975 339.66193 327.50674 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(345.44094 320.48425)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.071404" y="9" textLength="9.636719">(1)</tspan></text><text transform="translate(287.84475 325.98425)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text><text transform="translate(293.70647 224.40701) rotate(-23.19859)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan></text><text transform="translate(321.13139 156.92605) rotate(51.340192)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan></text><circle cx="184.25197" cy="411.02362" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 399.01575)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="93.63281">Self-service network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(197.7559 370.6693)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="184.25197" cy="382.67716" r="8.5039505" fill="#a57706"/><circle cx="42.519685" cy="411.02362" r="8.5039505" fill="#738a05"/><text transform="translate(56.023622 399.01575)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="94.91406">VLAN 101, 203.0.113.0/24</tspan></text><circle cx="42.519685" cy="382.67716" r="8.5039505" fill="#bd3612"/><text transform="translate(56.023622 370.6693)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text></g></g></svg>
