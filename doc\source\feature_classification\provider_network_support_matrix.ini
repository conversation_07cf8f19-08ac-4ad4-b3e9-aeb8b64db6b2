# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

[target.ovs]
label=networking-ovs
title=Open vSwitch
link=

[target.linuxbridge]
label=networking-linux-bridge
title=Linux Bridge
link=

[target.odl]
label=networking-odl
title=Networking ODL
link=https://docs.openstack.org/networking-odl/latest/

[target.midonet]
label=networking-midonet
title=Networking MidoNet
link=https://docs.openstack.org/networking-midonet/latest/

[target.ovn]
label=networking-ovn
title=Networking OVN
link=https://docs.openstack.org/networking-ovn/latest/

[operation.VLAN]
title=VLAN provider network support
status=mature
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=unknown
networking-midonet=incomplete
networking-ovn=complete

[operation.VXLAN]
title=VXLAN provider network support
status=mature
networking-ovs=complete
networking-linux-bridge=complete
networking-odl=complete
networking-midonet=incomplete
networking-ovn=incomplete

[operation.GRE]
title=GRE provider network support
status=immature
networking-ovs=complete
networking-linux-bridge=unknown
networking-odl=complete
networking-midonet=incomplete
networking-ovn=incomplete

[operation.Geneve]
title=Geneve provider network support
status=immature
networking-ovs=complete
networking-linux-bridge=unknown
networking-odl=incomplete
networking-midonet=incomplete
networking-ovn=complete
