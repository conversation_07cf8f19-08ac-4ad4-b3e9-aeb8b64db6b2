# Copyright (c) 2015 OpenStack Foundation.
#
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib import constants
from oslo_config import cfg

from neutron._i18n import _
from neutron.common import constants as n_const
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_consts


OPTS = [
    cfg.StrOpt('agent_mode', default=constants.L3_AGENT_MODE_LEGACY,
               choices=(constants.L3_AGENT_MODE_DVR,
                        n_const.L3_AGENT_MODE_DVR_BRIDGE,
                        constants.L3_AGENT_MODE_DVR_SNAT,
                        constants.L3_AGENT_MODE_LEGACY,
                        constants.L3_AGENT_MODE_DVR_NO_EXTERNAL),
               help=_("The working mode for the agent. Allowed modes are: "
                      "'legacy' - this preserves the existing behavior "
                      "where the L3 agent is deployed on a centralized "
                      "networking node to provide L3 services like DNAT, "
                      "and SNAT. Use this mode if you do not want to "
                      "adopt DVR. 'dvr' - this mode enables DVR "
                      "functionality and must be used for an L3 agent "
                      "that runs on a compute host. 'dvr_snat' - this "
                      "enables centralized SNAT support in conjunction "
                      "with DVR.  This mode must be used for an L3 agent "
                      "running on a centralized node (or in single-host "
                      "deployments, e.g. devstack). "
                      "'dvr_no_external' - this mode enables only East/West "
                      "DVR routing functionality for a L3 agent that runs on "
                      "a compute host, the North/South functionality such "
                      "as DNAT and SNAT will be provided by the centralized "
                      "network node that is running in 'dvr_snat' mode. "
                      "This mode should be used when there is no "
                      "external network connectivity on the compute host.")),
    cfg.StrOpt('agent_backend', default=n_const.L3_AGENT_BACKEND_LINUX,
               choices=(n_const.L3_AGENT_BACKEND_LINUX,
                        n_const.L3_AGENT_BACKEND_OVS),
               help=_("What technology the agent will use to deploy routers. "
                      "Allowed values are: 'linux' - which makes use of "
                      "Linux kernel technologies such as network namespaces "
                      "and the IP stack to instantiate virtual routers; and "
                      "'ovs' - which use OpenFlow rules in Open vSwitch to "
                      "implement the routers created in Neutron. The default "
                      "default backend is 'linux'.")),
    cfg.PortOpt('metadata_port',
                default=9697,
                help=_("TCP Port used by Neutron metadata namespace proxy.")),
    cfg.BoolOpt('handle_internal_only_routers',
                default=True,
                help=_("Indicates that this L3 agent should also handle "
                       "routers that do not have an external network gateway "
                       "configured. This option should be True only for a "
                       "single agent in a Neutron deployment, and may be "
                       "False for all agents if all routers must have an "
                       "external network gateway.")),
    cfg.StrOpt('ipv6_gateway', default='',
               help=_("With IPv6, the network used for the external gateway "
                      "does not need to have an associated subnet, since the "
                      "automatically assigned link-local address (LLA) can "
                      "be used. However, an IPv6 gateway address is needed "
                      "for use as the next-hop for the default route. "
                      "If no IPv6 gateway address is configured here, "
                      "(and only then) the neutron router will be configured "
                      "to get its default route from router advertisements "
                      "(RAs) from the upstream router; in which case the "
                      "upstream router must also be configured to send "
                      "these RAs. "
                      "The ipv6_gateway, when configured, should be the LLA "
                      "of the interface on the upstream router. If a "
                      "next-hop using a global unique address (GUA) is "
                      "desired, it needs to be done via a subnet allocated "
                      "to the network and not through this parameter. ")),
    cfg.StrOpt('prefix_delegation_driver',
               default='dibbler',
               help=_('Driver used for ipv6 prefix delegation. This needs to '
                      'be an entry point defined in the '
                      'neutron.agent.linux.pd_drivers namespace. See '
                      'setup.cfg for entry points included with the neutron '
                      'source.')),
    cfg.BoolOpt('enable_metadata_proxy', default=True,
                help=_("Allow running metadata proxy.")),
    cfg.StrOpt('metadata_access_mark',
               default='0x1',
               help=_('Iptables mangle mark used to mark metadata valid '
                      'requests. This mark will be masked with 0xffff so '
                      'that only the lower 16 bits will be used.')),
    cfg.StrOpt('external_ingress_mark',
               default='0x2',
               help=_('Iptables mangle mark used to mark ingress from '
                      'external network. This mark will be masked with '
                      '0xffff so that only the lower 16 bits will be used.')),
    cfg.StrOpt('radvd_user',
               default='',
               help=_('The username passed to radvd, used to drop root '
                      'privileges and change user ID to username and group ID '
                      'to the primary group of username. If no user specified '
                      '(by default), the user executing the L3 agent will be '
                      'passed. If "root" specified, because radvd is spawned '
                      'as root, no "username" parameter will be passed.')),
    cfg.BoolOpt('enable_nat6to6',
                default=False,
                help=_("Whether enable the IPv6 NAT.")),
    cfg.BoolOpt('enable_wo_fip_ad_ports',
                default=True,
                help=_("Whether enable Floating IP allowed/denied "
                       "port numbers function.")),
    cfg.BoolOpt('wo_enable_fip_admin_state_up',
                default=True,
                help=_("Whether enable Floating IP admin state up "
                       "function.")),
    cfg.BoolOpt('skip_process_if_no_compute_port', default=False,
                help=_('Whether skipping processing router when there is no '
                       'compute port under it.')),
    cfg.BoolOpt('process_delete_router_if_no_use', default=False,
                help=_('Whether delete router when there is no '
                       'compute port under it.')),
    cfg.BoolOpt('skip_process_if_no_centralized_res', default=False,
                help=_('Whether skipping processing router when there is no '
                       'centralized resources under it.')),
    cfg.BoolOpt('enable_l3_limit_report',
                default=False,
                help=_("enable_l3_limit_report.")),
    cfg.IntOpt('external_l3_bandwidth', default=2097152000,
               help=_('L3 agent has total bandwidth limits, which '
                      'default value is 2097152000, unit kbps.')),
    cfg.IntOpt('external_l3_packet_rate', default=400000000,
               help=_('L3 agent has total packet rate limits, which '
                      'default value is 400000000, unit pps.')),
    cfg.IntOpt('external_l3_connection', default=100000000,
               help=_('L3 agent has total connection limits, which '
                      'default value is 100000000, unit num.')),
    cfg.FloatOpt('external_bandwidth_ratio', default=1.0,
                 help=_('L3 agent has external bandwidth limits ratio, which '
                        'default value is 1.0.')),
    cfg.FloatOpt('external_packet_rate_ratio', default=1.0,
                 help=_('L3 agent has external packet rate limits ratio, '
                        'which default value is 1.0.')),
    cfg.FloatOpt('external_connection_ratio', default=1.0,
                 help=_('L3 agent has external connection ratio, which '
                        'default value is 1.0.')),
    cfg.IntOpt('max_routers_on_l3_agent', default=0,
               help=_('The maximum number of routers that an l3 agent can '
                      'handle, the default value is 0, which means it can '
                      'handle an infinite number of routers.')),
    cfg.BoolOpt('delete_icmp_and_udp_conntrack', default=False,
                help=_('Whether deleting icmp and udp conntrack rules in '
                       'router namespace.')),
    cfg.BoolOpt('enable_fip_meter_packet_limit', default=False,
                help=_('Whether enable packet limit for floating ip '
                       'using ovs meter.')),
    cfg.StrOpt('router_metering_path',
               default='$state_path/metering',
               help=_('Location to store router metering information '
                      'files')),
    cfg.IntOpt('metering_port', default=8080,
               help=_('Metering Port used by Taihu service. ')),
    cfg.IntOpt('taihu_request_timeout', default=30,
               help=_('Timeout in seconds to wait for L3 agent '
                      'request taihu service.')),
    cfg.StrOpt('metering_host', default="localhost",
               help=_('Host that Taihu service is deployed. ')),
]


ovs_opts = [
    cfg.StrOpt('of_interface', default='native',
               deprecated_for_removal=True,
               choices=['ovs-ofctl', 'native'],
               help=_("OpenFlow interface to use.")),
    cfg.StrOpt('datapath_type', default=p_consts.OVS_DATAPATH_SYSTEM,
               choices=[p_consts.OVS_DATAPATH_SYSTEM,
                        p_consts.OVS_DATAPATH_NETDEV],
               help=_("OVS datapath to use. 'system' is the default value and "
                      "corresponds to the kernel datapath. To enable the "
                      "userspace datapath set this value to 'netdev'.")),
    cfg.IPOpt('of_listen_address', default='127.0.0.1',
              help=_("Address to listen on for OpenFlow connections. "
                     "Used only for 'native' driver.")),
    cfg.PortOpt('of_listen_port', default=7733,
                help=_("Port to listen on for OpenFlow connections. "
                       "Used only for 'native' driver.")),
    cfg.IntOpt('of_connect_timeout', default=300,
               help=_("Timeout in seconds to wait for "
                      "the local switch connecting the controller. "
                      "Used only for 'native' driver.")),
    cfg.IntOpt('of_request_timeout', default=300,
               help=_("Timeout in seconds to wait for a single "
                      "OpenFlow request. "
                      "Used only for 'native' driver.")),
    cfg.IntOpt('of_inactivity_probe', default=10,
               help=_("The inactivity_probe interval in seconds for the local "
                      "switch connection to the controller. "
                      "A value of 0 disables inactivity probes. "
                      "Used only for 'native' driver.")),
]


def register_l3_agent_config_opts(opts, cfg=cfg.CONF):
    cfg.register_opts(opts)


def register_l3_agent_ovs_opts(opts, cfg=cfg.CONF):
    cfg.register_opts(opts, "OVS")
