#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from oslo_log import log as logging
from ryu.lib.packet import arp
from ryu.lib.packet import ether_types
from ryu.lib.packet import icmp as icmpv4
from ryu.lib.packet import icmpv6
from ryu.lib.packet import in_proto

from neutron.agent.common import utils as agent_comm
from neutron.common import constants as l3_constants
from neutron.common import utils as common_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.native \
    import ovs_bridge

LOG = logging.getLogger(__name__)

ICMP6_SNMA = l3_constants.DVR_ICMP6_SNMA


class L3AgentBridge(ovs_bridge.OVSAgentBridge):

    def enable_defer(self):
        pass

    def disable_defer(self):
        pass

    def defer_apply_flows(self):
        pass

    def init_bridge_flows(self):
        self.uninstall_flows(cookie=0)
        self.install_normal(table_id=l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE,
                            priority=1)
        self.install_drop(table_id=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE,
                          priority=1)

        self.install_goto(dest_table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
                          table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
                          priority=1)
        self.install_goto(dest_table_id=l3_constants.DVR_BRIDGE_FIP_QOS,
                          table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
                          priority=1)
        self.install_goto(dest_table_id=l3_constants.DVR_BRIDGE_FIP_NAT,
                          table_id=l3_constants.DVR_BRIDGE_FIP_QOS,
                          priority=1)

        # Revisit if this is needed.
        self.install_goto(dest_table_id=l3_constants.DVR_BRIDGE_ACL,
                          table_id=l3_constants.DVR_BRIDGE_FIP_NAT,
                          priority=1)
        self.install_goto(dest_table_id=l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE,
                          table_id=l3_constants.DVR_BRIDGE_ACL,
                          priority=1)
        self.install_goto(
            dest_table_id=l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE,
            table_id=l3_constants.DVR_BRIDGE_PRE_SNAT_ACL,
            priority=1)

    def install_goto_snat_ip4(self, mac_address, router_cidr):
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        self.install_goto(
            priority=100, eth_type=ether_types.ETH_TYPE_IP,
            eth_dst=mac_address,
            table_id=ext_output_table,
            ipv4_src=router_cidr,
            dest_table_id=l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE)

    def install_goto_snat_ip6(self, mac_address, router_cidr):
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        self.install_goto(
            priority=100, eth_type=ether_types.ETH_TYPE_IPV6,
            eth_dst=mac_address,
            table_id=ext_output_table,
            ipv6_src=router_cidr,
            dest_table_id=l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE)

    def install_arp_to_in_port(self):
        (_dp, ofp, ofpp) = self._get_dp()
        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP)
        actions = [ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
        self.install_apply_actions(
            table_id=l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE,
            priority=50,
            match=match,
            actions=actions)

    def install_snat_route(self, mac_src, mac_dst):
        (_dp, ofp, ofpp) = self._get_dp()
        match = ofpp.OFPMatch(eth_dst=mac_src)
        actions = [ofpp.OFPActionSetField(eth_src=mac_src),
                   ofpp.OFPActionSetField(eth_dst=mac_dst),
                   ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
        self.install_apply_actions(
            table_id=l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE,
            priority=100,
            match=match,
            actions=actions)

    def install_route_goto(self, input_table, output_table,
                           priority, mac_src, mac_dst, route_ip,
                           ip_version):
        (_dp, ofp, ofpp) = self._get_dp()
        if ip_version == 4:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  ipv4_dst=route_ip)
        else:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ipv6_dst=route_ip)
        actions = [ofpp.OFPActionSetField(eth_src=mac_src),
                   ofpp.OFPActionSetField(eth_dst=mac_dst),
                   ofpp.OFPActionDecNwTtl()]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(table_id=output_table)]
        self.install_instructions(table_id=input_table, priority=priority,
                                  instructions=instructions,
                                  match=match)

    def remove_route_goto(self, input_table, route_ip, ip_version):
        (_dp, _ofp, ofpp) = self._get_dp()
        if ip_version == 4:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  ipv4_dst=route_ip)
        else:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ipv6_dst=route_ip)
        self.uninstall_flows(table_id=input_table, match=match)

    def remove_router_interface_ip_flows(self, ip, ip_cidr, mac):
        if common_utils.get_ip_version(ip) == 6:
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ipv6_dst=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ip_proto=in_proto.IPPROTO_ICMPV6,
                                 icmpv6_type=135,
                                 ipv6_nd_target=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ip_proto=in_proto.IPPROTO_ICMPV6,
                                 icmpv6_type=136,
                                 ipv6_nd_target=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ip_proto=in_proto.IPPROTO_ICMPV6,
                                 ipv6_src=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ip_proto=in_proto.IPPROTO_ICMPV6,
                                 icmpv6_type=135,
                                 ipv6_nd_target=ip_cidr)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ip_proto=in_proto.IPPROTO_ICMPV6,
                                 icmpv6_type=136,
                                 ipv6_nd_target=ip_cidr)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ipv6_dst=ip_cidr)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                 ipv6_src=ip_cidr)
        elif common_utils.get_ip_version(ip) == 4:
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_dst=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IP,
                                 ip_proto=in_proto.IPPROTO_ICMP,
                                 ipv4_dst=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IP,
                                 ip_proto=in_proto.IPPROTO_ICMP,
                                 ipv4_src=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_ARP,
                                 ipv4_dst=ip)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_dst=ip_cidr)
            self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_src=ip_cidr)
        self.uninstall_flows(
            table_id=l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE,
            eth_dst=mac)

    def _generate_external_gateway_ping_actions(self, ex_gw_port, gateway_mac,
                                                port):
        (_dp, _ofp, ofpp) = self._get_dp()
        ipv4_actions = []
        ipv6_actions_rsp = []
        ipv6_actions_nsp = []

        # Note(davidsha)
        #    Creates flows that convert incoming arp/icmpv6 packets
        #    into arp/imcpv6 requests to the external gateways to learn their
        #    mac addresses.
        #    For each traffic type a packet is sent to the gateways with the
        #    same protocol.
        for subnet in ex_gw_port['subnets']:
            for fixed_ip in ex_gw_port['fixed_ips']:
                ip = fixed_ip['ip_address']
                ip_cidr = ip + "/%s" % fixed_ip['prefixlen']
                if (common_utils.get_ip_version(ip) == 4 and
                        agent_comm.check_ip_in_subnet(ip_cidr,
                                                      subnet['cidr'])):
                    ipv4_actions += [
                        ofpp.OFPActionSetField(eth_src=gateway_mac),
                        ofpp.OFPActionSetField(eth_dst="ff:ff:ff:ff:ff:ff"),
                        ofpp.OFPActionSetField(arp_sha=gateway_mac),
                        ofpp.OFPActionSetField(arp_tha="00:00:00:00:00:00"),
                        ofpp.OFPActionSetField(arp_spa=ip),
                        ofpp.OFPActionSetField(arp_tpa=subnet['gateway_ip']),
                        ofpp.OFPActionSetField(arp_op=arp.ARP_REQUEST),
                        ofpp.OFPActionOutput(port, 0)]
                elif (common_utils.get_ip_version(ip) == 6 and
                        agent_comm.check_ip_in_subnet(ip_cidr,
                                                      subnet['cidr'])):
                    ipv6_actions_rsp += [
                        ofpp.OFPActionSetField(eth_src=gateway_mac),
                        ofpp.OFPActionSetField(eth_dst="00:00:00:00:00:00"),
                        ofpp.OFPActionSetField(ipv6_src=ip),
                        ofpp.OFPActionSetField(ipv6_dst=subnet['gateway_ip']),
                        ofpp.OFPActionSetField(
                            icmpv6_type=icmpv6.ND_ROUTER_SOLICIT),
                        ofpp.OFPActionSetField(in_port=0),
                        ofpp.OFPActionOutput(port, 0)]
                    ipv6_actions_nsp += [
                        ofpp.OFPActionSetField(eth_src=gateway_mac),
                        ofpp.OFPActionSetField(eth_dst="00:00:00:00:00:00"),
                        ofpp.OFPActionSetField(ipv6_src=ip),
                        ofpp.OFPActionSetField(ipv6_dst=subnet['gateway_ip']),
                        ofpp.OFPActionSetField(
                            icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT),
                        ofpp.OFPActionSetField(in_port=0),
                        ofpp.OFPActionOutput(port, 0)]
        return [ipv4_actions, ipv6_actions_rsp, ipv6_actions_nsp]

    def install_dvr_snat(self, ex_gw_port, gateway_mac, of_port):
        (_dp, ofp, ofpp) = self._get_dp()
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        acts = self._generate_external_gateway_ping_actions(
            ex_gw_port, gateway_mac, of_port)
        ipv4_actions = acts[0]
        ipv6_actions_rsp = acts[1]
        ipv6_actions_nsp = acts[2]

        if ipv4_actions:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                  arp_op=2)
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                           ipv4_actions), ]
            self.install_instructions(table_id=mac_learning_table,
                                      priority=150,
                                      instructions=instructions,
                                      match=match)
        if ipv6_actions_rsp:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ip_proto=in_proto.IPPROTO_ICMPV6,
                                  icmpv6_type=133)
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                           ipv6_actions_rsp), ]
            self.install_instructions(table_id=mac_learning_table,
                                      priority=150,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ip_proto=in_proto.IPPROTO_ICMPV6,
                                  icmpv6_type=135)
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                           ipv6_actions_nsp), ]
            self.install_instructions(table_id=mac_learning_table,
                                      priority=150,
                                      instructions=instructions,
                                      match=match)

    def install_router_interface_flows(self, ip, ip_cidr, port, mac):
        (_dp, ofp, ofpp) = self._get_dp()
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        admin_state_table = l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE
        output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE

        if (common_utils.get_ip_version(ip) == 4 and
                common_utils.get_ip_version(ip_cidr) == 4):
            self.install_goto(
                priority=100, eth_type=ether_types.ETH_TYPE_IP,
                table_id=input_table,
                ipv4_dst=ip_cidr,
                dest_table_id=admin_state_table)

            self.install_goto(
                priority=50, eth_type=ether_types.ETH_TYPE_ARP,
                table_id=input_table,
                arp_tpa=ip,
                dest_table_id=admin_state_table)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                  arp_op=arp.ARP_REQUEST,
                                  arp_tpa=ip,
                                  in_port=port)
            actions = [ofpp.NXActionRegMove(src_field='eth_src',
                                            dst_field='eth_dst',
                                            n_bits=48),
                       ofpp.OFPActionSetField(eth_src=mac),
                       ofpp.OFPActionSetField(arp_op=arp.ARP_REPLY),
                       ofpp.NXActionRegMove(src_field='arp_sha',
                                            dst_field='arp_tha',
                                            n_bits=48),
                       ofpp.NXActionRegMove(src_field='arp_spa',
                                            dst_field='arp_tpa',
                                            n_bits=32),
                       ofpp.OFPActionSetField(arp_spa=ip),
                       ofpp.OFPActionSetField(arp_sha=mac),
                       ofpp.NXActionResubmitTable(table_id=output_table),
                       ofpp.NXActionResubmitTable(table_id=mac_learning_table)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=next_hop_table, priority=300,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  ip_proto=in_proto.IPPROTO_ICMP,
                                  ipv4_dst=ip,
                                  icmpv4_type=icmpv4.ICMP_ECHO_REQUEST)
            actions = [ofpp.NXActionRegMove(src_field='ipv4_src',
                                            dst_field='ipv4_dst',
                                            n_bits=32),
                       ofpp.OFPActionSetField(ipv4_src=ip),
                       ofpp.OFPActionSetField(
                           icmpv4_type=icmpv4.ICMP_ECHO_REPLY),
                       ofpp.NXActionRegMove(src_field='eth_src',
                                            dst_field='eth_dst',
                                            n_bits=48),
                       ofpp.OFPActionSetField(eth_src=mac)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=output_table)]
            self.install_instructions(table_id=next_hop_table, priority=300,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  ip_proto=in_proto.IPPROTO_ICMP,
                                  eth_src=mac,
                                  ipv4_src=ip)
            actions = [ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=150,
                                      instructions=instructions,
                                      match=match)
        elif (common_utils.get_ip_version(ip) == 6 and
                common_utils.get_ip_version(ip_cidr) == 6):
            self.install_goto(
                priority=150, eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT,
                ipv6_nd_target=ip_cidr,
                table_id=input_table,
                dest_table_id=admin_state_table)

            self.install_goto(
                priority=150, eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT,
                ipv6_nd_target=ip_cidr,
                table_id=input_table,
                dest_table_id=admin_state_table)

            self.install_goto(
                priority=100, eth_type=ether_types.ETH_TYPE_IPV6,
                ipv6_dst=ip_cidr,
                table_id=input_table,
                dest_table_id=admin_state_table)

            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                ipv6_nd_target=ip,
                icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT,
                in_port=port)
            actions = [
                ofpp.NXActionRegMove(src_field='eth_src',
                                     dst_field='eth_dst',
                                     n_bits=48),
                ofpp.OFPActionSetField(eth_src=mac),
                ofpp.NXActionRegMove(src_field='ipv6_src',
                                     dst_field='ipv6_dst',
                                     n_bits=128),
                ofpp.OFPActionSetField(
                    reg0=int("0x%s" % ICMP6_SNMA.replace(":", "")[0:8], 0)),
                ofpp.OFPActionSetField(
                    reg1=int("0x%s" % ICMP6_SNMA.replace(":", "")[8:16], 0)),
                ofpp.OFPActionSetField(
                    reg2=int("0x%s" % ICMP6_SNMA.replace(":", "")[16:24], 0)),
                ofpp.OFPActionSetField(
                    reg3=int("0x%s" % ICMP6_SNMA.replace(":", "")[24:26], 0)),
                ofpp.NXActionRegMove(src_field='reg0',
                                     dst_field='ipv6_dst',
                                     n_bits=32),
                ofpp.NXActionRegMove(src_field='reg1',
                                     dst_field='ipv6_dst',
                                     n_bits=32,
                                     dst_ofs=32),
                ofpp.NXActionRegMove(src_field='reg2',
                                     dst_field='ipv6_dst',
                                     n_bits=32,
                                     dst_ofs=64),
                ofpp.NXActionRegMove(src_field='reg3',
                                     dst_field='ipv6_dst',
                                     n_bits=8,
                                     dst_ofs=96),
                ofpp.OFPActionSetField(ipv6_src=ip),
                ofpp.OFPActionSetField(icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT),
                ofpp.NXActionResubmitTable(table_id=output_table),
                ofpp.NXActionResubmitTable(table_id=mac_learning_table)
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=next_hop_table, priority=300,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                icmpv6_type=icmpv6.ICMPV6_ECHO_REQUEST,
                ipv6_dst=ip)
            actions = [
                ofpp.NXActionRegMove(src_field='ipv6_src',
                                     dst_field='ipv6_dst',
                                     n_bits=128),
                ofpp.OFPActionSetField(ipv6_src=ip),
                ofpp.OFPActionSetField(icmpv6_type=icmpv6.ICMPV6_ECHO_REPLY),
                ofpp.NXActionRegMove(src_field='eth_src',
                                     dst_field='eth_dst',
                                     n_bits=48),
                ofpp.OFPActionSetField(eth_src=mac),
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=output_table)]
            self.install_instructions(table_id=next_hop_table, priority=300,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                eth_src=mac,
                icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT,
                ipv6_src=ip)
            actions = [
                ofpp.OFPActionSetField(ipv6_nd_tll=mac),
                ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0),
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=150,
                                      instructions=instructions,
                                      match=match)
            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                eth_src=mac,
                icmpv6_type=icmpv6.ICMPV6_ECHO_REPLY,
                ipv6_src=ip)
            actions = [
                ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0),
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=150,
                                      instructions=instructions,
                                      match=match)

    def install_drop_fip_admin_state(self, fip_address):
        self.install_drop(
            table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            priority=50,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=fip_address)
        self.install_drop(
            table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            priority=50,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_src=fip_address)

    def remove_drop_fip_admin_state(self, fip_address):
        self.uninstall_flows(
            table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=fip_address)
        self.uninstall_flows(
            table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_src=fip_address)

    def install_drop_fip_ad_ports(self, fip_address, port):
        self.install_drop(
            table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
            priority=50,
            eth_type=ether_types.ETH_TYPE_IP,
            ip_proto=in_proto.IPPROTO_TCP,
            ipv4_dst=fip_address,
            tcp_dst=int(port))
        self.install_drop(
            table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
            priority=50,
            eth_type=ether_types.ETH_TYPE_IP,
            ip_proto=in_proto.IPPROTO_UDP,
            ipv4_dst=fip_address,
            udp_dst=int(port))

    def remove_drop_fip_ad_ports(self, fip_address):
        self.uninstall_flows(
            table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=fip_address)

    def install_fip_flows(self, float_ip, fixed_ip, gate_ip,
                          fg_mac, fixed_port_mac,
                          gateway_ip_ofport,
                          fip_subnet_cidr,
                          internal_cidr=None):
        (_dp, ofp, ofpp) = self._get_dp()
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        fip_nat_table = l3_constants.DVR_BRIDGE_FIP_NAT
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        acl_table = l3_constants.DVR_BRIDGE_ACL
        admin_state_table = l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE
        output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        mac_learn_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        int_out_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        snat_table = l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE
        pre_snat_acl_table = l3_constants.DVR_BRIDGE_PRE_SNAT_ACL

        fip = netaddr.IPAddress(float_ip)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ipv4_dst=float_ip)
        actions = [ofpp.OFPActionSetField(ipv4_dst=fixed_ip), ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(table_id=acl_table)]
        self.install_instructions(table_id=fip_nat_table, priority=50,
                                  instructions=instructions,
                                  match=match)

        self.install_goto(dest_table_id=snat_table,
                          table_id=input_table, priority=50,
                          eth_type=ether_types.ETH_TYPE_IP,
                          ipv4_dst=float_ip)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ipv4_src=fip_subnet_cidr,
                              ipv4_dst=float_ip)
        flow_specs = [
            # Match
            ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_IP,
                                 dst=('eth_type_nxm', 0),
                                 n_bits=16),
            ofpp.NXFlowSpecMatch(src=('ipv4_src_nxm', 0),
                                 dst=('ipv4_dst_nxm', 0),
                                 n_bits=32),
            ofpp.NXFlowSpecMatch(src=('reg4', 0),
                                 dst=('ipv4_src_nxm', 0),
                                 n_bits=32),
            # Action
            ofpp.NXFlowSpecLoad(src=int(fip),
                                dst=('ipv4_src_nxm', 0),
                                n_bits=32),
            ofpp.NXFlowSpecLoad(src=('eth_src_nxm', 0),
                                dst=('eth_dst_nxm', 0),
                                n_bits=48),
            ofpp.NXFlowSpecLoad(
                src=int(netaddr.EUI(fg_mac, dialect=netaddr.mac_unix)),
                dst=('eth_src_nxm', 0),
                n_bits=48),
            ofpp.NXFlowSpecOutput(src=('reg3', 0),
                                  dst='',
                                  n_bits=32),
        ]
        actions = [
            ofpp.OFPActionSetField(reg3=gateway_ip_ofport),
            ofpp.OFPActionSetField(reg4=int(fip)),
            ofpp.NXActionLearn(
                table_id=output_table,
                cookie=self.default_cookie,
                priority=100,
                idle_timeout=l3_constants.DVR_LEARN_IDLE_TIMEOUT,
                specs=flow_specs)]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(table_id=snat_table)]
        self.install_instructions(table_id=input_table, priority=51,
                                  instructions=instructions,
                                  match=match)

        self.install_goto(dest_table_id=admin_state_table,
                          table_id=input_table, priority=50,
                          eth_type=ether_types.ETH_TYPE_ARP,
                          arp_tpa=float_ip)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ipv4_src=fixed_ip)
        self.install_goto(dest_table_id=pre_snat_acl_table,
                          table_id=input_table, priority=30, match=match)

        self.install_goto(dest_table_id=next_hop_table,
                          table_id=acl_table, priority=60010,
                          eth_type=ether_types.ETH_TYPE_IP,
                          ipv4_src=float_ip)
        self.install_goto(dest_table_id=output_table,
                          table_id=next_hop_table, priority=80,
                          eth_type=ether_types.ETH_TYPE_IP,
                          ipv4_src=float_ip)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ipv4_src=fixed_ip)
        actions = [ofpp.OFPActionSetField(eth_src=fg_mac),
                   ofpp.OFPActionSetField(ipv4_src=float_ip),
                   ofpp.OFPActionDecNwTtl()]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(table_id=admin_state_table)]
        self.install_instructions(table_id=snat_table, priority=30,
                                  instructions=instructions,
                                  match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                              arp_op=arp.ARP_REQUEST,
                              arp_tpa=float_ip)
        actions = [ofpp.NXActionRegMove(src_field='eth_src',
                                        dst_field='eth_dst',
                                        n_bits=48),
                   ofpp.OFPActionSetField(eth_src=fg_mac),
                   ofpp.OFPActionSetField(arp_op=arp.ARP_REPLY),
                   ofpp.NXActionRegMove(src_field='arp_sha',
                                        dst_field='arp_tha',
                                        n_bits=48),
                   ofpp.NXActionRegMove(src_field='arp_spa',
                                        dst_field='arp_tpa',
                                        n_bits=32),
                   ofpp.OFPActionSetField(arp_spa=float_ip),
                   ofpp.OFPActionSetField(arp_sha=fg_mac),
                   ofpp.NXActionResubmitTable(table_id=int_out_table),
                   ofpp.NXActionResubmitTable(table_id=mac_learn_table)]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
        self.install_instructions(table_id=next_hop_table, priority=300,
                                  instructions=instructions,
                                  match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                              arp_op=arp.ARP_REPLY,
                              eth_dst=fixed_port_mac,
                              arp_tpa=fixed_ip)
        fip_mac_arp_responder_actions = [
            ofpp.OFPActionSetField(arp_sha=fg_mac),
            ofpp.OFPActionSetField(eth_src=fg_mac),
            ofpp.OFPActionSetField(eth_dst="ff:ff:ff:ff:ff:ff"),
            ofpp.OFPActionSetField(arp_spa=float_ip),
            ofpp.OFPActionSetField(arp_tpa=gate_ip),
            ofpp.OFPActionSetField(arp_tha="00:00:00:00:00:00"),
            ofpp.OFPActionSetField(arp_op=arp.ARP_REQUEST),
            # ofpp.OFPActionSetField(in_port=0),
            ofpp.OFPActionOutput(gateway_ip_ofport, 0)]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                       fip_mac_arp_responder_actions)]
        self.install_instructions(table_id=mac_learn_table, priority=150,
                                  instructions=instructions,
                                  match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                              arp_op=arp.ARP_REPLY,
                              eth_src=fg_mac,
                              arp_spa=float_ip)
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                       fip_mac_arp_responder_actions)]
        self.install_instructions(table_id=mac_learn_table, priority=150,
                                  instructions=instructions,
                                  match=match)

        if internal_cidr:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  ipv4_src=float_ip,
                                  ipv4_dst=internal_cidr)
            actions = [ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
            self.install_apply_actions(
                table_id=int_out_table,
                priority=101,
                match=match,
                actions=actions)

    def remove_fip_flows(self, float_ip, fixed_ip):
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_src=fixed_ip)
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=float_ip)
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_src=float_ip)
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_ARP,
            arp_tpa=float_ip)
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_ARP,
            arp_tpa=fixed_ip,
            table_id=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE)
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_ARP,
            arp_spa=float_ip,
            table_id=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE)
        self.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=fixed_ip,
            table_id=l3_constants.DVR_BRIDGE_ACL)

    def install_learn_action_flows(self, ofport):
        if not ofport:
            LOG.error("Invalid ofport for learn actions.")
            return
        (_dp, ofp, ofpp) = self._get_dp()
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        snat_table = l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE

        # Note(davidsha)
        #    These learn action flows are for receiving external
        #    traffic and generating next hop flows like on table 2
        #    to send traffic destined for the external gateway to the
        #    correct mac.
        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                              in_port=ofport,
                              reg4=0,
                              arp_op=arp.ARP_REPLY)
        flow_specs1 = [
            # Match
            ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_ARP,
                                 dst=('eth_type_nxm', 0),
                                 n_bits=16),
            ofpp.NXFlowSpecMatch(src=('arp_spa_nxm', 0),
                                 dst=('arp_tpa_nxm', 0),
                                 n_bits=32),
            # Action
            ofpp.NXFlowSpecOutput(src=('reg4', 0),
                                  dst='',
                                  n_bits=32),
        ]
        flow_specs2 = [
            # Match
            ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_IP,
                                 dst=('eth_type_nxm', 0),
                                 n_bits=16),
            # Action
            ofpp.NXFlowSpecLoad(src=('eth_src_nxm', 0),
                                dst=('eth_dst_nxm', 0),
                                n_bits=48),
            ofpp.NXFlowSpecOutput(src=('reg4', 0),
                                  dst='',
                                  n_bits=32),
        ]
        actions = [
            ofpp.OFPActionSetField(reg4=ofport),
            ofpp.NXActionLearn(
                table_id=ext_output_table,
                priority=100,
                idle_timeout=l3_constants.DVR_LEARN_IDLE_TIMEOUT,
                specs=flow_specs1),
            ofpp.NXActionLearn(
                table_id=ext_output_table,
                priority=99,
                specs=flow_specs2)]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
        self.install_instructions(table_id=input_table, priority=60,
                                  instructions=instructions,
                                  match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP)
        actions = [ofpp.OFPActionDecNwTtl()]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=ext_output_table)]
        self.install_instructions(table_id=input_table, priority=25,
                                  instructions=instructions,
                                  match=match)
        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6)
        actions = [ofpp.OFPActionDecNwTtl()]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=ext_output_table)]
        self.install_instructions(table_id=input_table, priority=25,
                                  instructions=instructions,
                                  match=match)

        self.install_drop(table_id=ext_output_table, priority=25)
        self.install_goto(
            dest_table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            table_id=snat_table, priority=1)

    def install_ip_forwarder_flows(self, ip, src_mac, dst_mac, port):
        LOG.debug("dvr adding ip: %s", ip)

        (_dp, ofp, ofpp) = self._get_dp()
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE

        if common_utils.get_ip_version(ip) == 6:
            LOG.debug("Adding IPv6 flow for %s", ip)
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ipv6_dst=ip)
            actions = [ofpp.OFPActionSetField(eth_src=src_mac),
                       ofpp.OFPActionSetField(eth_dst=dst_mac),
                       ofpp.OFPActionDecNwTtl()]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=output_table)]
            self.install_instructions(table_id=next_hop_table, priority=200,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ip_proto=in_proto.IPPROTO_ICMPV6,
                                  icmpv6_type=136,
                                  ipv6_nd_target=ip)
            actions = [ofpp.OFPActionSetField(eth_src=src_mac),
                       ofpp.OFPActionSetField(eth_dst=dst_mac),
                       ofpp.OFPActionDecNwTtl()]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=output_table)]
            self.install_instructions(table_id=next_hop_table, priority=200,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  ip_proto=in_proto.IPPROTO_ICMPV6,
                                  icmpv6_type=135,
                                  ipv6_nd_target=ip)
            actions = [ofpp.OFPActionSetField(eth_src=src_mac),
                       ofpp.OFPActionSetField(eth_dst=dst_mac),
                       ofpp.OFPActionDecNwTtl()]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=output_table)]
            self.install_instructions(table_id=next_hop_table, priority=200,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  vlan_tci=(0x1000, 0x1000),
                                  eth_dst=dst_mac)
            actions = [ofpp.OFPActionPopVlan(),
                       ofpp.OFPActionOutput(port, 0)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=101,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                                  eth_dst=dst_mac)
            actions = [ofpp.OFPActionOutput(port, 0)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=100,
                                      instructions=instructions,
                                      match=match)

        else:
            LOG.debug("Adding IPv4 flow for %s", ip)
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  ipv4_dst=ip)
            actions = [ofpp.OFPActionSetField(eth_src=src_mac),
                       ofpp.OFPActionSetField(eth_dst=dst_mac),
                       ofpp.OFPActionDecNwTtl()]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(table_id=output_table)]
            self.install_instructions(table_id=next_hop_table, priority=200,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  vlan_tci=(0x1000, 0x1000),
                                  eth_dst=dst_mac)
            actions = [ofpp.OFPActionPopVlan(),
                       ofpp.OFPActionOutput(port, 0)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=101,
                                      instructions=instructions,
                                      match=match)

            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                  eth_dst=dst_mac)
            actions = [ofpp.OFPActionOutput(port, 0)]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.install_instructions(table_id=output_table, priority=100,
                                      instructions=instructions,
                                      match=match)

    def remove_ip_forwarder_flows(self, mac, ip):
        if common_utils.get_ip_version(ip) == 6:
            self.uninstall_flows(
                eth_type=ether_types.ETH_TYPE_IPV6, ipv6_dst=ip)
            self.uninstall_flows(
                eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                icmpv6_type=135,
                ipv6_nd_target=ip)
            self.uninstall_flows(
                eth_type=ether_types.ETH_TYPE_IPV6,
                ip_proto=in_proto.IPPROTO_ICMPV6,
                icmpv6_type=136,
                ipv6_nd_target=ip)
            self.uninstall_flows(
                eth_type=ether_types.ETH_TYPE_IPV6, eth_dst=mac)
        else:
            self.uninstall_flows(
                eth_type=ether_types.ETH_TYPE_IP, ipv4_dst=ip)
            self.uninstall_flows(
                eth_type=ether_types.ETH_TYPE_IP, eth_dst=mac)

    def remove_external_gateway_flows(self, ex_gw_port):
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        gateway_mac = ex_gw_port['mac_address']

        self.uninstall_flows(
            table_id=input_table,
            eth_type=ether_types.ETH_TYPE_ARP,
            reg4=0)
        self.uninstall_flows(
            table_id=mac_learning_table, eth_type=ether_types.ETH_TYPE_ARP)
        self.uninstall_flows(
            table_id=mac_learning_table,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6)

        for fixed_ip in ex_gw_port['fixed_ips']:
            ip = fixed_ip['ip_address']
            if common_utils.get_ip_version(ip) == 6:
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IPV6, eth_src=gateway_mac)
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IPV6,
                    ip_proto=in_proto.IPPROTO_ICMPV6,
                    eth_src=gateway_mac)
                self.uninstall_flows(eth_type=ether_types.ETH_TYPE_IPV6,
                                     ipv6_dst=ip)
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IPV6,
                    ip_proto=in_proto.IPPROTO_ICMPV6,
                    icmpv6_type=135,
                    ipv6_nd_target=ip)
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IPV6,
                    ip_proto=in_proto.IPPROTO_ICMPV6,
                    icmpv6_type=136,
                    ipv6_nd_target=ip)
            else:
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IP,
                    eth_src=gateway_mac)
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IP,
                    ip_proto=in_proto.IPPROTO_ICMP,
                    eth_src=gateway_mac)
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_ARP,
                    arp_tpa=ip)
                self.uninstall_flows(
                    eth_type=ether_types.ETH_TYPE_IP,
                    ipv4_dst=ip)

        self.uninstall_flows(table_id=ext_output_table)
