Controller node
~~~~~~~~~~~~~~~

Configure network interfaces
----------------------------

#. Configure the first interface as the management interface:

   IP address: ********1

   Network mask: ************* (or /24)

   Default gateway: ********

#. The provider interface uses a special configuration without an IP
   address assigned to it. Configure the second interface as the provider
   interface:

   Replace ``INTERFACE_NAME`` with the actual interface name. For example,
   *eth1* or *ens224*.



* Edit the ``/etc/sysconfig/network-scripts/ifcfg-INTERFACE_NAME`` file
  to contain the following:

  Do not change the ``HWADDR`` and ``UUID`` keys.

  .. path /etc/sysconfig/network-scripts/ifcfg-INTERFACE_NAME
  .. code-block:: ini

     DEVICE=INTERFACE_NAME
     TYPE=Ethernet
     ONBOOT="yes"
     BOOTPROTO="none"

  .. end



#. Reboot the system to activate the changes.

Configure name resolution
-------------------------

#. Set the hostname of the node to ``controller``.

#. .. include:: shared/edit_hosts_file.txt
