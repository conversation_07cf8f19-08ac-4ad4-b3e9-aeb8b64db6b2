<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 476 741" width="476pt" height="741pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-04 15:57:53 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><path d="M 36.346457 283.46457 L 190.4252 283.46457 C 194.84347 283.46457 198.4252 287.04629 198.4252 291.46457 L 198.4252 649.6378 C 198.4252 654.05607 194.84347 657.6378 190.4252 657.6378 L 36.346457 657.6378 C 31.928179 657.6378 28.346457 654.05607 28.346457 649.6378 L 28.346457 291.46457 C 28.346457 287.04629 31.928179 283.46457 36.346457 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 288.46457)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="34.971987" y="13" textLength="90.134766"> Network Nodes</tspan></text><line x1="113.385826" y1="637.79527" x2="113.385826" y2="416.6929" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="113.385826" y1="637.79527" x2="113.385826" y2="491.81102" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 234.77165 56.692913 L 473.88976 56.692913 C 478.30804 56.692913 481.88976 60.274635 481.88976 64.692913 L 481.88976 366.17323 C 481.88976 370.5915 478.30804 374.17323 473.88976 374.17323 L 234.77165 374.17323 C 230.35337 374.17323 226.77165 370.5915 226.77165 366.17323 L 226.77165 64.692913 C 226.77165 60.274635 230.35337 56.692913 234.77165 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="75.55515" y="13" textLength="94.00781"> Compute Nodes</tspan></text><text transform="translate(104.188974 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".3515625" y="17" textLength="285.03906">Open vSwitch - High-availability with D</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="285.23438" y="17" textLength="19.414062">VR</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="126.569336" y="35" textLength="42.767578">Overvie</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="169.09668" y="35" textLength="9.3339844">w</tspan></text><line x1="361.82614" y1="453.5433" x2="425.19685" y2="453.5433" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 397.95483 455.0658 C 386.0433 453.5433 390.79332 440.72645 409.79486 442.91338 C 411.55778 438.65036 433.65401 439.3423 433.50956 442.91338 C 447.36457 438.34592 465.07045 447.45335 453.1943 452.02082 C 467.4451 454.23524 453.01447 466.16626 441.3189 464.17323 C 440.3829 467.49515 419.47472 468.65764 417.63957 464.17323 C 405.80027 468.96236 381.11346 461.5988 397.95483 455.0658 Z" fill="#fdf5dd"/><path d="M 397.95483 455.0658 C 386.0433 453.5433 390.79332 440.72645 409.79486 442.91338 C 411.55778 438.65036 433.65401 439.3423 433.50956 442.91338 C 447.36457 438.34592 465.07045 447.45335 453.1943 452.02082 C 467.4451 454.23524 453.01447 466.16626 441.3189 464.17323 C 440.3829 467.49515 419.47472 468.65764 417.63957 464.17323 C 405.80027 468.96236 381.11346 461.5988 397.95483 455.0658 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(404.40157 448.0433)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="5.0179317" y="9" textLength="31.554688">Internet</tspan></text><circle cx="184.25197" cy="708.6614" r="8.5039505" fill="#738a05"/><text transform="translate(197.7559 702.32283)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan></text><path d="M 36.346457 56.692913 L 190.4252 56.692913 C 194.84347 56.692913 198.4252 60.274635 198.4252 64.692913 L 198.4252 252.7874 C 198.4252 257.20568 194.84347 260.7874 190.4252 260.7874 L 36.346457 260.7874 C 31.928179 260.7874 28.346457 257.20568 28.346457 252.7874 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="33.71808" y="13" textLength="34.435547"> Contr</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="67.913393" y="13" textLength="58.447266">oller Node</tspan></text><path d="M 70.86614 99.2126 C 70.86614 99.2126 92.83585 101.57882 104.88189 141.73228 C 116.92793 181.88575 113.385826 240.94488 113.385826 240.94488" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="141.73228" x2="113.385826" y2="240.94488" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="129.64497" y1="204.09081" x2="113.385826" y2="240.94488" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 105.385826 C 99.2126 109.804104 95.630876 113.385826 91.2126 113.385826 L 50.519685 113.385826 C 46.101407 113.385826 42.519685 109.804104 42.519685 105.385826 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 105.385826 C 99.2126 109.804104 95.630876 113.385826 91.2126 113.385826 L 50.519685 113.385826 C 46.101407 113.385826 42.519685 109.804104 42.519685 105.385826 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 88.2126)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.8308315" y="9" textLength="15.03125">SQL</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="5.133566" y="20" textLength="36.425781">Database</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 127.559054 L 91.2126 127.559054 C 95.630876 127.559054 99.2126 131.14078 99.2126 135.559054 L 99.2126 147.90551 C 99.2126 152.32379 95.630876 155.90551 91.2126 155.90551 L 50.519685 155.90551 C 46.101407 155.90551 42.519685 152.32379 42.519685 147.90551 L 42.519685 135.559054 C 42.519685 131.14078 46.101407 127.559054 50.519685 127.559054 Z" fill="#fdf5dd"/><path d="M 50.519685 127.559054 L 91.2126 127.559054 C 95.630876 127.559054 99.2126 131.14078 99.2126 135.559054 L 99.2126 147.90551 C 99.2126 152.32379 95.630876 155.90551 91.2126 155.90551 L 50.519685 155.90551 C 46.101407 155.90551 42.519685 152.32379 42.519685 147.90551 L 42.519685 135.559054 C 42.519685 131.14078 46.101407 127.559054 50.519685 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 130.73228)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.606222" y="9" textLength="33.480469">Message</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.219503" y="20" textLength="14.253906">Bus</tspan></text></g><g filter="url(#Shadow)"><path d="M 129.88976 85.03937 L 181.92126 85.03937 C 186.33954 85.03937 189.92126 88.62109 189.92126 93.03937 L 189.92126 196.09449 C 189.92126 200.51276 186.33954 204.09449 181.92126 204.09449 L 129.88976 204.09449 C 125.471485 204.09449 121.88976 200.51276 121.88976 196.09449 L 121.88976 93.03937 C 121.88976 88.62109 125.471485 85.03937 129.88976 85.03937 Z" fill="#fdf5dd"/><path d="M 129.88976 85.03937 L 181.92126 85.03937 C 186.33954 85.03937 189.92126 88.62109 189.92126 93.03937 L 189.92126 196.09449 C 189.92126 200.51276 186.33954 204.09449 181.92126 204.09449 L 129.88976 204.09449 C 125.471485 204.09449 121.88976 200.51276 121.88976 196.09449 L 121.88976 93.03937 C 121.88976 88.62109 125.471485 85.03937 129.88976 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(126.88976 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.4044197" y="9" textLength="45.222656">Networking</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.3751228" y="20" textLength="51.28125">Management</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 170.07874 L 176.25197 170.07874 C 180.67025 170.07874 184.25197 173.66046 184.25197 178.07874 L 184.25197 190.4252 C 184.25197 194.84347 180.67025 198.4252 176.25197 198.4252 L 135.559054 198.4252 C 131.14078 198.4252 127.559054 194.84347 127.559054 190.4252 L 127.559054 178.07874 C 127.559054 173.66046 131.14078 170.07874 135.559054 170.07874 Z" fill="#eae3cc"/><path d="M 135.559054 170.07874 L 176.25197 170.07874 C 180.67025 170.07874 184.25197 173.66046 184.25197 178.07874 L 184.25197 190.4252 C 184.25197 194.84347 180.67025 198.4252 176.25197 198.4252 L 135.559054 198.4252 C 131.14078 198.4252 127.559054 194.84347 127.559054 190.4252 L 127.559054 178.07874 C 127.559054 173.66046 131.14078 170.07874 135.559054 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 178.75197)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".8835659" y="9" textLength="44.92578">ML2 Plug-in</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 127.559054 L 176.25197 127.559054 C 180.67025 127.559054 184.25197 131.14078 184.25197 135.559054 L 184.25197 147.90551 C 184.25197 152.32379 180.67025 155.90551 176.25197 155.90551 L 135.559054 155.90551 C 131.14078 155.90551 127.559054 152.32379 127.559054 147.90551 L 127.559054 135.559054 C 127.559054 131.14078 131.14078 127.559054 135.559054 127.559054 Z" fill="#eae3cc"/><path d="M 135.559054 127.559054 L 176.25197 127.559054 C 180.67025 127.559054 184.25197 131.14078 184.25197 135.559054 L 184.25197 147.90551 C 184.25197 152.32379 180.67025 155.90551 176.25197 155.90551 L 135.559054 155.90551 C 131.14078 155.90551 127.559054 152.32379 127.559054 147.90551 L 127.559054 135.559054 C 127.559054 131.14078 131.14078 127.559054 135.559054 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 136.23228)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.020285" y="9" textLength="12.652344">API</tspan></text></g><circle cx="42.519685" cy="680.31496" r="8.5039505" fill="#d11b24"/><text transform="translate(56.409448 668.3071)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="102.9834">Management network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.0.0/24</tspan></text><path d="M 113.385826 240.94488 C 113.385826 240.94488 166.30242 230.79182 198.4252 274.96063 C 230.54797 319.12944 194.64888 346.25703 226.77165 396.8504 C 238.36447 415.10908 252.66553 427.5728 266.21018 436.04618" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="354.3307" y1="354.3307" x2="322.22443" y2="429.24535" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 93.03937 226.77165 L 133.73228 226.77165 C 138.15056 226.77165 141.73228 230.35337 141.73228 234.77165 L 141.73228 247.11811 C 141.73228 251.53639 138.15056 255.11811 133.73228 255.11811 L 93.03937 255.11811 C 88.62109 255.11811 85.03937 251.53639 85.03937 247.11811 L 85.03937 234.77165 C 85.03937 230.35337 88.62109 226.77165 93.03937 226.77165 Z" fill="#d11b24"/><path d="M 93.03937 226.77165 L 133.73228 226.77165 C 138.15056 226.77165 141.73228 230.35337 141.73228 234.77165 L 141.73228 247.11811 C 141.73228 251.53639 138.15056 255.11811 133.73228 255.11811 L 93.03937 255.11811 C 88.62109 255.11811 85.03937 251.53639 85.03937 247.11811 L 85.03937 234.77165 C 85.03937 230.35337 88.62109 226.77165 93.03937 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 235.44488)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.6062222" y="9" textLength="41.480469">Interface 1</tspan></text></g><line x1="269.29134" y1="354.3307" x2="300.3137" y2="426.7162" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 269.29134 354.3307 C 269.29134 354.3307 357.17017 265.88302 405.35433 198.4252 C 421.29735 176.10496 430.41405 159.55549 435.47633 147.40157" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="354.3307" x2="286.83914" y2="317.48031" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="354.3307" x2="405.35433" y2="268.15748" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 243.27559 212.59842 L 380.34645 212.59842 C 384.76473 212.59842 388.34645 216.18015 388.34645 220.59842 L 388.34645 309.48031 C 388.34645 313.8986 384.76473 317.48031 380.34645 317.48031 L 243.27559 317.48031 C 238.85731 317.48031 235.27559 313.8986 235.27559 309.48031 L 235.27559 220.59842 C 235.27559 216.18015 238.85731 212.59842 243.27559 212.59842 Z" fill="#fdf5dd"/><path d="M 243.27559 212.59842 L 380.34645 212.59842 C 384.76473 212.59842 388.34645 216.18015 388.34645 220.59842 L 388.34645 309.48031 C 388.34645 313.8986 384.76473 317.48031 380.34645 317.48031 L 243.27559 317.48031 C 238.85731 317.48031 235.27559 313.8986 235.27559 309.48031 L 235.27559 220.59842 C 235.27559 216.18015 238.85731 212.59842 243.27559 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(240.27559 217.59842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="32.873323" y="9" textLength="77.32422">Open vSwitch Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 340.15748 L 289.6378 340.15748 C 294.05607 340.15748 297.6378 343.7392 297.6378 348.15748 L 297.6378 360.50393 C 297.6378 364.92221 294.05607 368.50393 289.6378 368.50393 L 248.94488 368.50393 C 244.5266 368.50393 240.94488 364.92221 240.94488 360.50393 L 240.94488 348.15748 C 240.94488 343.7392 244.5266 340.15748 248.94488 340.15748 Z" fill="#d11b24"/><path d="M 248.94488 340.15748 L 289.6378 340.15748 C 294.05607 340.15748 297.6378 343.7392 297.6378 348.15748 L 297.6378 360.50393 C 297.6378 364.92221 294.05607 368.50393 289.6378 368.50393 L 248.94488 368.50393 C 244.5266 368.50393 240.94488 364.92221 240.94488 360.50393 L 240.94488 348.15748 C 240.94488 343.7392 244.5266 340.15748 248.94488 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 348.8307)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.6062222" y="9" textLength="41.480469">Interface 1</tspan></text></g><line x1="269.29134" y1="99.2126" x2="269.29134" y2="141.73228" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 262.9674 240.94488 C 259.18905 230.68737 255.11811 215.64094 255.11811 198.4252 C 255.11811 166.30242 269.29134 141.73228 269.29134 141.73228" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><circle cx="184.25197" cy="680.31496" r="8.5039505" fill="#bd3612"/><text transform="translate(197.7559 668.3071)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text><g filter="url(#Shadow)"><path d="M 248.94488 85.03937 L 289.6378 85.03937 C 294.05607 85.03937 297.6378 88.62109 297.6378 93.03937 L 297.6378 105.385826 C 297.6378 109.804104 294.05607 113.385826 289.6378 113.385826 L 248.94488 113.385826 C 244.5266 113.385826 240.94488 109.804104 240.94488 105.385826 L 240.94488 93.03937 C 240.94488 88.62109 244.5266 85.03937 248.94488 85.03937 Z" fill="#fdf5dd"/><path d="M 248.94488 85.03937 L 289.6378 85.03937 C 294.05607 85.03937 297.6378 88.62109 297.6378 93.03937 L 297.6378 105.385826 C 297.6378 109.804104 294.05607 113.385826 289.6378 113.385826 L 248.94488 113.385826 C 244.5266 113.385826 240.94488 109.804104 240.94488 105.385826 L 240.94488 93.03937 C 240.94488 88.62109 244.5266 85.03937 248.94488 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 93.7126)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 340.15748 L 374.67716 340.15748 C 379.09544 340.15748 382.67716 343.7392 382.67716 348.15748 L 382.67716 360.50393 C 382.67716 364.92221 379.09544 368.50393 374.67716 368.50393 L 333.98425 368.50393 C 329.56597 368.50393 325.98425 364.92221 325.98425 360.50393 L 325.98425 348.15748 C 325.98425 343.7392 329.56597 340.15748 333.98425 340.15748 Z" fill="#bd3612"/><path d="M 333.98425 340.15748 L 374.67716 340.15748 C 379.09544 340.15748 382.67716 343.7392 382.67716 348.15748 L 382.67716 360.50393 C 382.67716 364.92221 379.09544 368.50393 374.67716 368.50393 L 333.98425 368.50393 C 329.56597 368.50393 325.98425 364.92221 325.98425 360.50393 L 325.98425 348.15748 C 325.98425 343.7392 329.56597 340.15748 333.98425 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 348.8307)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 127.559054 L 289.6378 127.559054 C 294.05607 127.559054 297.6378 131.14078 297.6378 135.559054 L 297.6378 147.90551 C 297.6378 152.32379 294.05607 155.90551 289.6378 155.90551 L 248.94488 155.90551 C 244.5266 155.90551 240.94488 152.32379 240.94488 147.90551 L 240.94488 135.559054 C 240.94488 131.14078 244.5266 127.559054 248.94488 127.559054 Z" fill="#fdf5dd"/><path d="M 248.94488 127.559054 L 289.6378 127.559054 C 294.05607 127.559054 297.6378 131.14078 297.6378 135.559054 L 297.6378 147.90551 C 297.6378 152.32379 294.05607 155.90551 289.6378 155.90551 L 248.94488 155.90551 C 244.5266 155.90551 240.94488 152.32379 240.94488 147.90551 L 240.94488 135.559054 C 240.94488 131.14078 244.5266 127.559054 248.94488 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 136.23228)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.4148173" y="9" textLength="9.9375">Fir</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="18.192161" y="9" textLength="4.609375">e</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="22.64138" y="9" textLength="15.636719">wall</tspan></text></g><g filter="url(#Shadow)"><path d="M 44.850393 439.37008 L 181.92126 439.37008 C 186.33954 439.37008 189.92126 442.9518 189.92126 447.37008 L 189.92126 536.25197 C 189.92126 540.67024 186.33954 544.25197 181.92126 544.25197 L 44.850393 544.25197 C 40.432115 544.25197 36.850393 540.67024 36.850393 536.25197 L 36.850393 447.37008 C 36.850393 442.9518 40.432115 439.37008 44.850393 439.37008 Z" fill="#fdf5dd"/><path d="M 44.850393 439.37008 L 181.92126 439.37008 C 186.33954 439.37008 189.92126 442.9518 189.92126 447.37008 L 189.92126 536.25197 C 189.92126 540.67024 186.33954 544.25197 181.92126 544.25197 L 44.850393 544.25197 C 40.432115 544.25197 36.850393 540.67024 36.850393 536.25197 L 36.850393 447.37008 C 36.850393 442.9518 40.432115 439.37008 44.850393 439.37008 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(41.850393 444.37008)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="32.873323" y="9" textLength="77.32422">Open vSwitch Agent</tspan></text></g><line x1="269.29134" y1="255.11811" x2="354.3307" y2="255.11811" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="354.3307" y1="255.11811" x2="439.37008" y2="354.3307" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="439.37008" y1="354.3307" x2="341.92953" y2="430.1178" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 155.90551 595.2756 C 155.90551 595.2756 182.60284 607.0826 226.77165 566.92913 C 255.61761 540.70553 283.0568 500.37315 298.62335 475.5453" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 113.385826 637.79527 C 113.385826 637.79527 170.5568 647.4751 226.77165 595.2756 C 264.63768 560.11428 290.07589 508.01375 302.64797 477.74808" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 70.86614 595.2756 C 70.86614 595.2756 124.495014 587.24008 192.7559 547.0866 C 231.65778 524.20316 264.88672 497.02072 286.27396 477.8715" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="595.2756" x2="155.90551" y2="481.88976" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="481.88976" x2="127.559054" y2="524.40945" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 127.559054 524.40945 C 127.559054 524.40945 109.843724 500.31255 121.88976 476.22047 C 133.9358 452.1284 172.48795 471.0913 170.07874 439.37008 C 169.06526 426.02597 158.872 410.61572 147.40157 397.23905" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 79.370076 397.23905 C 67.899654 410.61573 57.70639 426.02597 56.692913 439.37008 C 54.283705 471.0913 92.83585 452.1284 104.88189 476.22047 C 116.92793 500.31255 99.2126 524.40945 99.2126 524.40945" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="481.88976" x2="99.2126" y2="524.40945" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="481.88976" x2="70.86614" y2="595.2756" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><circle cx="42.519685" cy="708.6614" r="8.5039505" fill="#a57706"/><text transform="translate(54.06299 696.65354)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="325.98425" cy="680.31496" r="8.5039505" fill="#2076c8"/><text transform="translate(339.48819 672.65354)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="93.63281">Self-service network</tspan></text><g filter="url(#Shadow)"><path d="M 93.03937 623.62204 L 133.73228 623.62204 C 138.15056 623.62204 141.73228 627.20377 141.73228 631.62204 L 141.73228 643.9685 C 141.73228 648.38678 138.15056 651.9685 133.73228 651.9685 L 93.03937 651.9685 C 88.62109 651.9685 85.03937 648.38678 85.03937 643.9685 L 85.03937 631.62204 C 85.03937 627.20377 88.62109 623.62204 93.03937 623.62204 Z" fill="#d11b24"/><path d="M 93.03937 623.62204 L 133.73228 623.62204 C 138.15056 623.62204 141.73228 627.20377 141.73228 631.62204 L 141.73228 643.9685 C 141.73228 648.38678 138.15056 651.9685 133.73228 651.9685 L 93.03937 651.9685 C 88.62109 651.9685 85.03937 648.38678 85.03937 643.9685 L 85.03937 631.62204 C 85.03937 627.20377 88.62109 623.62204 93.03937 623.62204 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 632.29527)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.6062222" y="9" textLength="41.480469">Interface 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 581.10236 L 91.2126 581.10236 C 95.630876 581.10236 99.2126 584.68408 99.2126 589.10236 L 99.2126 601.4488 C 99.2126 605.8671 95.630876 609.4488 91.2126 609.4488 L 50.519685 609.4488 C 46.101407 609.4488 42.519685 605.8671 42.519685 601.4488 L 42.519685 589.10236 C 42.519685 584.68408 46.101407 581.10236 50.519685 581.10236 Z" fill="#bd3612"/><path d="M 50.519685 581.10236 L 91.2126 581.10236 C 95.630876 581.10236 99.2126 584.68408 99.2126 589.10236 L 99.2126 601.4488 C 99.2126 605.8671 95.630876 609.4488 91.2126 609.4488 L 50.519685 609.4488 C 46.101407 609.4488 42.519685 605.8671 42.519685 601.4488 L 42.519685 589.10236 C 42.519685 584.68408 46.101407 581.10236 50.519685 581.10236 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 589.7756)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 93.03937 510.23622 L 133.73228 510.23622 C 138.15056 510.23622 141.73228 513.81794 141.73228 518.23622 L 141.73228 530.58267 C 141.73228 535.00095 138.15056 538.58267 133.73228 538.58267 L 93.03937 538.58267 C 88.62109 538.58267 85.03937 535.00095 85.03937 530.58267 L 85.03937 518.23622 C 85.03937 513.81794 88.62109 510.23622 93.03937 510.23622 Z" fill="#eae3cc"/><path d="M 93.03937 510.23622 L 133.73228 510.23622 C 138.15056 510.23622 141.73228 513.81794 141.73228 518.23622 L 141.73228 530.58267 C 141.73228 535.00095 138.15056 538.58267 133.73228 538.58267 L 93.03937 538.58267 C 88.62109 538.58267 85.03937 535.00095 85.03937 530.58267 L 85.03937 518.23622 C 85.03937 513.81794 88.62109 510.23622 93.03937 510.23622 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 513.40945)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="1.8503628" y="9" textLength="23.171875">Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.862082" y="9" textLength="19.980469">ation</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="20" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 467.71653 L 91.2126 467.71653 C 95.630876 467.71653 99.2126 471.29825 99.2126 475.71653 L 99.2126 488.063 C 99.2126 492.48127 95.630876 496.063 91.2126 496.063 L 50.519685 496.063 C 46.101407 496.063 42.519685 492.48127 42.519685 488.063 L 42.519685 475.71653 C 42.519685 471.29825 46.101407 467.71653 50.519685 467.71653 Z" fill="#eae3cc"/><path d="M 50.519685 467.71653 L 91.2126 467.71653 C 95.630876 467.71653 99.2126 471.29825 99.2126 475.71653 L 99.2126 488.063 C 99.2126 492.48127 95.630876 496.063 91.2126 496.063 L 50.519685 496.063 C 46.101407 496.063 42.519685 492.48127 42.519685 488.063 L 42.519685 475.71653 C 42.519685 471.29825 46.101407 467.71653 50.519685 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 470.88976)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.1003628" y="9" textLength="8.3710938">Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.3113" y="9" textLength="4.8867188">o</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="20.037863" y="9" textLength="19.554688">vider</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="20" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 581.10236 L 176.25197 581.10236 C 180.67025 581.10236 184.25197 584.68408 184.25197 589.10236 L 184.25197 601.4488 C 184.25197 605.8671 180.67025 609.4488 176.25197 609.4488 L 135.559054 609.4488 C 131.14078 609.4488 127.559054 605.8671 127.559054 601.4488 L 127.559054 589.10236 C 127.559054 584.68408 131.14078 581.10236 135.559054 581.10236 Z" fill="#a57706"/><path d="M 135.559054 581.10236 L 176.25197 581.10236 C 180.67025 581.10236 184.25197 584.68408 184.25197 589.10236 L 184.25197 601.4488 C 184.25197 605.8671 180.67025 609.4488 176.25197 609.4488 L 135.559054 609.4488 C 131.14078 609.4488 127.559054 605.8671 127.559054 601.4488 L 127.559054 589.10236 C 127.559054 584.68408 131.14078 581.10236 135.559054 581.10236 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 589.7756)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 3</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 467.71653 L 176.25197 467.71653 C 180.67025 467.71653 184.25197 471.29825 184.25197 475.71653 L 184.25197 488.063 C 184.25197 492.48127 180.67025 496.063 176.25197 496.063 L 135.559054 496.063 C 131.14078 496.063 127.559054 492.48127 127.559054 488.063 L 127.559054 475.71653 C 127.559054 471.29825 131.14078 467.71653 135.559054 467.71653 Z" fill="#eae3cc"/><path d="M 135.559054 467.71653 L 176.25197 467.71653 C 180.67025 467.71653 184.25197 471.29825 184.25197 475.71653 L 184.25197 488.063 C 184.25197 492.48127 180.67025 496.063 176.25197 496.063 L 135.559054 496.063 C 131.14078 496.063 127.559054 492.48127 127.559054 488.063 L 127.559054 475.71653 C 127.559054 471.29825 131.14078 467.71653 135.559054 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 470.88976)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.244894" y="9" textLength="4.5273438">T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.3738" y="9" textLength="22.074219">unnel</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="20" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 240.94488 L 374.67716 240.94488 C 379.09544 240.94488 382.67716 244.5266 382.67716 248.94488 L 382.67716 261.29134 C 382.67716 265.70962 379.09544 269.29134 374.67716 269.29134 L 333.98425 269.29134 C 329.56597 269.29134 325.98425 265.70962 325.98425 261.29134 L 325.98425 248.94488 C 325.98425 244.5266 329.56597 240.94488 333.98425 240.94488 Z" fill="#eae3cc"/><path d="M 333.98425 240.94488 L 374.67716 240.94488 C 379.09544 240.94488 382.67716 244.5266 382.67716 248.94488 L 382.67716 261.29134 C 382.67716 265.70962 379.09544 269.29134 374.67716 269.29134 L 333.98425 269.29134 C 329.56597 269.29134 325.98425 265.70962 325.98425 261.29134 L 325.98425 248.94488 C 325.98425 244.5266 329.56597 240.94488 333.98425 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 244.11811)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.244894" y="9" textLength="4.5273438">T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.3738" y="9" textLength="22.074219">unnel</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="20" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 419.02362 340.15748 L 459.71653 340.15748 C 464.1348 340.15748 467.71653 343.7392 467.71653 348.15748 L 467.71653 360.50393 C 467.71653 364.92221 464.1348 368.50393 459.71653 368.50393 L 419.02362 368.50393 C 414.60534 368.50393 411.02362 364.92221 411.02362 360.50393 L 411.02362 348.15748 C 411.02362 343.7392 414.60534 340.15748 419.02362 340.15748 Z" fill="#a57706"/><path d="M 419.02362 340.15748 L 459.71653 340.15748 C 464.1348 340.15748 467.71653 343.7392 467.71653 348.15748 L 467.71653 360.50393 C 467.71653 364.92221 464.1348 368.50393 459.71653 368.50393 L 419.02362 368.50393 C 414.60534 368.50393 411.02362 364.92221 411.02362 360.50393 L 411.02362 348.15748 C 411.02362 343.7392 414.60534 340.15748 419.02362 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(416.02362 348.8307)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 3</tspan></text></g><path d="M 269.90022 456.58828 C 251.5748 453.5433 258.88252 427.9096 288.11565 432.28346 C 290.82784 423.75741 324.82205 425.1413 324.5998 432.28346 C 345.91521 423.14853 373.15502 441.3634 354.88403 450.49833 C 376.80831 454.92718 354.60737 478.78923 336.61417 474.80315 C 335.17417 481.447 303.00775 483.77197 300.18444 474.80315 C 281.97014 484.38141 243.99042 469.6543 269.90022 456.58828 Z" fill="#fdf5dd"/><path d="M 269.90022 456.58828 C 251.5748 453.5433 258.88252 427.9096 288.11565 432.28346 C 290.82784 423.75741 324.82205 425.1413 324.5998 432.28346 C 345.91521 423.14853 373.15502 441.3634 354.88403 450.49833 C 376.80831 454.92718 354.60737 478.78923 336.61417 474.80315 C 335.17417 481.447 303.00775 483.77197 300.18444 474.80315 C 281.97014 484.38141 243.99042 469.6543 269.90022 456.58828 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(277.12598 442.5433)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".25144539" y="9" textLength="70.945312"> Physical Network </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.685039" y="20" textLength="13.871094">Infr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.395977" y="20" textLength="35.839844">astructur</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="57.075664" y="20" textLength="4.609375">e</tspan></text><g filter="url(#Shadow)"><path d="M 413.35433 85.03937 L 465.38583 85.03937 C 469.8041 85.03937 473.38583 88.62109 473.38583 93.03937 L 473.38583 139.40157 C 473.38583 143.81985 469.8041 147.40157 465.38583 147.40157 L 413.35433 147.40157 C 408.93605 147.40157 405.35433 143.81985 405.35433 139.40157 L 405.35433 93.03937 C 405.35433 88.62109 408.93605 85.03937 413.35433 85.03937 Z" fill="#fdf5dd"/><path d="M 413.35433 85.03937 L 465.38583 85.03937 C 469.8041 85.03937 473.38583 88.62109 473.38583 93.03937 L 473.38583 139.40157 C 473.38583 143.81985 469.8041 147.40157 465.38583 147.40157 L 413.35433 147.40157 C 408.93605 147.40157 405.35433 143.81985 405.35433 139.40157 L 405.35433 93.03937 C 405.35433 88.62109 408.93605 85.03937 413.35433 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(410.35433 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="5.7559836" y="9" textLength="46.51953">DHCP Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 413.35433 212.59842 L 465.38582 212.59842 C 469.8041 212.59842 473.38582 216.18015 473.38582 220.59842 L 473.38582 272.62992 C 473.38582 277.0482 469.8041 280.62992 465.38582 280.62992 L 413.35433 280.62992 C 408.93605 280.62992 405.35433 277.0482 405.35433 272.62992 L 405.35433 220.59842 C 405.35433 216.18015 408.93605 212.59842 413.35433 212.59842 Z" fill="#fdf5dd"/><path d="M 413.35433 212.59842 L 465.38582 212.59842 C 469.8041 212.59842 473.38582 216.18015 473.38582 220.59842 L 473.38582 272.62992 C 473.38582 277.0482 469.8041 280.62992 465.38582 280.62992 L 413.35433 280.62992 C 408.93605 280.62992 405.35433 277.0482 405.35433 272.62992 L 405.35433 220.59842 C 405.35433 216.18015 408.93605 212.59842 413.35433 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(410.35433 217.59842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.421999" y="9" textLength="39.265625">Metadata </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.730593" y="20" textLength="22.570312">Agent</tspan></text></g><path d="M 439.37008 260.7874 C 439.37008 260.7874 473.38582 236.16945 473.38582 198.4252 C 473.38582 160.68094 439.37008 127.559054 439.37008 127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 419.02362 246.61417 L 459.71653 246.61417 C 464.1348 246.61417 467.71653 250.1959 467.71653 254.61417 L 467.71653 266.96063 C 467.71653 271.3789 464.1348 274.96063 459.71653 274.96063 L 419.02362 274.96063 C 414.60534 274.96063 411.02362 271.3789 411.02362 266.96063 L 411.02362 254.61417 C 411.02362 250.1959 414.60534 246.61417 419.02362 246.61417 Z" fill="#eae3cc"/><path d="M 419.02362 246.61417 L 459.71653 246.61417 C 464.1348 246.61417 467.71653 250.1959 467.71653 254.61417 L 467.71653 266.96063 C 467.71653 271.3789 464.1348 274.96063 459.71653 274.96063 L 419.02362 274.96063 C 414.60534 274.96063 411.02362 271.3789 411.02362 266.96063 L 411.02362 254.61417 C 411.02362 250.1959 414.60534 246.61417 419.02362 246.61417 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(416.02362 249.7874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="4.7527065" y="9" textLength="37.1875">Metadata</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.617941" y="20" textLength="8.3710938">Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.828878" y="20" textLength="21.246094">ocess</tspan></text></g><path d="M 320.31495 161.36781 C 307.44017 171.67965 293.48893 184.55816 283.46457 198.4252 C 259.37249 231.75257 269.29134 255.11811 269.29134 255.11811" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 439.37008 127.559054 C 439.37008 127.559054 425.19203 176.46031 377.00787 212.59842 C 328.82371 248.73654 269.29134 255.11811 269.29134 255.11811" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 419.02362 113.385826 L 459.71653 113.385826 C 464.1348 113.385826 467.71653 116.96755 467.71653 121.385826 L 467.71653 133.73228 C 467.71653 138.15056 464.1348 141.73228 459.71653 141.73228 L 419.02362 141.73228 C 414.60534 141.73228 411.02362 138.15056 411.02362 133.73228 L 411.02362 121.385826 C 411.02362 116.96755 414.60534 113.385826 419.02362 113.385826 Z" fill="#eae3cc"/><path d="M 419.02362 113.385826 L 459.71653 113.385826 C 464.1348 113.385826 467.71653 116.96755 467.71653 121.385826 L 467.71653 133.73228 C 467.71653 138.15056 464.1348 141.73228 459.71653 141.73228 L 419.02362 141.73228 C 414.60534 141.73228 411.02362 138.15056 411.02362 133.73228 L 411.02362 121.385826 C 411.02362 116.96755 414.60534 113.385826 419.02362 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(416.02362 116.559054)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="12.41091" y="9" textLength="23.949219">DHCP </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 240.94488 L 289.6378 240.94488 C 294.05607 240.94488 297.6378 244.5266 297.6378 248.94488 L 297.6378 261.29134 C 297.6378 265.70962 294.05607 269.29134 289.6378 269.29134 L 248.94488 269.29134 C 244.5266 269.29134 240.94488 265.70962 240.94488 261.29134 L 240.94488 248.94488 C 240.94488 244.5266 244.5266 240.94488 248.94488 240.94488 Z" fill="#eae3cc"/><path d="M 248.94488 240.94488 L 289.6378 240.94488 C 294.05607 240.94488 297.6378 244.5266 297.6378 248.94488 L 297.6378 261.29134 C 297.6378 265.70962 294.05607 269.29134 289.6378 269.29134 L 248.94488 269.29134 C 244.5266 269.29134 240.94488 265.70962 240.94488 261.29134 L 240.94488 248.94488 C 240.94488 244.5266 244.5266 240.94488 248.94488 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 244.11811)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="1.8503628" y="9" textLength="23.171875">Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.862082" y="9" textLength="19.980469">ation</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="20" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 87.37008 311.81102 L 139.40158 311.81102 C 143.819855 311.81102 147.40158 315.39274 147.40158 319.81102 L 147.40158 408.6929 C 147.40158 413.1112 143.819855 416.6929 139.40158 416.6929 L 87.37008 416.6929 C 82.9518 416.6929 79.37008 413.1112 79.37008 408.6929 L 79.37008 319.81102 C 79.37008 315.39274 82.9518 311.81102 87.37008 311.81102 Z" fill="#fdf5dd"/><path d="M 87.37008 311.81102 L 139.40158 311.81102 C 143.819855 311.81102 147.40158 315.39274 147.40158 319.81102 L 147.40158 408.6929 C 147.40158 413.1112 143.819855 416.6929 139.40158 416.6929 L 87.37008 416.6929 C 82.9518 416.6929 79.37008 413.1112 79.37008 408.6929 L 79.37008 319.81102 C 79.37008 315.39274 82.9518 311.81102 87.37008 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(84.37008 316.81102)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.4591086" y="9" textLength="53.11328">Layer-3 Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 93.03937 340.15748 L 133.73228 340.15748 C 138.15056 340.15748 141.73228 343.7392 141.73228 348.15748 L 141.73228 360.50393 C 141.73228 364.92221 138.15056 368.50393 133.73228 368.50393 L 93.03937 368.50393 C 88.62109 368.50393 85.03937 364.92221 85.03937 360.50393 L 85.03937 348.15748 C 85.03937 343.7392 88.62109 340.15748 93.03937 340.15748 Z" fill="#eae3cc"/><path d="M 93.03937 340.15748 L 133.73228 340.15748 C 138.15056 340.15748 141.73228 343.7392 141.73228 348.15748 L 141.73228 360.50393 C 141.73228 364.92221 138.15056 368.50393 133.73228 368.50393 L 93.03937 368.50393 C 88.62109 368.50393 85.03937 364.92221 85.03937 360.50393 L 85.03937 348.15748 C 85.03937 343.7392 88.62109 340.15748 93.03937 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 343.3307)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.2058315" y="9" textLength="26.28125">Router</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g><g filter="url(#Shadow)"><path d="M 93.03937 382.67716 L 133.73228 382.67716 C 138.15056 382.67716 141.73228 386.25889 141.73228 390.67716 L 141.73228 403.02362 C 141.73228 407.4419 138.15056 411.02362 133.73228 411.02362 L 93.03937 411.02362 C 88.62109 411.02362 85.03937 407.4419 85.03937 403.02362 L 85.03937 390.67716 C 85.03937 386.25889 88.62109 382.67716 93.03937 382.67716 Z" fill="#eae3cc"/><path d="M 93.03937 382.67716 L 133.73228 382.67716 C 138.15056 382.67716 141.73228 386.25889 141.73228 390.67716 L 141.73228 403.02362 C 141.73228 407.4419 138.15056 411.02362 133.73228 411.02362 L 93.03937 411.02362 C 88.62109 411.02362 85.03937 407.4419 85.03937 403.02362 L 85.03937 390.67716 C 85.03937 386.25889 88.62109 382.67716 93.03937 382.67716 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 385.8504)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="13.385519" y="9" textLength="15.953125">SNA</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="28.78005" y="9" textLength="4.5273438">T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g><line x1="344.20697" y1="340.15748" x2="311.81102" y2="294.80315" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 311.81102 294.80315 C 311.81102 294.80315 384.80435 299.68845 396.8504 255.11811 C 402.12285 235.61001 394.6342 210.6426 384.54523 188.73212" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 291.46457 280.62992 L 332.15748 280.62992 C 336.57576 280.62992 340.15748 284.21164 340.15748 288.62992 L 340.15748 300.97638 C 340.15748 305.39465 336.57576 308.97638 332.15748 308.97638 L 291.46457 308.97638 C 287.04629 308.97638 283.46457 305.39465 283.46457 300.97638 L 283.46457 288.62992 C 283.46457 284.21164 287.04629 280.62992 291.46457 280.62992 Z" fill="#eae3cc"/><path d="M 291.46457 280.62992 L 332.15748 280.62992 C 336.57576 280.62992 340.15748 284.21164 340.15748 288.62992 L 340.15748 300.97638 C 340.15748 305.39465 336.57576 308.97638 332.15748 308.97638 L 291.46457 308.97638 C 287.04629 308.97638 283.46457 305.39465 283.46457 300.97638 L 283.46457 288.62992 C 283.46457 284.21164 287.04629 280.62992 291.46457 280.62992 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 283.80315)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.1003628" y="9" textLength="8.3710938">Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.3113" y="9" textLength="4.8867188">o</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="20.037863" y="9" textLength="19.554688">vider</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="20" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 328.31496 85.03937 L 380.34645 85.03937 C 384.76473 85.03937 388.34645 88.62109 388.34645 93.03937 L 388.34645 181.92126 C 388.34645 186.33954 384.76473 189.92126 380.34645 189.92126 L 328.31496 189.92126 C 323.89668 189.92126 320.31496 186.33954 320.31496 181.92126 L 320.31496 93.03937 C 320.31496 88.62109 323.89668 85.03937 328.31496 85.03937 Z" fill="#fdf5dd"/><path d="M 328.31496 85.03937 L 380.34645 85.03937 C 384.76473 85.03937 388.34645 88.62109 388.34645 93.03937 L 388.34645 181.92126 C 388.34645 186.33954 384.76473 189.92126 380.34645 189.92126 L 328.31496 189.92126 C 323.89668 189.92126 320.31496 186.33954 320.31496 181.92126 L 320.31496 93.03937 C 320.31496 88.62109 323.89668 85.03937 328.31496 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(325.31496 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.4591086" y="9" textLength="53.11328">Layer-3 Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 113.385826 L 374.67716 113.385826 C 379.09544 113.385826 382.67716 116.96755 382.67716 121.385826 L 382.67716 133.73228 C 382.67716 138.15056 379.09544 141.73228 374.67716 141.73228 L 333.98425 141.73228 C 329.56597 141.73228 325.98425 138.15056 325.98425 133.73228 L 325.98425 121.385826 C 325.98425 116.96755 329.56597 113.385826 333.98425 113.385826 Z" fill="#eae3cc"/><path d="M 333.98425 113.385826 L 374.67716 113.385826 C 379.09544 113.385826 382.67716 116.96755 382.67716 121.385826 L 382.67716 133.73228 C 382.67716 138.15056 379.09544 141.73228 374.67716 141.73228 L 333.98425 141.73228 C 329.56597 141.73228 325.98425 138.15056 325.98425 133.73228 L 325.98425 121.385826 C 325.98425 116.96755 329.56597 113.385826 333.98425 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 116.559054)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="1.5964565" y="9" textLength="43.5">Dist Router</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 155.90551 L 374.67716 155.90551 C 379.09544 155.90551 382.67716 159.48723 382.67716 163.90551 L 382.67716 176.25197 C 382.67716 180.67025 379.09544 184.25197 374.67716 184.25197 L 333.98425 184.25197 C 329.56597 184.25197 325.98425 180.67025 325.98425 176.25197 L 325.98425 163.90551 C 325.98425 159.48723 329.56597 155.90551 333.98425 155.90551 Z" fill="#eae3cc"/><path d="M 333.98425 155.90551 L 374.67716 155.90551 C 379.09544 155.90551 382.67716 159.48723 382.67716 163.90551 L 382.67716 176.25197 C 382.67716 180.67025 379.09544 184.25197 374.67716 184.25197 L 333.98425 184.25197 C 329.56597 184.25197 325.98425 180.67025 325.98425 176.25197 L 325.98425 163.90551 C 325.98425 159.48723 329.56597 155.90551 333.98425 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 159.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.166769" y="9" textLength="40.359375">Floating IP</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g></g></g></svg>
