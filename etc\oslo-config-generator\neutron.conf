[DEFAULT]
output_file = etc/neutron.conf.sample
wrap_width = 79

namespace = neutron
namespace = neutron.agent
namespace = neutron.db
namespace = neutron.extensions
namespace = nova.auth
namespace = oslo.log
namespace = oslo.db
namespace = oslo.policy
namespace = oslo.concurrency
namespace = oslo.messaging
namespace = oslo.middleware.cors
namespace = oslo.middleware.http_proxy_to_wsgi
namespace = oslo.service.sslutils
namespace = oslo.service.wsgi
namespace = keystonemiddleware.auth_token
