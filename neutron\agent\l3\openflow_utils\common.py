# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr

from neutron_lib import constants as lib_constants
from oslo_log import log as logging

from neutron.common import utils as common_utils

LOG = logging.getLogger(__name__)


class OpenFlowCommonRouterMixin(object):

    def _get_router_subnets(self):
        subnets = []
        for interface in self.router.get('_interfaces', []):
            for subnet in interface.get('subnets', []):
                subnets.append(subnet)
        return subnets

    def _port_has_ipv6_subnet(self, port):
        if 'subnets' in port:
            for subnet in port['subnets']:
                if (netaddr.IPNetwork(subnet['cidr']).version == 6 and
                        subnet['cidr'] !=
                        lib_constants.PROVISIONAL_IPV6_PD_PREFIX):
                    return True

    def _get_internal_port(self, subnet_id):
        """Return internal router port based on subnet_id."""
        router_ports = self.router.get(lib_constants.INTERFACE_KEY, [])
        for port in router_ports:
            fips = port['fixed_ips']
            for f in fips:
                if f['subnet_id'] == subnet_id:
                    return port

    def _get_gw_ips_cidr(self):
        gw_cidrs = set()
        ex_gw_port = self.get_ex_gw_port()
        if ex_gw_port:
            for ip_addr in ex_gw_port['fixed_ips']:
                ex_gw_ip = ip_addr['ip_address']
                addr = netaddr.IPAddress(ex_gw_ip)
                if addr.version == lib_constants.IP_VERSION_4:
                    gw_cidrs.add(common_utils.ip_to_cidr(ex_gw_ip))
        return gw_cidrs
