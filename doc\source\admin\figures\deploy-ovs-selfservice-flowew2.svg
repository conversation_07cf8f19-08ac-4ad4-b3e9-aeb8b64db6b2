<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 383 684" width="383pt" height="57pc" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:10:04 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><text transform="translate(44.68504 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".12890625" y="17" textLength="274.74219">Open vSwitch - Self-service Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="17.204102" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="74.157227" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="78.81543" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="96.674805" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="121.74121" y="35" textLength="58.253906">w - East/W</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="179.75488" y="35" textLength="78.041016">est Scenario 2</tspan></text><path d="M 36.346457 56.692913 L 332.15748 56.692913 C 336.57576 56.692913 340.15748 60.274635 340.15748 64.692913 L 340.15748 366.17323 C 340.15748 370.5915 336.57576 374.17323 332.15748 374.17323 L 36.346457 374.17323 C 31.928179 374.17323 28.346457 370.5915 28.346457 366.17323 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="108.3225" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.6003628" y="9" textLength="39.492188">Instance 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" fill="#fdf5dd"/><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="127.559054" x2="170.07874" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="127.559054" x2="212.59842" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="127.559054" x2="255.11811" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" fill="#2076c8"/><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" fill="#2076c8"/><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 255.11811 L 133.73228 255.11811 C 138.15056 255.11811 141.73228 258.69983 141.73228 263.11811 L 141.73228 309.48031 C 141.73228 313.8986 138.15056 317.48031 133.73228 317.48031 L 50.519685 317.48031 C 46.101407 317.48031 42.519685 313.8986 42.519685 309.48031 L 42.519685 263.11811 C 42.519685 258.69983 46.101407 255.11811 50.519685 255.11811 Z" fill="#fdf5dd"/><path d="M 50.519685 255.11811 L 133.73228 255.11811 C 138.15056 255.11811 141.73228 258.69983 141.73228 263.11811 L 141.73228 309.48031 C 141.73228 313.8986 138.15056 317.48031 133.73228 317.48031 L 50.519685 317.48031 C 46.101407 317.48031 42.519685 313.8986 42.519685 309.48031 L 42.519685 263.11811 C 42.519685 258.69983 46.101407 255.11811 50.519685 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 260.11811)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.088721" y="9" textLength="8.375"> O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.385596" y="9" textLength="15.980469">VS T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="31.967627" y="9" textLength="49.15625">unnel Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="33.97129" y="18" textLength="21.27002">br-tun</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 255.11811 L 317.98425 255.11811 C 322.40253 255.11811 325.98425 258.69983 325.98425 263.11811 L 325.98425 309.48031 C 325.98425 313.8986 322.40253 317.48031 317.98425 317.48031 L 192.25197 317.48031 C 187.83369 317.48031 184.25197 313.8986 184.25197 309.48031 L 184.25197 263.11811 C 184.25197 258.69983 187.83369 255.11811 192.25197 255.11811 Z" fill="#fdf5dd"/><path d="M 192.25197 255.11811 L 317.98425 255.11811 C 322.40253 255.11811 325.98425 258.69983 325.98425 263.11811 L 325.98425 309.48031 C 325.98425 313.8986 322.40253 317.48031 317.98425 317.48031 L 192.25197 317.48031 C 187.83369 317.48031 184.25197 313.8986 184.25197 309.48031 L 184.25197 263.11811 C 184.25197 258.69983 187.83369 255.11811 192.25197 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 260.11811)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.993094" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="28.211844" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="62.67669" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="56.47698" y="18" textLength="18.77832">br-int</tspan></text></g><path d="M 255.11811 127.559054 C 255.11811 127.559054 313.93821 121.89458 325.98425 170.07874 C 334.89714 205.73031 315.10227 258.45743 304.2021 283.47397" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="340.15748" x2="70.86614" y2="297.6378" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" fill="#2076c8"/><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 283.46457 L 77.03937 283.46457 C 81.45765 283.46457 85.03937 287.04629 85.03937 291.46457 L 85.03937 303.81102 C 85.03937 308.2293 81.45765 311.81102 77.03937 311.81102 L 64.692913 311.81102 C 60.274635 311.81102 56.692913 308.2293 56.692913 303.81102 L 56.692913 291.46457 C 56.692913 287.04629 60.274635 283.46457 64.692913 283.46457 Z" fill="#a57706"/><path d="M 64.692913 283.46457 L 77.03937 283.46457 C 81.45765 283.46457 85.03937 287.04629 85.03937 291.46457 L 85.03937 303.81102 C 85.03937 308.2293 81.45765 311.81102 77.03937 311.81102 L 64.692913 311.81102 C 60.274635 311.81102 56.692913 308.2293 56.692913 303.81102 L 56.692913 291.46457 C 56.692913 287.04629 60.274635 283.46457 64.692913 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 286.6378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(25)</tspan></text></g><line x1="70.86614" y1="354.3307" x2="368.50394" y2="396.8504" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 64.692913 340.15748 L 77.03937 340.15748 C 81.45765 340.15748 85.03937 343.7392 85.03937 348.15748 L 85.03937 360.50393 C 85.03937 364.92221 81.45765 368.50393 77.03937 368.50393 L 64.692913 368.50393 C 60.274635 368.50393 56.692913 364.92221 56.692913 360.50393 L 56.692913 348.15748 C 56.692913 343.7392 60.274635 340.15748 64.692913 340.15748 Z" fill="#a57706"/><path d="M 64.692913 340.15748 L 77.03937 340.15748 C 81.45765 340.15748 85.03937 343.7392 85.03937 348.15748 L 85.03937 360.50393 C 85.03937 364.92221 81.45765 368.50393 77.03937 368.50393 L 64.692913 368.50393 C 60.274635 368.50393 56.692913 364.92221 56.692913 360.50393 L 56.692913 348.15748 C 56.692913 343.7392 60.274635 340.15748 64.692913 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 343.3307)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(24)</tspan></text></g><line x1="212.59842" y1="297.6378" x2="113.385826" y2="297.6378" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 206.4252 283.46457 L 218.77165 283.46457 C 223.18993 283.46457 226.77165 287.04629 226.77165 291.46457 L 226.77165 303.81102 C 226.77165 308.2293 223.18993 311.81102 218.77165 311.81102 L 206.4252 311.81102 C 202.00692 311.81102 198.4252 308.2293 198.4252 303.81102 L 198.4252 291.46457 C 198.4252 287.04629 202.00692 283.46457 206.4252 283.46457 Z" fill="#708284"/><path d="M 206.4252 283.46457 L 218.77165 283.46457 C 223.18993 283.46457 226.77165 287.04629 226.77165 291.46457 L 226.77165 303.81102 C 226.77165 308.2293 223.18993 311.81102 218.77165 311.81102 L 206.4252 311.81102 C 202.00692 311.81102 198.4252 308.2293 198.4252 303.81102 L 198.4252 291.46457 C 198.4252 287.04629 202.00692 283.46457 206.4252 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 286.6378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(27)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 283.46457 L 119.559054 283.46457 C 123.97733 283.46457 127.559054 287.04629 127.559054 291.46457 L 127.559054 303.81102 C 127.559054 308.2293 123.97733 311.81102 119.559054 311.81102 L 107.2126 311.81102 C 102.79432 311.81102 99.2126 308.2293 99.2126 303.81102 L 99.2126 291.46457 C 99.2126 287.04629 102.79432 283.46457 107.2126 283.46457 Z" fill="#708284"/><path d="M 107.2126 283.46457 L 119.559054 283.46457 C 123.97733 283.46457 127.559054 287.04629 127.559054 291.46457 L 127.559054 303.81102 C 127.559054 308.2293 123.97733 311.81102 119.559054 311.81102 L 107.2126 311.81102 C 102.79432 311.81102 99.2126 308.2293 99.2126 303.81102 L 99.2126 291.46457 C 99.2126 287.04629 102.79432 283.46457 107.2126 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 286.6378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(26)</tspan></text></g><circle cx="184.25197" cy="623.62204" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 611.61417)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(56.897637 611.61417)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="42.519685" cy="623.62204" r="8.5039505" fill="#a57706"/><text transform="translate(311.0736 377.53438) rotate(8.1301023)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#595ab7" x=".087890625" y="20" textLength="28.824219">VNI 102</tspan></text><path d="M 353.83515 398.37288 C 347.42126 396.8504 349.97896 384.03354 360.21056 386.22047 C 361.15982 381.95745 373.0578 382.64938 372.98 386.22047 C 380.4404 381.653 389.97433 390.76044 383.57949 395.3279 C 391.25299 397.54233 383.48266 409.47335 377.18504 407.48031 C 376.68104 410.80223 365.42279 411.96472 364.43463 407.48031 C 358.05963 412.26945 344.76673 404.90589 353.83515 398.37288 Z" fill="#a57706"/><path d="M 353.83515 398.37288 C 347.42126 396.8504 349.97896 384.03354 360.21056 386.22047 C 361.15982 381.95745 373.0578 382.64938 372.98 386.22047 C 380.4404 381.653 389.97433 390.76044 383.57949 395.3279 C 391.25299 397.54233 383.48266 409.47335 377.18504 407.48031 C 376.68104 410.80223 365.42279 411.96472 364.43463 407.48031 C 358.05963 412.26945 344.76673 404.90589 353.83515 398.37288 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(359.61417 385.8504)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="9" textLength="14.203125">(10)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="20" textLength="14.203125">(23)</tspan></text><path d="M 36.346457 411.02362 L 332.15748 411.02362 C 336.57576 411.02362 340.15748 414.60534 340.15748 419.02362 L 340.15748 584.44094 C 340.15748 588.85922 336.57576 592.44094 332.15748 592.44094 L 36.346457 592.44094 C 31.928179 592.44094 28.346457 588.85922 28.346457 584.44094 L 28.346457 419.02362 C 28.346457 414.60534 31.928179 411.02362 36.346457 411.02362 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 416.02362)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="110.259027" y="13" textLength="81.29297">Network Node</tspan></text><text transform="translate(348.69651 444.3651) rotate(-72.539755)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#595ab7" x=".087890625" y="20" textLength="28.824219">VNI 102</tspan></text><g filter="url(#Shadow)"><path d="M 93.03937 524.40945 L 218.77165 524.40945 C 223.18993 524.40945 226.77165 527.99117 226.77165 532.40945 L 226.77165 578.77165 C 226.77165 583.18993 223.18993 586.77165 218.77165 586.77165 L 93.03937 586.77165 C 88.62109 586.77165 85.03937 583.18993 85.03937 578.77165 L 85.03937 532.40945 C 85.03937 527.99117 88.62109 524.40945 93.03937 524.40945 Z" fill="#fdf5dd"/><path d="M 93.03937 524.40945 L 218.77165 524.40945 C 223.18993 524.40945 226.77165 527.99117 226.77165 532.40945 L 226.77165 578.77165 C 226.77165 583.18993 223.18993 586.77165 218.77165 586.77165 L 93.03937 586.77165 C 88.62109 586.77165 85.03937 583.18993 85.03937 578.77165 L 85.03937 532.40945 C 85.03937 527.99117 88.62109 524.40945 93.03937 524.40945 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 529.40945)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.993094" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="28.211844" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="62.67669" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="56.47698" y="18" textLength="18.77832">br-int</tspan></text></g><g filter="url(#Shadow)"><path d="M 178.07874 439.37008 L 261.29134 439.37008 C 265.70962 439.37008 269.29134 442.9518 269.29134 447.37008 L 269.29134 493.73228 C 269.29134 498.15056 265.70962 501.73228 261.29134 501.73228 L 178.07874 501.73228 C 173.66046 501.73228 170.07874 498.15056 170.07874 493.73228 L 170.07874 447.37008 C 170.07874 442.9518 173.66046 439.37008 178.07874 439.37008 Z" fill="#fdf5dd"/><path d="M 178.07874 439.37008 L 261.29134 439.37008 C 265.70962 439.37008 269.29134 442.9518 269.29134 447.37008 L 269.29134 493.73228 C 269.29134 498.15056 265.70962 501.73228 261.29134 501.73228 L 178.07874 501.73228 C 173.66046 501.73228 170.07874 498.15056 170.07874 493.73228 L 170.07874 447.37008 C 170.07874 442.9518 173.66046 439.37008 178.07874 439.37008 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(175.07874 444.37008)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="9.1277833" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.346533" y="9" textLength="15.980469">VS T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="30.928565" y="9" textLength="49.15625">unnel Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="33.97129" y="18" textLength="21.27002">br-tun</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 439.37007 L 133.73228 439.37007 C 138.15056 439.37007 141.73228 442.9518 141.73228 447.37007 L 141.73228 493.73228 C 141.73228 498.15056 138.15056 501.73228 133.73228 501.73228 L 50.519685 501.73228 C 46.101407 501.73228 42.519685 498.15056 42.519685 493.73228 L 42.519685 447.37007 C 42.519685 442.9518 46.101407 439.37007 50.519685 439.37007 Z" fill="#fdf5dd"/><path d="M 50.519685 439.37007 L 133.73228 439.37007 C 138.15056 439.37007 141.73228 442.9518 141.73228 447.37007 L 141.73228 493.73228 C 141.73228 498.15056 138.15056 501.73228 133.73228 501.73228 L 50.519685 501.73228 C 46.101407 501.73228 42.519685 498.15056 42.519685 493.73228 L 42.519685 447.37007 C 42.519685 442.9518 46.101407 439.37007 50.519685 439.37007 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 444.37007)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.7996583" y="9" textLength="73.61328">Router Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="31.725684" y="18" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.964942" y="18" textLength="18.521973">outer</tspan></text></g><path d="M 155.90551 566.92913 C 155.90551 566.92913 149.71517 561.58966 136.06299 552.7559 C 122.41081 543.92214 114.14109 555.82476 107.716535 535.74803 C 101.29198 515.6713 113.385826 481.88976 113.385826 481.88976" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="113.385826" y1="566.92913" x2="70.86614" y2="481.88976" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 198.4252 566.92913 C 198.4252 566.92913 204.09449 554.1708 204.09449 530.07874 C 204.09449 505.98666 198.4252 481.88976 198.4252 481.88976" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="243.77953" y1="481.88976" x2="311.81102" y2="481.88976" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 368.11008 410.22006 C 367.07848 421.9192 363.8713 439.2324 354.3307 453.5433 C 338.26932 477.63538 311.81102 481.88976 311.81102 481.88976" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 107.2126 552.7559 L 119.559054 552.7559 C 123.97733 552.7559 127.559054 556.33762 127.559054 560.7559 L 127.559054 573.10236 C 127.559054 577.52064 123.97733 581.10236 119.559054 581.10236 L 107.2126 581.10236 C 102.79432 581.10236 99.2126 577.52064 99.2126 573.10236 L 99.2126 560.7559 C 99.2126 556.33762 102.79432 552.7559 107.2126 552.7559 Z" fill="#5959b7"/><path d="M 107.2126 552.7559 L 119.559054 552.7559 C 123.97733 552.7559 127.559054 556.33762 127.559054 560.7559 L 127.559054 573.10236 C 127.559054 577.52064 123.97733 581.10236 119.559054 581.10236 L 107.2126 581.10236 C 102.79432 581.10236 99.2126 577.52064 99.2126 573.10236 L 99.2126 560.7559 C 99.2126 556.33762 102.79432 552.7559 107.2126 552.7559 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 561.42913)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(18)</tspan></text></g><g filter="url(#Shadow)"><path d="M 305.6378 467.71653 L 317.98425 467.71653 C 322.40253 467.71653 325.98425 471.29825 325.98425 475.71653 L 325.98425 488.063 C 325.98425 492.48127 322.40253 496.063 317.98425 496.063 L 305.6378 496.063 C 301.21952 496.063 297.6378 492.48127 297.6378 488.063 L 297.6378 475.71653 C 297.6378 471.29825 301.21952 467.71653 305.6378 467.71653 Z" fill="#a57706"/><path d="M 305.6378 467.71653 L 317.98425 467.71653 C 322.40253 467.71653 325.98425 471.29825 325.98425 475.71653 L 325.98425 488.063 C 325.98425 492.48127 322.40253 496.063 317.98425 496.063 L 305.6378 496.063 C 301.21952 496.063 297.6378 492.48127 297.6378 488.063 L 297.6378 475.71653 C 297.6378 471.29825 301.21952 467.71653 305.6378 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(302.6378 470.88976)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(22)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 467.71653 L 204.59842 467.71653 C 209.0167 467.71653 212.59842 471.29825 212.59842 475.71653 L 212.59842 488.063 C 212.59842 492.48127 209.0167 496.063 204.59842 496.063 L 192.25197 496.063 C 187.83369 496.063 184.25197 492.48127 184.25197 488.063 L 184.25197 475.71653 C 184.25197 471.29825 187.83369 467.71653 192.25197 467.71653 Z" fill="#708284"/><path d="M 192.25197 467.71653 L 204.59842 467.71653 C 209.0167 467.71653 212.59842 471.29825 212.59842 475.71653 L 212.59842 488.063 C 212.59842 492.48127 209.0167 496.063 204.59842 496.063 L 192.25197 496.063 C 187.83369 496.063 184.25197 492.48127 184.25197 488.063 L 184.25197 475.71653 C 184.25197 471.29825 187.83369 467.71653 192.25197 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 470.88976)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(13)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(20)</tspan></text></g><g filter="url(#Shadow)"><path d="M 237.6063 467.71653 L 249.95275 467.71653 C 254.37103 467.71653 257.95275 471.29825 257.95275 475.71653 L 257.95275 488.063 C 257.95275 492.48127 254.37103 496.063 249.95275 496.063 L 237.6063 496.063 C 233.18802 496.063 229.6063 492.48127 229.6063 488.063 L 229.6063 475.71653 C 229.6063 471.29825 233.18802 467.71653 237.6063 467.71653 Z" fill="#a57706"/><path d="M 237.6063 467.71653 L 249.95275 467.71653 C 254.37103 467.71653 257.95275 471.29825 257.95275 475.71653 L 257.95275 488.063 C 257.95275 492.48127 254.37103 496.063 249.95275 496.063 L 237.6063 496.063 C 233.18802 496.063 229.6063 492.48127 229.6063 488.063 L 229.6063 475.71653 C 229.6063 471.29825 233.18802 467.71653 237.6063 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(234.6063 470.88976)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(21)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 467.71653 L 119.559054 467.71653 C 123.97733 467.71653 127.559054 471.29825 127.559054 475.71653 L 127.559054 488.063 C 127.559054 492.48127 123.97733 496.063 119.559054 496.063 L 107.2126 496.063 C 102.79432 496.063 99.2126 492.48127 99.2126 488.063 L 99.2126 475.71653 C 99.2126 471.29825 102.79432 467.71653 107.2126 467.71653 Z" fill="#2076c8"/><path d="M 107.2126 467.71653 L 119.559054 467.71653 C 123.97733 467.71653 127.559054 471.29825 127.559054 475.71653 L 127.559054 488.063 C 127.559054 492.48127 123.97733 496.063 119.559054 496.063 L 107.2126 496.063 C 102.79432 496.063 99.2126 492.48127 99.2126 488.063 L 99.2126 475.71653 C 99.2126 471.29825 102.79432 467.71653 107.2126 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 476.38976)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(16)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 467.71653 L 77.03937 467.71653 C 81.45765 467.71653 85.03937 471.29825 85.03937 475.71653 L 85.03937 488.063 C 85.03937 492.48127 81.45765 496.063 77.03937 496.063 L 64.692913 496.063 C 60.274635 496.063 56.692913 492.48127 56.692913 488.063 L 56.692913 475.71653 C 56.692913 471.29825 60.274635 467.71653 64.692913 467.71653 Z" fill="#5959b7"/><path d="M 64.692913 467.71653 L 77.03937 467.71653 C 81.45765 467.71653 85.03937 471.29825 85.03937 475.71653 L 85.03937 488.063 C 85.03937 492.48127 81.45765 496.063 77.03937 496.063 L 64.692913 496.063 C 60.274635 496.063 56.692913 492.48127 56.692913 488.063 L 56.692913 475.71653 C 56.692913 471.29825 60.274635 467.71653 64.692913 467.71653 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 476.38976)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(17)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 552.7559 L 162.07874 552.7559 C 166.49702 552.7559 170.07874 556.33762 170.07874 560.7559 L 170.07874 573.10236 C 170.07874 577.52064 166.49702 581.10236 162.07874 581.10236 L 149.73228 581.10236 C 145.314005 581.10236 141.73228 577.52064 141.73228 573.10236 L 141.73228 560.7559 C 141.73228 556.33762 145.314005 552.7559 149.73228 552.7559 Z" fill="#2076c8"/><path d="M 149.73228 552.7559 L 162.07874 552.7559 C 166.49702 552.7559 170.07874 556.33762 170.07874 560.7559 L 170.07874 573.10236 C 170.07874 577.52064 166.49702 581.10236 162.07874 581.10236 L 149.73228 581.10236 C 145.314005 581.10236 141.73228 577.52064 141.73228 573.10236 L 141.73228 560.7559 C 141.73228 556.33762 145.314005 552.7559 149.73228 552.7559 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 561.42913)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(15)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 552.7559 L 204.59842 552.7559 C 209.0167 552.7559 212.59842 556.33762 212.59842 560.7559 L 212.59842 573.10236 C 212.59842 577.52064 209.0167 581.10236 204.59842 581.10236 L 192.25197 581.10236 C 187.83369 581.10236 184.25197 577.52064 184.25197 573.10236 L 184.25197 560.7559 C 184.25197 556.33762 187.83369 552.7559 192.25197 552.7559 Z" fill="#708284"/><path d="M 192.25197 552.7559 L 204.59842 552.7559 C 209.0167 552.7559 212.59842 556.33762 212.59842 560.7559 L 212.59842 573.10236 C 212.59842 577.52064 209.0167 581.10236 204.59842 581.10236 L 192.25197 581.10236 C 187.83369 581.10236 184.25197 577.52064 184.25197 573.10236 L 184.25197 560.7559 C 184.25197 556.33762 187.83369 552.7559 192.25197 552.7559 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 555.92913)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(14)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(19)</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 170.07874 L 91.2126 170.07874 C 95.630876 170.07874 99.2126 173.66046 99.2126 178.07874 L 99.2126 224.44094 C 99.2126 228.85922 95.630876 232.44094 91.2126 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" fill="#fdf5dd"/><path d="M 50.519685 170.07874 L 91.2126 170.07874 C 95.630876 170.07874 99.2126 173.66046 99.2126 178.07874 L 99.2126 224.44094 C 99.2126 228.85922 95.630876 232.44094 91.2126 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.6003628" y="9" textLength="39.492188">Instance 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 149.73228 232.44094 C 145.314005 232.44094 141.73228 228.85922 141.73228 224.44094 L 141.73228 178.07874 C 141.73228 173.66046 145.314005 170.07874 149.73228 170.07874 Z" fill="#fdf5dd"/><path d="M 149.73228 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 149.73228 232.44094 C 145.314005 232.44094 141.73228 228.85922 141.73228 224.44094 L 141.73228 178.07874 C 141.73228 173.66046 145.314005 170.07874 149.73228 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="212.59842" x2="170.07874" y2="212.59842" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="212.59842" x2="212.59842" y2="212.59842" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="212.59842" x2="255.11811" y2="212.59842" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" fill="#5959b7"/><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(32)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 214.12091 C 191.51575 212.59842 194.07345 199.78157 204.30504 201.9685 C 205.25431 197.70548 217.15228 198.39742 217.0745 201.9685 C 224.53489 197.40104 234.06882 206.50847 227.67398 211.07594 C 235.34748 213.29036 227.57715 225.22138 221.27953 223.22835 C 220.77553 226.55027 209.51728 227.71275 208.52912 223.22835 C 202.15412 228.01748 188.86121 220.65392 197.92964 214.12091 Z" fill="#5959b7"/><path d="M 197.92964 214.12091 C 191.51575 212.59842 194.07345 199.78157 204.30504 201.9685 C 205.25431 197.70548 217.15228 198.39742 217.0745 201.9685 C 224.53489 197.40104 234.06882 206.50847 227.67398 211.07594 C 235.34748 213.29036 227.57715 225.22138 221.27953 223.22835 C 220.77553 226.55027 209.51728 227.71275 208.52912 223.22835 C 202.15412 228.01748 188.86121 220.65392 197.92964 214.12091 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7882012" y="9" textLength="14.203125">(30)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 198.4252 L 176.25197 198.4252 C 180.67025 198.4252 184.25197 202.00692 184.25197 206.4252 L 184.25197 218.77165 C 184.25197 223.18993 180.67025 226.77165 176.25197 226.77165 L 163.90551 226.77165 C 159.48723 226.77165 155.90551 223.18993 155.90551 218.77165 L 155.90551 206.4252 C 155.90551 202.00692 159.48723 198.4252 163.90551 198.4252 Z" fill="#5959b7"/><path d="M 163.90551 198.4252 L 176.25197 198.4252 C 180.67025 198.4252 184.25197 202.00692 184.25197 206.4252 L 184.25197 218.77165 C 184.25197 223.18993 180.67025 226.77165 176.25197 226.77165 L 163.90551 226.77165 C 159.48723 226.77165 155.90551 223.18993 155.90551 218.77165 L 155.90551 206.4252 C 155.90551 202.00692 159.48723 198.4252 163.90551 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(31)</tspan></text></g><path d="M 255.11811 212.59842 C 255.11811 212.59842 296.684 240.71067 302.30548 260.7874 C 307.92697 280.86413 288.32905 273.02466 274.96063 283.46457 C 261.59221 293.90447 255.11811 297.6378 255.11811 297.6378" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 291.46457 283.46457 L 303.81102 283.46457 C 308.2293 283.46457 311.81102 287.04629 311.81102 291.46457 L 311.81102 303.81102 C 311.81102 308.2293 308.2293 311.81102 303.81102 311.81102 L 291.46457 311.81102 C 287.04629 311.81102 283.46457 308.2293 283.46457 303.81102 L 283.46457 291.46457 C 283.46457 287.04629 287.04629 283.46457 291.46457 283.46457 Z" fill="#2076c8"/><path d="M 291.46457 283.46457 L 303.81102 283.46457 C 308.2293 283.46457 311.81102 287.04629 311.81102 291.46457 L 311.81102 303.81102 C 311.81102 308.2293 308.2293 311.81102 303.81102 311.81102 L 291.46457 311.81102 C 287.04629 311.81102 283.46457 308.2293 283.46457 303.81102 L 283.46457 291.46457 C 283.46457 287.04629 287.04629 283.46457 291.46457 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 292.1378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 283.46457 L 261.29134 283.46457 C 265.70962 283.46457 269.29134 287.04629 269.29134 291.46457 L 269.29134 303.81102 C 269.29134 308.2293 265.70962 311.81102 261.29134 311.81102 L 248.94488 311.81102 C 244.5266 311.81102 240.94488 308.2293 240.94488 303.81102 L 240.94488 291.46457 C 240.94488 287.04629 244.5266 283.46457 248.94488 283.46457 Z" fill="#5959b7"/><path d="M 248.94488 283.46457 L 261.29134 283.46457 C 265.70962 283.46457 269.29134 287.04629 269.29134 291.46457 L 269.29134 303.81102 C 269.29134 308.2293 265.70962 311.81102 261.29134 311.81102 L 248.94488 311.81102 C 244.5266 311.81102 240.94488 308.2293 240.94488 303.81102 L 240.94488 291.46457 C 240.94488 287.04629 244.5266 283.46457 248.94488 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 292.1378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(28)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" fill="#5959b7"/><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(29)</tspan></text></g><circle cx="42.519685" cy="651.9685" r="8.5039505" fill="#5959b7"/><text transform="translate(54.574802 639.96063)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 102, 192.168.2.0/24</tspan></text></g></g></svg>
