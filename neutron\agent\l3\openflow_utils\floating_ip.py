# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib import constants as lib_constants
from neutron_lib.exceptions import l3 as n_exc
from oslo_log import log as logging

from neutron.agent.common import utils as agent_comm
from neutron.agent.l2.extensions.communicate_tunnel import comm_sock
from neutron.agent.l3 import router_info as info
from neutron.common import utils as common_utils

LOG = logging.getLogger(__name__)


class OpenFlowFloatingIPRouterMixin(object):

    def __init__(self, *args, **kwargs):
        super(OpenFlowFloatingIPRouterMixin, self).__init__(*args, **kwargs)
        self.prev_cidrs = set()

    def process_fip_admin_state(self, fip):
        if not fip.get('admin_state_up', False):
            self.br.install_drop_fip_admin_state(fip['floating_ip_address'])
        else:
            self.br.remove_drop_fip_admin_state(fip['floating_ip_address'])

    def process_fip_denied_ports(self, fip):
        fip_denied_ports_list = fip.get('denied_port_numbers', [])

        # Directly clear all denied_ports flows, this action should not
        # be set in deferred action, because ovs_lib has the
        # order=('add', 'mod', 'del'), the added flows will be finally
        # removed.
        self.br.remove_drop_fip_ad_ports(fip['floating_ip_address'])
        if not fip_denied_ports_list:
            return

        # Add new denied_ports flows
        for dp in fip_denied_ports_list:
            self.br.install_drop_fip_ad_ports(fip['floating_ip_address'], dp)

    def add_floating_ip(self, fip, interface_name):
        self._floating_ips[interface_name] = fip
        float_ip = fip['floating_ip_address']
        fixed_ip = fip['fixed_ip_address']
        subnets = self._get_router_subnets()
        internal_cidr = ''
        gate_ip = None
        for sub in subnets:
            if agent_comm.check_ip_in_subnet(fixed_ip, sub['cidr']):
                internal_cidr = sub['cidr']
        ex_gw_port = self.get_ex_gw_port()
        external_subnets = self.agent.plugin_rpc.get_subnets_by_network(
            self.agent.context, ex_gw_port['network_id'])
        for sub in external_subnets:
            if agent_comm.check_ip_in_subnet(float_ip, sub['cidr']):
                fip_subnet_cidr = sub['cidr']
                gate_ip = sub['gateway_ip']
                gw_ip = ex_gw_port['fixed_ips'][0]
                gateway_ip_ofport = self.inf_ip_of_ports[gw_ip['ip_address']]

        if not gate_ip:
            return
        fixed_port_mac = fip['port_details']['mac_address']
        fg_mac = self.get_fip_agent_gateway(ex_gw_port)['mac_address']

        self.br.install_fip_flows(float_ip, fixed_ip, gate_ip,
                                  fg_mac, fixed_port_mac,
                                  gateway_ip_ofport,
                                  fip_subnet_cidr,
                                  internal_cidr=internal_cidr)

        self.process_fip_admin_state(fip)
        self.process_fip_denied_ports(fip)

        return lib_constants.FLOATINGIP_STATUS_ACTIVE

    def remove_floating_ip(self, ip_cidr):
        float_ip = ip_cidr.split('/')[0]
        fixed_ip = self.fip_map[float_ip]

        self.br.remove_fip_flows(float_ip, fixed_ip)

        ip = {"floating_ip_address": float_ip}
        try:
            comm_sock.notify_ovs_agent("", "", "", ip, "DOWN")
        except Exception as err:
            LOG.error("Failed to notify openvswitch agent to "
                      "remove flows for floating IP %s, error: %s",
                      float_ip, err)

    def disable_floating_ip_addresses(self):
        floating_ips = self.get_floating_ips()
        fip_statuses = {}
        for fip in floating_ips:
            fip_ip = fip['floating_ip_address']
            ip_cidr = common_utils.ip_to_cidr(fip_ip)
            self.remove_floating_ip(ip_cidr)
            fip_statuses[fip['id']] = lib_constants.FLOATINGIP_STATUS_DOWN
        return fip_statuses

    def process_floating_ip_addresses(self, interface_name):
        """Configure IP addresses on router's external gateway interface.

        Ensures addresses for existing floating IPs and cleans up
        those that should not longer be configured.
        """

        fip_statuses = {}
        if interface_name is None:
            LOG.debug('No Interface for floating IPs router: %s',
                      self.router['id'])
            return fip_statuses

        existing_cidrs = self.get_router_cidrs()
        new_cidrs = set()

        floating_ips = self.get_floating_ips()
        # Loop once to ensure that floating ips are configured.
        for fip in floating_ips:
            fip_ip = fip['floating_ip_address']
            ip_cidr = common_utils.ip_to_cidr(fip_ip)
            new_cidrs.add(ip_cidr)
            fip_statuses[fip['id']] = lib_constants.FLOATINGIP_STATUS_ACTIVE
            if ip_cidr not in existing_cidrs:
                fip_statuses[fip['id']] = self.add_floating_ip(
                    fip, interface_name)
                LOG.debug('Floating ip %(id)s added, status %(status)s',
                          {'id': fip['id'],
                           'status': fip_statuses.get(fip['id'])})
            elif (fip_ip in self.fip_map and
                  self.fip_map[fip_ip] != fip['fixed_ip_address']):
                LOG.debug("Floating IP was moved from fixed IP "
                          "%(old)s to %(new)s",
                          {'old': self.fip_map[fip_ip],
                           'new': fip['fixed_ip_address']})
                fip_statuses[fip['id']] = self.move_floating_ip(fip)
            elif fip_statuses[fip['id']] == fip['status']:
                # mark the status as not changed. we can't remove it because
                # that's how the caller determines that it was removed
                fip_statuses[fip['id']] = info.FLOATINGIP_STATUS_NOCHANGE
        fips_to_remove = (ip_cidr for ip_cidr in self.prev_cidrs -
                          new_cidrs if common_utils.is_cidr_host(ip_cidr))

        for ip_cidr in list(fips_to_remove):
            LOG.debug("Removing floating ip %s from interface %s in "
                      "namespace %s", ip_cidr, interface_name, self.ns_name)
            self.remove_floating_ip(ip_cidr)

        self.prev_cidrs = new_cidrs

        return fip_statuses

    def configure_fip_addresses(self, interface_name):
        try:
            return self.process_floating_ip_addresses(interface_name)
        except Exception as e:
            # TODO(salv-orlando): Less broad catching
            msg = ('L3 agent failure to setup floating IPs: %s' % e)
            LOG.exception(msg)
            raise n_exc.FloatingIpSetupException(msg)

    def floating_forward_rules(self, floating_ip, fixed_ip):
        pass

    def floating_mangle_rules(self, floating_ip, fixed_ip, internal_mark):
        pass

    def process_floating_ip_nat_rules(self):
        """Configure NAT rules for the router's floating IPs.

        Configures iptables rules for the floating ips of the given router
        """
        pass

    def process_floating_ip_address_scope_rules(self):
        """Configure address scope related iptables rules for the router's
         floating IPs.
        """
        pass

    def process_snat_dnat_for_fip(self):
        pass

    def gateway_redirect_cleanup(self, rtr_interface):
        pass

    def move_floating_ip(self, fip):
        return lib_constants.FLOATINGIP_STATUS_ACTIVE

    def external_gateway_nat_fip_rules(self, ex_gw_ip, interface_name):
        pass

    def external_gateway_nat_snat_rules(self, ex_gw_ip, interface_name):
        pass

    def put_fips_in_error_state(self):
        pass

    def notify_ovs_agent_fips(self, fip_statuses):
        ex_gw_port = self.get_ex_gw_port()
        if not ex_gw_port:
            return
        interface_name = self.get_external_device_interface_name(
            ex_gw_port)
        fip_agent_port = self.get_fip_agent_gateway(ex_gw_port)
        fip_agent_port_mac = fip_agent_port['mac_address']
        floating_ip_network = ex_gw_port['network_id']
        fips = {fip["id"]: fip for fip in self.get_floating_ips()}

        for fip_id, state in fip_statuses.items():
            ip = fips.get(fip_id)
            if state == "ACTIVE" and ip:
                try:
                    comm_sock.notify_ovs_agent(interface_name,
                                               floating_ip_network,
                                               fip_agent_port_mac,
                                               ip, state)
                except Exception as err:
                    LOG.error("Failed to notify openvswitch agent to "
                              "add flows for floating IP %s, error: %s",
                              ip, err)

    def update_fip_statuses(self, fip_statuses):
        # Identify floating IPs which were disabled
        existing_floating_ips = self.floating_ips
        self.floating_ips = set(fip_statuses.keys())
        for fip_id in existing_floating_ips - self.floating_ips:
            fip_statuses[fip_id] = lib_constants.FLOATINGIP_STATUS_DOWN

        # filter out statuses that didn't change
        fip_statuses = {f: stat for f, stat in fip_statuses.items()
                        if stat != info.FLOATINGIP_STATUS_NOCHANGE}
        if not fip_statuses:
            return
        self.notify_ovs_agent_fips(fip_statuses)
        LOG.debug('Sending floating ip statuses: %s', fip_statuses)
        # Update floating IP status on the neutron server
        self.agent.plugin_rpc.update_floatingip_statuses(
            self.agent.context, self.router_id, fip_statuses)
