CI Status Dashboards
====================

Gerrit Dashboards
-----------------

- `Neutron master branch reviews <https://review.openstack.org/#/dashboard/?title=Neutron+Review+Inbox+%28master+branch+only%29&foreach=%28project%3Aopenstack%2Fneutron+OR%0Aproject%3Aopenstack%2Fneutron%2Dlib+OR%0Aproject%3Aopenstack%2Fneutron%2Dtempest%2Dplugin+OR%0Aproject%3Aopenstack%2Fpython%2Dneutronclient+OR%0Aproject%3Aopenstack%2Fneutron%2Dspecs%29%0Astatus%3Aopen+NOT+owner%3Aself+NOT+label%3AWorkflow%3C%3D%2D1+label%3AVerified%3E%3D1%2Czuul+NOT+reviewedby%3Aself+branch%3Amaster&Needs+Feedback+%28Changes+older+than+5+days+that+have+not+been+reviewed+by+anyone%29=NOT+label%3ACode%2DReview%3C%3D%2D1+NOT+label%3ACode%2DReview%3E%3D1+age%3A5d&You+are+a+reviewer%2C+but+haven%27t+voted+in+the+current+revision=NOT+label%3ACode%2DReview%3C%3D%2D1%2Cself+NOT+label%3ACode%2DReview%3E%3D1%2Cself+reviewer%3Aself&Needs+final+%2B2=label%3ACode%2DReview%3E%3D2+NOT%28reviewerin%3Aneutron%2Dcore+label%3ACode%2DReview%3C%3D%2D1%29+limit%3A50&Passed+Zuul%2C+No+Negative+Core+Feedback=NOT+label%3ACode%2DReview%3E%3D2+NOT%28reviewerin%3Aneutron%2Dcore+label%3ACode%2DReview%3C%3D%2D1%29+limit%3A50&Wayward+Changes+%28Changes+with+no+code+review+in+the+last+2days%29=NOT+label%3ACode%2DReview%3C%3D%2D1+NOT+label%3ACode%2DReview%3E%3D1+age%3A2d>`_
- `Neutron subproject reviews (master branch) <https://review.openstack.org/#/dashboard/?title=Neutron+Sub+Projects+Review+Inbox&foreach=%28%0Aproject%3Aopenstack%2Fnetworking%2Dbagpipe+OR%0Aproject%3Aopenstack%2Fnetworking%2Dbgpvpn+OR%0Aproject%3Aopenstack%2Fnetworking%2Dmidonet+OR%0Aproject%3Aopenstack%2Fnetworking%2Dodl+OR%0Aproject%3Aopenstack%2Fnetworking%2Dovn+OR%0Aproject%3Aopenstack%2Fnetworking%2Dsfc+OR%0Aproject%3Aopenstack%2Fneutron%2Ddynamic%2Drouting+OR%0Aproject%3Aopenstack%2Fneutron%2Dfwaas+OR%0Aproject%3Aopenstack%2Fneutron%2Dvpnaas+OR%0Aproject%3Aopenstack%2Fovsdbapp%29+status%3Aopen+NOT+owner%3Aself+NOT+label%3AWorkflow%3C%3D%2D1+label%3AVerified%3E%3D1%2Czuul+NOT+reviewedby%3Aself+branch%3Amaster&Needs+Feedback+%28Changes+older+than+5+days+that+have+not+been+reviewed+by+anyone%29=NOT+label%3ACode%2DReview%3C%3D%2D1+NOT+label%3ACode%2DReview%3E%3D1+age%3A5d&You+are+a+reviewer%2C+but+haven%27t+voted+in+the+current+revision=NOT+label%3ACode%2DReview%3C%3D%2D1%2Cself+NOT+label%3ACode%2DReview%3E%3D1%2Cself+reviewer%3Aself&Needs+final+%2B2=label%3ACode%2DReview%3E%3D2+NOT%28reviewerin%3Aneutron%2Dcore+label%3ACode%2DReview%3C%3D%2D1%29+limit%3A50&Passed+Zuul%2C+No+Negative+Core+Feedback=NOT+label%3ACode%2DReview%3E%3D2+NOT%28reviewerin%3Aneutron%2Dcore+label%3ACode%2DReview%3C%3D%2D1%29+limit%3A50&Wayward+Changes+%28Changes+with+no+code+review+in+the+last+2days%29=NOT+label%3ACode%2DReview%3C%3D%2D1+NOT+label%3ACode%2DReview%3E%3D1+age%3A2d>`_
- `Neutron stable branch reviews <https://review.openstack.org/#/dashboard/?title=Neutron+Stable+Related+Projects+Review+Inbox&foreach=%28%0Aproject%3Aopenstack%2Fnetworking%2Dbagpipe+OR%0Aproject%3Aopenstack%2Fnetworking%2Dbgpvpn+OR%0Aproject%3Aopenstack%2Fnetworking%2Dmidonet+OR%0Aproject%3Aopenstack%2Fnetworking%2Dodl+OR%0Aproject%3Aopenstack%2Fnetworking%2Dovn+OR%0Aproject%3Aopenstack%2Fnetworking%2Dsfc+OR%0Aproject%3Aopenstack%2Fneutron+OR%0Aproject%3Aopenstack%2Fneutron%2Ddynamic%2Drouting+OR%0Aproject%3Aopenstack%2Fneutron%2Dfwaas+OR%0Aproject%3Aopenstack%2Fneutron%2Dvpnaas+OR%0Aproject%3Aopenstack%2Fneutron%2Dlib+OR%0Aproject%3Aopenstack%2Fovsdbapp+OR%0Aproject%3Aopenstack%2Fpython%2Dneutronclient%29+status%3Aopen+NOT+owner%3Aself+NOT+label%3AWorkflow%3C%3D%2D1+label%3AVerified%3E%3D1%2Czuul+NOT+reviewedby%3Aself+branch%3A%5Estable%2F.%2A&Needs+Feedback+%28Changes+older+than+5+days+that+have+not+been+reviewed+by+anyone%29=NOT+label%3ACode%2DReview%3C%3D%2D1+NOT+label%3ACode%2DReview%3E%3D1+age%3A5d&You+are+a+reviewer%2C+but+haven%27t+voted+in+the+current+revision=NOT+label%3ACode%2DReview%3C%3D%2D1%2Cself+NOT+label%3ACode%2DReview%3E%3D1%2Cself+reviewer%3Aself&Needs+final+%2B2=label%3ACode%2DReview%3E%3D2+NOT%28reviewerin%3Aneutron%2Dstable%2Dmaint+label%3ACode%2DReview%3C%3D%2D1%29+limit%3A50&Passed+Zuul%2C+No+Negative+Core+Feedback=NOT+label%3ACode%2DReview%3E%3D2+NOT%28reviewerin%3Aneutron%2Dstable%2Dmaint+label%3ACode%2DReview%3C%3D%2D1%29+limit%3A50&Wayward+Changes+%28Changes+with+no+code+review+in+the+last+2days%29=NOT+label%3ACode%2DReview%3C%3D%2D1+NOT+label%3ACode%2DReview%3E%3D1+age%3A2d>`_
- `Neutron Infra reviews <https://review.openstack.org/#/dashboard/?title=Neutron+Infra+Review+Inbox&foreach=%28project%3Aopenstack%2Dinfra%2Fproject%2Dconfig+OR+project%3Aopenstack%2Dinfra%2Fopenstack%2Dzuul%2Djobs+OR+project%3Aopenstack%2Dinfra%2Fdevstack%2Dgate%29+status%3Aopen+NOT+owner%3Aself+NOT+label%3AWorkflow%3C%3D%2D1+label%3AVerified%3E%3D1%2Czuul+NOT+reviewedby%3Aself&Neutron+related+infra+reviews=%28message%3A%22neutron%22+OR+message%3A%22networking%2D%22+OR+message%3A%22n8g%2D%22+OR+message%3A%22ovsdbapp%22+OR+%28comment%3A%22neutron%22+%28comment%3A%22liaison%22+OR+comment%3A%22liason%22%29%29%29>`_

These dashboard links can be generated by `Gerrit Dashboard Creator`_.
Useful dashboard definitions are found in ``dashboards`` directory.

.. _Gerrit Dashboard Creator: https://github.com/openstack/gerrit-dash-creator

Grafana Dashboards
------------------

Look for neutron and networking-* dashboard by names by going to the following link:

`Grafana <http://grafana.openstack.org/>`_

For instance:

* `Neutron <http://grafana.openstack.org/dashboard/db/neutron-failure-rate>`_
* `Neutron-lib <http://grafana.openstack.org/dashboard/db/neutron-lib-failure-rate>`_
