<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 340 611" width="340pt" height="611pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.1.4 <dc:date>2017-03-02 04:52:59 +0000</dc:date></metadata><defs><font-face font-family="Helvetica" font-size="16" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="12" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Helvetica" font-size="8" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="532.22656" cap-height="719.72656" ascent="770.01953" descent="-229.98047" font-weight="bold"><font-face-src><font-face-name name="Helvetica-Bold"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="7" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="532.22656" cap-height="719.72656" ascent="770.01953" descent="-229.98047" font-weight="bold"><font-face-src><font-face-name name="Helvetica-Bold"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="10" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="8" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><rect fill="white" width="559" height="783"/><g><title>Layer 1</title><text transform="translate(48.18504 9.8582674)" fill="#536870"><tspan font-family="Helvetica" font-size="16" font-weight="500" fill="#536870" x=".18359375" y="15" textLength="267.63281">Open vSwitch - Self-service Networks</tspan><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="16.733398" y="30" textLength="47.34375">Network </tspan><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="63.86621" y="30" textLength="7.330078">T</tspan><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="70.756836" y="30" textLength="20.003906">raffi</tspan><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="90.76074" y="30" textLength="84.00586">c Flow - East/W</tspan><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="174.55566" y="30" textLength="76.710938">est Scenario 1</tspan></text><path d="M 36.346457 56.692913 L 289.6378 56.692913 C 294.05607 56.692913 297.6378 60.274635 297.6378 64.692913 L 297.6378 281.13386 C 297.6378 285.55213 294.05607 289.13386 289.6378 289.13386 L 36.346457 289.13386 C 31.928179 289.13386 28.346457 285.55213 28.346457 281.13386 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="84.285317" y="11" textLength="90.720703">Compute Node 1</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="3.7820034" y="8" textLength="39.128906">Instance 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" fill="#fdf5dd"/><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 90.03937)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="41.42083" y="8" textLength="48.890625">Linux Bridge</tspan><tspan font-family="Helvetica" font-size="7" font-weight="bold" fill="#536870" x="60.228202" y="16" textLength="11.275879">qbr</tspan></text></g><line x1="70.86614" y1="127.559054" x2="170.07874" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="127.559054" x2="212.59842" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="127.559054" x2="255.11811" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.559054)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" fill="#2076c8"/><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 122.559054)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0010918" y="8" textLength="9.777344">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" fill="#2076c8"/><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 122.559054)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(2)</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 170.07874 L 133.73228 170.07874 C 138.15056 170.07874 141.73228 173.66046 141.73228 178.07874 L 141.73228 224.44094 C 141.73228 228.85922 138.15056 232.44094 133.73228 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" fill="#fdf5dd"/><path d="M 50.519685 170.07874 L 133.73228 170.07874 C 138.15056 170.07874 141.73228 173.66046 141.73228 178.07874 L 141.73228 224.44094 C 141.73228 228.85922 138.15056 232.44094 133.73228 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 175.07874)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="7.342627" y="8" textLength="26.226562"> OVS T</tspan><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="32.979346" y="8" textLength="48.890625">unnel Bridge</tspan><tspan font-family="Helvetica" font-size="7" font-weight="bold" fill="#536870" x="34.499365" y="16" textLength="20.213867">br-tun</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 192.25197 232.44094 C 187.83369 232.44094 184.25197 228.85922 184.25197 224.44094 L 184.25197 178.07874 C 184.25197 173.66046 187.83369 170.07874 192.25197 170.07874 Z" fill="#fdf5dd"/><path d="M 192.25197 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 192.25197 232.44094 C 187.83369 232.44094 184.25197 228.85922 184.25197 224.44094 L 184.25197 178.07874 C 184.25197 173.66046 187.83369 170.07874 192.25197 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 175.07874)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x=".60239267" y="8" textLength="88.00781">OVS Integration Bridge</tspan><tspan font-family="Helvetica" font-size="7" font-weight="bold" fill="#536870" x="35.664893" y="16" textLength="17.882812">br-int</tspan></text></g><path d="M 255.11811 127.559054 C 255.11811 127.559054 291.9685 145.98666 291.9685 170.07874 C 291.9685 194.17082 255.11811 212.59842 255.11811 212.59842" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="255.11811" x2="70.86614" y2="212.59842" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" fill="#2076c8"/><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 122.559054)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" fill="#2076c8"/><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 207.59842)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(5)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" fill="#a57706"/><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 207.59842)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(8)</tspan></text></g><path d="M 70.86614 269.29134 C 70.86614 269.29134 69.456046 295.2764 141.73228 303.30708 C 214.00852 311.33778 325.98425 297.6378 325.98425 297.6378" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 64.692913 255.11811 L 77.03937 255.11811 C 81.45765 255.11811 85.03937 258.69983 85.03937 263.11811 L 85.03937 275.46457 C 85.03937 279.88284 81.45765 283.46457 77.03937 283.46457 L 64.692913 283.46457 C 60.274635 283.46457 56.692913 279.88284 56.692913 275.46457 L 56.692913 263.11811 C 56.692913 258.69983 60.274635 255.11811 64.692913 255.11811 Z" fill="#a57706"/><path d="M 64.692913 255.11811 L 77.03937 255.11811 C 81.45765 255.11811 85.03937 258.69983 85.03937 263.11811 L 85.03937 275.46457 C 85.03937 279.88284 81.45765 283.46457 77.03937 283.46457 L 64.692913 283.46457 C 60.274635 283.46457 56.692913 279.88284 56.692913 275.46457 L 56.692913 263.11811 C 56.692913 258.69983 60.274635 255.11811 64.692913 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 264.29134)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(9)</tspan></text></g><line x1="212.59842" y1="212.59842" x2="113.385826" y2="212.59842" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" fill="#708284"/><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 207.59842)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 198.4252 L 119.559054 198.4252 C 123.97733 198.4252 127.559054 202.00692 127.559054 206.4252 L 127.559054 218.77165 C 127.559054 223.18993 123.97733 226.77165 119.559054 226.77165 L 107.2126 226.77165 C 102.79432 226.77165 99.2126 223.18993 99.2126 218.77165 L 99.2126 206.4252 C 99.2126 202.00692 102.79432 198.4252 107.2126 198.4252 Z" fill="#708284"/><path d="M 107.2126 198.4252 L 119.559054 198.4252 C 123.97733 198.4252 127.559054 202.00692 127.559054 206.4252 L 127.559054 218.77165 C 127.559054 223.18993 123.97733 226.77165 119.559054 226.77165 L 107.2126 226.77165 C 102.79432 226.77165 99.2126 223.18993 99.2126 218.77165 L 99.2126 206.4252 C 99.2126 202.00692 102.79432 198.4252 107.2126 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 207.59842)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.2845564" y="8" textLength="9.777344">(7)</tspan></text></g><circle cx="184.25197" cy="581.10236" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 569.0945)" fill="#536870"><tspan font-family="Helvetica" font-size="10" font-weight="500" fill="#536870" x="0" y="10" textLength="98.36914">Self-service network 1</tspan><tspan font-family="Helvetica" font-size="8" font-weight="500" fill="#536870" x="0" y="20" textLength="86.734375">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(56.897637 569.0945)" fill="#536870"><tspan font-family="Helvetica" font-size="10" font-weight="500" fill="#536870" x="0" y="10" textLength="72.246094">Overlay network</tspan><tspan font-family="Helvetica" font-size="8" font-weight="500" fill="#536870" x="0" y="20" textLength="40.035156">10.0.1.0/24</tspan></text><circle cx="42.519685" cy="581.10236" r="8.5039505" fill="#a57706"/><text transform="translate(90.72141 291.25432) rotate(17.305357)" fill="#2176c7"><tspan font-family="Helvetica" font-size="8" font-weight="500" fill="#2176c7" x=".046875" y="8" textLength="28.90625">VNI 101</tspan></text><path d="M 36.346457 311.81102 L 289.6378 311.81102 C 294.05607 311.81102 297.6378 315.39274 297.6378 319.81102 L 297.6378 536.25197 C 297.6378 540.67024 294.05607 544.25197 289.6378 544.25197 L 36.346457 544.25197 C 31.928179 544.25197 28.346457 540.67024 28.346457 536.25197 L 28.346457 319.81102 C 28.346457 315.39274 31.928179 311.81102 36.346457 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 316.81102)" fill="#536870"><tspan font-family="Helvetica" font-size="12" font-weight="500" fill="#536870" x="84.285317" y="11" textLength="90.720703">Compute Node 2</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 340.15748 L 91.2126 340.15748 C 95.630876 340.15748 99.2126 343.7392 99.2126 348.15748 L 99.2126 394.51968 C 99.2126 398.93796 95.630876 402.51968 91.2126 402.51968 L 50.519685 402.51968 C 46.101407 402.51968 42.519685 398.93796 42.519685 394.51968 L 42.519685 348.15748 C 42.519685 343.7392 46.101407 340.15748 50.519685 340.15748 Z" fill="#fdf5dd"/><path d="M 50.519685 340.15748 L 91.2126 340.15748 C 95.630876 340.15748 99.2126 343.7392 99.2126 348.15748 L 99.2126 394.51968 C 99.2126 398.93796 95.630876 402.51968 91.2126 402.51968 L 50.519685 402.51968 C 46.101407 402.51968 42.519685 398.93796 42.519685 394.51968 L 42.519685 348.15748 C 42.519685 343.7392 46.101407 340.15748 50.519685 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 345.15748)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="3.7820034" y="8" textLength="39.128906">Instance 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 340.15748 L 275.46457 340.15748 C 279.88284 340.15748 283.46457 343.7392 283.46457 348.15748 L 283.46457 394.51968 C 283.46457 398.93796 279.88284 402.51968 275.46457 402.51968 L 149.73228 402.51968 C 145.314005 402.51968 141.73228 398.93796 141.73228 394.51968 L 141.73228 348.15748 C 141.73228 343.7392 145.314005 340.15748 149.73228 340.15748 Z" fill="#fdf5dd"/><path d="M 149.73228 340.15748 L 275.46457 340.15748 C 279.88284 340.15748 283.46457 343.7392 283.46457 348.15748 L 283.46457 394.51968 C 283.46457 398.93796 279.88284 402.51968 275.46457 402.51968 L 149.73228 402.51968 C 145.314005 402.51968 141.73228 398.93796 141.73228 394.51968 L 141.73228 348.15748 C 141.73228 343.7392 145.314005 340.15748 149.73228 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 345.15748)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="41.42083" y="8" textLength="48.890625">Linux Bridge</tspan><tspan font-family="Helvetica" font-size="7" font-weight="bold" fill="#536870" x="60.228202" y="16" textLength="11.275879">qbr</tspan></text></g><line x1="70.86614" y1="382.67716" x2="170.07874" y2="382.67716" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="382.67716" x2="212.59842" y2="382.67716" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="382.67716" x2="255.11811" y2="382.67716" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 368.50393 L 77.03937 368.50393 C 81.45765 368.50393 85.03937 372.08566 85.03937 376.50393 L 85.03937 388.8504 C 85.03937 393.26867 81.45765 396.8504 77.03937 396.8504 L 64.692913 396.8504 C 60.274635 396.8504 56.692913 393.26867 56.692913 388.8504 L 56.692913 376.50393 C 56.692913 372.08566 60.274635 368.50393 64.692913 368.50393 Z" fill="#2076c8"/><path d="M 64.692913 368.50393 L 77.03937 368.50393 C 81.45765 368.50393 85.03937 372.08566 85.03937 376.50393 L 85.03937 388.8504 C 85.03937 393.26867 81.45765 396.8504 77.03937 396.8504 L 64.692913 396.8504 C 60.274635 396.8504 56.692913 393.26867 56.692913 388.8504 L 56.692913 376.50393 C 56.692913 372.08566 60.274635 368.50393 64.692913 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 377.67716)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(19)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 384.19965 C 191.51575 382.67716 194.07345 369.86031 204.30504 372.04724 C 205.25431 367.78422 217.15228 368.47616 217.0745 372.04724 C 224.53489 367.47978 234.06882 376.5872 227.67398 381.15467 C 235.34748 383.3691 227.57715 395.30012 221.27953 393.30708 C 220.77553 396.629 209.51728 397.7915 208.52912 393.30708 C 202.15412 398.09622 188.86121 390.73266 197.92964 384.19965 Z" fill="#2076c8"/><path d="M 197.92964 384.19965 C 191.51575 382.67716 194.07345 369.86031 204.30504 372.04724 C 205.25431 367.78422 217.15228 368.47616 217.0745 372.04724 C 224.53489 367.47978 234.06882 376.5872 227.67398 381.15467 C 235.34748 383.3691 227.57715 395.30012 221.27953 393.30708 C 220.77553 396.629 209.51728 397.7915 208.52912 393.30708 C 202.15412 398.09622 188.86121 390.73266 197.92964 384.19965 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 377.67716)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7764824" y="8" textLength="14.2265625">(17)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 368.50393 L 176.25197 368.50393 C 180.67025 368.50393 184.25197 372.08566 184.25197 376.50393 L 184.25197 388.8504 C 184.25197 393.26867 180.67025 396.8504 176.25197 396.8504 L 163.90551 396.8504 C 159.48723 396.8504 155.90551 393.26867 155.90551 388.8504 L 155.90551 376.50393 C 155.90551 372.08566 159.48723 368.50393 163.90551 368.50393 Z" fill="#2076c8"/><path d="M 163.90551 368.50393 L 176.25197 368.50393 C 180.67025 368.50393 184.25197 372.08566 184.25197 376.50393 L 184.25197 388.8504 C 184.25197 393.26867 180.67025 396.8504 176.25197 396.8504 L 163.90551 396.8504 C 159.48723 396.8504 155.90551 393.26867 155.90551 388.8504 L 155.90551 376.50393 C 155.90551 372.08566 159.48723 368.50393 163.90551 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 377.67716)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(18)</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 425.19685 L 133.73228 425.19685 C 138.15056 425.19685 141.73228 428.77857 141.73228 433.19685 L 141.73228 479.55905 C 141.73228 483.97733 138.15056 487.55905 133.73228 487.55905 L 50.519685 487.55905 C 46.101407 487.55905 42.519685 483.97733 42.519685 479.55905 L 42.519685 433.19685 C 42.519685 428.77857 46.101407 425.19685 50.519685 425.19685 Z" fill="#fdf5dd"/><path d="M 50.519685 425.19685 L 133.73228 425.19685 C 138.15056 425.19685 141.73228 428.77857 141.73228 433.19685 L 141.73228 479.55905 C 141.73228 483.97733 138.15056 487.55905 133.73228 487.55905 L 50.519685 487.55905 C 46.101407 487.55905 42.519685 483.97733 42.519685 479.55905 L 42.519685 433.19685 C 42.519685 428.77857 46.101407 425.19685 50.519685 425.19685 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 430.19685)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="7.342627" y="8" textLength="26.226562"> OVS T</tspan><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x="32.979346" y="8" textLength="48.890625">unnel Bridge</tspan><tspan font-family="Helvetica" font-size="7" font-weight="bold" fill="#536870" x="34.499365" y="16" textLength="20.213867">br-tun</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 425.19685 L 275.46457 425.19685 C 279.88284 425.19685 283.46457 428.77857 283.46457 433.19685 L 283.46457 479.55905 C 283.46457 483.97733 279.88284 487.55905 275.46457 487.55905 L 192.25197 487.55905 C 187.83369 487.55905 184.25197 483.97733 184.25197 479.55905 L 184.25197 433.19685 C 184.25197 428.77857 187.83369 425.19685 192.25197 425.19685 Z" fill="#fdf5dd"/><path d="M 192.25197 425.19685 L 275.46457 425.19685 C 279.88284 425.19685 283.46457 428.77857 283.46457 433.19685 L 283.46457 479.55905 C 283.46457 483.97733 279.88284 487.55905 275.46457 487.55905 L 192.25197 487.55905 C 187.83369 487.55905 184.25197 483.97733 184.25197 479.55905 L 184.25197 433.19685 C 184.25197 428.77857 187.83369 425.19685 192.25197 425.19685 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 430.19685)" fill="#536870"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#536870" x=".60239267" y="8" textLength="88.00781">OVS Integration Bridge</tspan><tspan font-family="Helvetica" font-size="7" font-weight="bold" fill="#536870" x="35.664893" y="16" textLength="17.882812">br-int</tspan></text></g><path d="M 255.11811 382.67716 C 255.11811 382.67716 291.9685 401.10477 291.9685 425.19685 C 291.9685 449.28893 255.11811 467.71653 255.11811 467.71653" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="510.23622" x2="70.86614" y2="467.71653" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 368.50393 L 261.29134 368.50393 C 265.70962 368.50393 269.29134 372.08566 269.29134 376.50393 L 269.29134 388.8504 C 269.29134 393.26867 265.70962 396.8504 261.29134 396.8504 L 248.94488 396.8504 C 244.5266 396.8504 240.94488 393.26867 240.94488 388.8504 L 240.94488 376.50393 C 240.94488 372.08566 244.5266 368.50393 248.94488 368.50393 Z" fill="#2076c8"/><path d="M 248.94488 368.50393 L 261.29134 368.50393 C 265.70962 368.50393 269.29134 372.08566 269.29134 376.50393 L 269.29134 388.8504 C 269.29134 393.26867 265.70962 396.8504 261.29134 396.8504 L 248.94488 396.8504 C 244.5266 396.8504 240.94488 393.26867 240.94488 388.8504 L 240.94488 376.50393 C 240.94488 372.08566 244.5266 368.50393 248.94488 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 377.67716)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(16)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 453.5433 L 261.29134 453.5433 C 265.70962 453.5433 269.29134 457.12503 269.29134 461.5433 L 269.29134 473.88976 C 269.29134 478.30804 265.70962 481.88976 261.29134 481.88976 L 248.94488 481.88976 C 244.5266 481.88976 240.94488 478.30804 240.94488 473.88976 L 240.94488 461.5433 C 240.94488 457.12503 244.5266 453.5433 248.94488 453.5433 Z" fill="#2076c8"/><path d="M 248.94488 453.5433 L 261.29134 453.5433 C 265.70962 453.5433 269.29134 457.12503 269.29134 461.5433 L 269.29134 473.88976 C 269.29134 478.30804 265.70962 481.88976 261.29134 481.88976 L 248.94488 481.88976 C 244.5266 481.88976 240.94488 478.30804 240.94488 473.88976 L 240.94488 461.5433 C 240.94488 457.12503 244.5266 453.5433 248.94488 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 462.71653)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(15)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 453.5433 L 77.03937 453.5433 C 81.45765 453.5433 85.03937 457.12503 85.03937 461.5433 L 85.03937 473.88976 C 85.03937 478.30804 81.45765 481.88976 77.03937 481.88976 L 64.692913 481.88976 C 60.274635 481.88976 56.692913 478.30804 56.692913 473.88976 L 56.692913 461.5433 C 56.692913 457.12503 60.274635 453.5433 64.692913 453.5433 Z" fill="#a57706"/><path d="M 64.692913 453.5433 L 77.03937 453.5433 C 81.45765 453.5433 85.03937 457.12503 85.03937 461.5433 L 85.03937 473.88976 C 85.03937 478.30804 81.45765 481.88976 77.03937 481.88976 L 64.692913 481.88976 C 60.274635 481.88976 56.692913 478.30804 56.692913 473.88976 L 56.692913 461.5433 C 56.692913 457.12503 60.274635 453.5433 64.692913 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 462.71653)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(12)</tspan></text></g><line x1="212.59842" y1="467.71653" x2="113.385826" y2="467.71653" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 206.4252 453.5433 L 218.77165 453.5433 C 223.18993 453.5433 226.77165 457.12503 226.77165 461.5433 L 226.77165 473.88976 C 226.77165 478.30804 223.18993 481.88976 218.77165 481.88976 L 206.4252 481.88976 C 202.00692 481.88976 198.4252 478.30804 198.4252 473.88976 L 198.4252 461.5433 C 198.4252 457.12503 202.00692 453.5433 206.4252 453.5433 Z" fill="#708284"/><path d="M 206.4252 453.5433 L 218.77165 453.5433 C 223.18993 453.5433 226.77165 457.12503 226.77165 461.5433 L 226.77165 473.88976 C 226.77165 478.30804 223.18993 481.88976 218.77165 481.88976 L 206.4252 481.88976 C 202.00692 481.88976 198.4252 478.30804 198.4252 473.88976 L 198.4252 461.5433 C 198.4252 457.12503 202.00692 453.5433 206.4252 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 462.71653)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(14)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 453.5433 L 119.559054 453.5433 C 123.97733 453.5433 127.559054 457.12503 127.559054 461.5433 L 127.559054 473.88976 C 127.559054 478.30804 123.97733 481.88976 119.559054 481.88976 L 107.2126 481.88976 C 102.79432 481.88976 99.2126 478.30804 99.2126 473.88976 L 99.2126 461.5433 C 99.2126 457.12503 102.79432 453.5433 107.2126 453.5433 Z" fill="#708284"/><path d="M 107.2126 453.5433 L 119.559054 453.5433 C 123.97733 453.5433 127.559054 457.12503 127.559054 461.5433 L 127.559054 473.88976 C 127.559054 478.30804 123.97733 481.88976 119.559054 481.88976 L 107.2126 481.88976 C 102.79432 481.88976 99.2126 478.30804 99.2126 473.88976 L 99.2126 461.5433 C 99.2126 457.12503 102.79432 453.5433 107.2126 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 462.71653)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.059947" y="8" textLength="14.2265625">(13)</tspan></text></g><path d="M 325.98425 297.6378 C 325.98425 297.6378 355.7408 431.81745 283.46457 496.063 C 225.13277 547.91348 122.4787 534.53397 85.039364 527.44126" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 311.31547 299.16028 C 304.90157 297.6378 307.45927 284.82094 317.69087 287.00787 C 318.64014 282.74485 330.5381 283.43679 330.46033 287.00787 C 337.92072 282.4404 347.45465 291.54784 341.0598 296.1153 C 348.7333 298.32973 340.96297 310.26075 334.66535 308.26771 C 334.16135 311.58964 322.9031 312.75212 321.91495 308.26771 C 315.53994 313.05685 302.24704 305.69329 311.31547 299.16028 Z" fill="#a57706"/><path d="M 311.31547 299.16028 C 304.90157 297.6378 307.45927 284.82094 317.69087 287.00787 C 318.64014 282.74485 330.5381 283.43679 330.46033 287.00787 C 337.92072 282.4404 347.45465 291.54784 341.0598 296.1153 C 348.7333 298.32973 340.96297 310.26075 334.66535 308.26771 C 334.16135 311.58964 322.9031 312.75212 321.91495 308.26771 C 315.53994 313.05685 302.24704 305.69329 311.31547 299.16028 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(317.09449 292.6378)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7764822" y="8" textLength="14.2265625">(10)</tspan></text><g filter="url(#Shadow)"><path d="M 64.692913 510.23622 L 77.03937 510.23622 C 81.45765 510.23622 85.03937 513.81794 85.03937 518.23622 L 85.03937 530.58267 C 85.03937 535.00095 81.45765 538.58267 77.03937 538.58267 L 64.692913 538.58267 C 60.274635 538.58267 56.692913 535.00095 56.692913 530.58267 L 56.692913 518.23622 C 56.692913 513.81794 60.274635 510.23622 64.692913 510.23622 Z" fill="#a57706"/><path d="M 64.692913 510.23622 L 77.03937 510.23622 C 81.45765 510.23622 85.03937 513.81794 85.03937 518.23622 L 85.03937 530.58267 C 85.03937 535.00095 81.45765 538.58267 77.03937 538.58267 L 64.692913 538.58267 C 60.274635 538.58267 56.692913 535.00095 56.692913 530.58267 L 56.692913 518.23622 C 56.692913 513.81794 60.274635 510.23622 64.692913 510.23622 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 519.40945)" fill="#fcf4dc"><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.278697" y="8" textLength="7.1132812">(1</tspan><tspan font-family="Helvetica" font-size="8" font-weight="bold" fill="#fcf4dc" x="8.954478" y="8" textLength="7.1132812">1)</tspan></text></g><text transform="translate(302.35093 475.55534) rotate(-60.911396)" fill="#2176c7"><tspan font-family="Helvetica" font-size="8" font-weight="500" fill="#2176c7" x=".046875" y="8" textLength="28.90625">VNI 101</tspan></text></g></g></svg>
