# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

# import paramiko

# from oslo_config import cfg
from oslo_log import log as logging

from neutron import privileged

LOG = logging.getLogger(__name__)


@privileged.default.entrypoint
def write_sysfs(path, value):
    LOG.info("Writing value %(value)s to %(path)s", {"value": value,
                                                     "path": path})
    try:
        with open(path, "w") as f:
            f.write(str(value))
    except Exception as err:
        LOG.error("Failed to write file %s with value %s, error: %s",
                  path, value, err)


def write_sysfs_remote(path, value):
    """Writing value to remote host file path.

    # reserved for future use.
    try:
        ssh = paramiko.SSHClient()
        host = cfg.CONF.OVS.qos_sysfs_ssh_host
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host,
                    cfg.CONF.OVS.qos_sysfs_ssh_port,
                    cfg.CONF.OVS.qos_sysfs_ssh_user)
        # TODO(liuyulong): execmd = 'echo %s > %s' % (value, path)
        execmd = "echo"
        stdin, stdout, stderr = ssh.exec_command(execmd)  # noqa
    except Exception as err:
        LOG.error("Failed to write host %s file %s with value %s, error: %s",
                  host, path, value, err)
    else:
        LOG.debug("Ssh to host %s with stdin: %s, stdout: %s, stderr: %s",
                  host, stdin, stdout, stderr)
    finally:
        ssh.close()
    """
    LOG.info("Writing value %(value)s to remote host %(path)s",
             {"value": value, "path": path})
