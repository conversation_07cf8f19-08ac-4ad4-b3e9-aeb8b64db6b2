<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -12 476 420" width="476pt" height="35pc" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.5.2 <dc:date>2016-04-26 14:57:28 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><path d="M 36.346457 28.346457 L 162.07874 28.346457 C 166.49702 28.346457 170.07874 31.928179 170.07874 36.346457 L 170.07874 125.228346 C 170.07874 129.64662 166.49702 133.228346 162.07874 133.228346 L 36.346457 133.228346 C 31.928179 133.228346 28.346457 129.64662 28.346457 125.228346 L 28.346457 36.346457 C 28.346457 31.928179 31.928179 28.346457 36.346457 28.346457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 33.346457)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="19.591727" y="13" textLength="32.44336">Contr</tspan><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="51.794852" y="13" textLength="60.345703">oller Node</tspan></text><path d="M 192.25197 28.346457 L 317.98425 28.346457 C 322.40253 28.346457 325.98425 31.928179 325.98425 36.346457 L 325.98425 125.228346 C 325.98425 129.64662 322.40253 133.228346 317.98425 133.228346 L 192.25197 133.228346 C 187.83369 133.228346 184.25197 129.64662 184.25197 125.228346 L 184.25197 36.346457 C 184.25197 31.928179 187.83369 28.346457 192.25197 28.346457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 33.346457)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="17.198173" y="13" textLength="97.33594">Compute Node 1</tspan></text><g filter="url(#Shadow)"><path d="M 42.015748 56.692913 L 82.70866 56.692913 C 87.12694 56.692913 90.70866 60.274635 90.70866 64.692913 L 90.70866 77.03937 C 90.70866 81.45765 87.12694 85.03937 82.70866 85.03937 L 42.015748 85.03937 C 37.59747 85.03937 34.015748 81.45765 34.015748 77.03937 L 34.015748 64.692913 C 34.015748 60.274635 37.59747 56.692913 42.015748 56.692913 Z" fill="#fdf5dd"/><path d="M 42.015748 56.692913 L 82.70866 56.692913 C 87.12694 56.692913 90.70866 60.274635 90.70866 64.692913 L 90.70866 77.03937 C 90.70866 81.45765 87.12694 85.03937 82.70866 85.03937 L 42.015748 85.03937 C 37.59747 85.03937 34.015748 81.45765 34.015748 77.03937 L 34.015748 64.692913 C 34.015748 60.274635 37.59747 56.692913 42.015748 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(39.015748 59.86614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.492941" y="9" textLength="11.707031">1-2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.381613" y="20" textLength="15.9296875">CPU</tspan></text></g><path d="M 348.15748 28.346457 L 473.88976 28.346457 C 478.30804 28.346457 481.88976 31.928179 481.88976 36.346457 L 481.88976 167.74803 C 481.88976 172.16631 478.30804 175.74803 473.88976 175.74803 L 348.15748 175.74803 C 343.7392 175.74803 340.15748 172.16631 340.15748 167.74803 L 340.15748 36.346457 C 340.15748 31.928179 343.7392 28.346457 348.15748 28.346457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><text transform="translate(345.15748 33.346457)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="4.4423132" y="13" textLength="58.335938">Block Stor</tspan><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="62.538016" y="13" textLength="64.751953">age Node 1</tspan></text><path d="M 36.346457 155.90551 L 162.07874 155.90551 C 166.49702 155.90551 170.07874 159.48723 170.07874 163.90551 L 170.07874 295.30708 C 170.07874 299.72536 166.49702 303.30708 162.07874 303.30708 L 36.346457 303.30708 C 31.928179 303.30708 28.346457 299.72536 28.346457 295.30708 L 28.346457 163.90551 C 28.346457 159.48723 31.928179 155.90551 36.346457 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><text transform="translate(33.346457 160.90551)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="1.2225866" y="13" textLength="64.77539">Object Stor</tspan><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="65.757743" y="13" textLength="64.751953">age Node 1</tspan></text><path d="M 192.25197 155.90551 L 317.98425 155.90551 C 322.40253 155.90551 325.98425 159.48723 325.98425 163.90551 L 325.98425 295.30708 C 325.98425 299.72536 322.40253 303.30708 317.98425 303.30708 L 192.25197 303.30708 C 187.83369 303.30708 184.25197 299.72536 184.25197 295.30708 L 184.25197 163.90551 C 184.25197 159.48723 187.83369 155.90551 192.25197 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><text transform="translate(189.25197 160.90551)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="1.2225866" y="13" textLength="64.77539">Object Stor</tspan><tspan font-family="Open Sans" font-size="12" font-weight="bold" fill="#536870" x="65.757743" y="13" textLength="64.751953">age Node 2</tspan></text><text transform="translate(164.51968 2.6653543)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".453125" y="17" textLength="27.234375">Har</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="27.367188" y="17" textLength="37.679688">dwar</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="64.726562" y="17" textLength="62.203125">e Requir</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="126.609375" y="17" textLength="55.9375">ements</tspan></text><path d="M 37.562991 325.98425 L 78.255904 325.98425 C 82.67418 325.98425 86.255904 329.56597 86.255904 333.98425 L 86.255904 346.3307 C 86.255904 350.74898 82.67418 354.3307 78.255904 354.3307 L 37.562991 354.3307 C 33.144713 354.3307 29.562991 350.74898 29.562991 346.3307 L 29.562991 333.98425 C 29.562991 329.56597 33.144713 325.98425 37.562991 325.98425 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><path d="M 37.562991 368.50393 L 78.255904 368.50393 C 82.67418 368.50393 86.255904 372.08566 86.255904 376.50393 L 86.255904 388.8504 C 86.255904 393.26867 82.67418 396.8504 78.255904 396.8504 L 37.562991 396.8504 C 33.144713 396.8504 29.562991 393.26867 29.562991 388.8504 L 29.562991 376.50393 C 29.562991 372.08566 33.144713 368.50393 37.562991 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><text transform="translate(90.92126 333.8189)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x=".4375" y="11" textLength="16.430664">Cor</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="16.667969" y="11" textLength="61.89453">e component</tspan></text><text transform="translate(91.42126 376.33858)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x=".23583984" y="11" textLength="96.52832">Optional component</tspan></text><g filter="url(#Shadow)"><path d="M 115.716535 56.692913 L 156.40945 56.692913 C 160.82773 56.692913 164.40945 60.274635 164.40945 64.692913 L 164.40945 77.03937 C 164.40945 81.45765 160.82773 85.03937 156.40945 85.03937 L 115.716535 85.03937 C 111.29826 85.03937 107.716535 81.45765 107.716535 77.03937 L 107.716535 64.692913 C 107.716535 60.274635 111.29826 56.692913 115.716535 56.692913 Z" fill="#fdf5dd"/><path d="M 115.716535 56.692913 L 156.40945 56.692913 C 160.82773 56.692913 164.40945 60.274635 164.40945 64.692913 L 164.40945 77.03937 C 164.40945 81.45765 160.82773 85.03937 156.40945 85.03937 L 115.716535 85.03937 C 111.29826 85.03937 107.716535 81.45765 107.716535 77.03937 L 107.716535 64.692913 C 107.716535 60.274635 111.29826 56.692913 115.716535 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(112.716535 59.86614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.479269" y="9" textLength="17.734375">8 GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.453878" y="20" textLength="17.785156">RAM</tspan></text></g><g filter="url(#Shadow)"><path d="M 42.015748 99.2126 L 82.70866 99.2126 C 87.12694 99.2126 90.70866 102.79432 90.70866 107.2126 L 90.70866 119.559054 C 90.70866 123.97733 87.12694 127.559054 82.70866 127.559054 L 42.015748 127.559054 C 37.59747 127.559054 34.015748 123.97733 34.015748 119.559054 L 34.015748 107.2126 C 34.015748 102.79432 37.59747 99.2126 42.015748 99.2126 Z" fill="#fdf5dd"/><path d="M 42.015748 99.2126 L 82.70866 99.2126 C 87.12694 99.2126 90.70866 102.79432 90.70866 107.2126 L 90.70866 119.559054 C 90.70866 123.97733 87.12694 127.559054 82.70866 127.559054 L 42.015748 127.559054 C 37.59747 127.559054 34.015748 123.97733 34.015748 119.559054 L 34.015748 107.2126 C 34.015748 102.79432 37.59747 99.2126 42.015748 99.2126 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(39.015748 102.385826)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="9.912863" y="9" textLength="26.867188">100 GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.637472" y="20" textLength="15.878906">Stor</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.356222" y="20" textLength="13.699219">age</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92126 56.692913 L 238.61417 56.692913 C 243.03245 56.692913 246.61417 60.274635 246.61417 64.692913 L 246.61417 77.03937 C 246.61417 81.45765 243.03245 85.03937 238.61417 85.03937 L 197.92126 85.03937 C 193.50298 85.03937 189.92126 81.45765 189.92126 77.03937 L 189.92126 64.692913 C 189.92126 60.274635 193.50298 56.692913 197.92126 56.692913 Z" fill="#fdf5dd"/><path d="M 197.92126 56.692913 L 238.61417 56.692913 C 243.03245 56.692913 246.61417 60.274635 246.61417 64.692913 L 246.61417 77.03937 C 246.61417 81.45765 243.03245 85.03937 238.61417 85.03937 L 197.92126 85.03937 C 193.50298 85.03937 189.92126 81.45765 189.92126 77.03937 L 189.92126 64.692913 C 189.92126 60.274635 193.50298 56.692913 197.92126 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(194.92126 59.86614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.209738" y="9" textLength="16.2734375">2-4+</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.381613" y="20" textLength="15.9296875">CPU</tspan></text></g><g filter="url(#Shadow)"><path d="M 271.62205 56.692913 L 312.31496 56.692913 C 316.73324 56.692913 320.31496 60.274635 320.31496 64.692913 L 320.31496 77.03937 C 320.31496 81.45765 316.73324 85.03937 312.31496 85.03937 L 271.62205 85.03937 C 267.20377 85.03937 263.62205 81.45765 263.62205 77.03937 L 263.62205 64.692913 C 263.62205 60.274635 267.20377 56.692913 271.62205 56.692913 Z" fill="#fdf5dd"/><path d="M 271.62205 56.692913 L 312.31496 56.692913 C 316.73324 56.692913 320.31496 60.274635 320.31496 64.692913 L 320.31496 77.03937 C 320.31496 81.45765 316.73324 85.03937 312.31496 85.03937 L 271.62205 85.03937 C 267.20377 85.03937 263.62205 81.45765 263.62205 77.03937 L 263.62205 64.692913 C 263.62205 60.274635 267.20377 56.692913 271.62205 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(268.62205 59.86614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="12.196066" y="9" textLength="22.300781">8+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.453878" y="20" textLength="17.785156">RAM</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92126 99.2126 L 238.61417 99.2126 C 243.03245 99.2126 246.61417 102.79432 246.61417 107.2126 L 246.61417 119.559054 C 246.61417 123.97733 243.03245 127.559054 238.61417 127.559054 L 197.92126 127.559054 C 193.50298 127.559054 189.92126 123.97733 189.92126 119.559054 L 189.92126 107.2126 C 189.92126 102.79432 193.50298 99.2126 197.92126 99.2126 Z" fill="#fdf5dd"/><path d="M 197.92126 99.2126 L 238.61417 99.2126 C 243.03245 99.2126 246.61417 102.79432 246.61417 107.2126 L 246.61417 119.559054 C 246.61417 123.97733 243.03245 127.559054 238.61417 127.559054 L 197.92126 127.559054 C 193.50298 127.559054 189.92126 123.97733 189.92126 119.559054 L 189.92126 107.2126 C 189.92126 102.79432 193.50298 99.2126 197.92126 99.2126 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(194.92126 102.385826)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.6296597" y="9" textLength="31.433594">100+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.637472" y="20" textLength="15.878906">Stor</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.356222" y="20" textLength="13.699219">age</tspan></text></g><g filter="url(#Shadow)"><path d="M 353.82677 56.692913 L 394.51968 56.692913 C 398.93796 56.692913 402.51968 60.274635 402.51968 64.692913 L 402.51968 77.03937 C 402.51968 81.45765 398.93796 85.03937 394.51968 85.03937 L 353.82677 85.03937 C 349.4085 85.03937 345.82677 81.45765 345.82677 77.03937 L 345.82677 64.692913 C 345.82677 60.274635 349.4085 56.692913 353.82677 56.692913 Z" fill="#fdf5dd"/><path d="M 353.82677 56.692913 L 394.51968 56.692913 C 398.93796 56.692913 402.51968 60.274635 402.51968 64.692913 L 402.51968 77.03937 C 402.51968 81.45765 398.93796 85.03937 394.51968 85.03937 L 353.82677 85.03937 C 349.4085 85.03937 345.82677 81.45765 345.82677 77.03937 L 345.82677 64.692913 C 345.82677 60.274635 349.4085 56.692913 353.82677 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(350.82677 59.86614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.492941" y="9" textLength="11.707031">1-2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.381613" y="20" textLength="15.9296875">CPU</tspan></text></g><g filter="url(#Shadow)"><path d="M 427.52756 56.692913 L 468.22047 56.692913 C 472.63875 56.692913 476.22047 60.274635 476.22047 64.692913 L 476.22047 77.03937 C 476.22047 81.45765 472.63875 85.03937 468.22047 85.03937 L 427.52756 85.03937 C 423.10928 85.03937 419.52756 81.45765 419.52756 77.03937 L 419.52756 64.692913 C 419.52756 60.274635 423.10928 56.692913 427.52756 56.692913 Z" fill="#fdf5dd"/><path d="M 427.52756 56.692913 L 468.22047 56.692913 C 472.63875 56.692913 476.22047 60.274635 476.22047 64.692913 L 476.22047 77.03937 C 476.22047 81.45765 472.63875 85.03937 468.22047 85.03937 L 427.52756 85.03937 C 423.10928 85.03937 419.52756 81.45765 419.52756 77.03937 L 419.52756 64.692913 C 419.52756 60.274635 423.10928 56.692913 427.52756 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(424.52756 59.86614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.479269" y="9" textLength="17.734375">4 GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.453878" y="20" textLength="17.785156">RAM</tspan></text></g><g filter="url(#Shadow)"><path d="M 115.716535 99.2126 L 156.40945 99.2126 C 160.82773 99.2126 164.40945 102.79432 164.40945 107.2126 L 164.40945 119.559054 C 164.40945 123.97733 160.82773 127.559054 156.40945 127.559054 L 115.716535 127.559054 C 111.29826 127.559054 107.716535 123.97733 107.716535 119.559054 L 107.716535 107.2126 C 107.716535 102.79432 111.29826 99.2126 115.716535 99.2126 Z" fill="#fdf5dd"/><path d="M 115.716535 99.2126 L 156.40945 99.2126 C 160.82773 99.2126 164.40945 102.79432 164.40945 107.2126 L 164.40945 119.559054 C 164.40945 123.97733 160.82773 127.559054 156.40945 127.559054 L 115.716535 127.559054 C 111.29826 127.559054 107.716535 123.97733 107.716535 119.559054 L 107.716535 107.2126 C 107.716535 102.79432 111.29826 99.2126 115.716535 99.2126 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(112.716535 102.385826)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.063253" y="9" textLength="4.5664062">2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.457785" y="20" textLength="13.777344">NIC</tspan></text></g><g filter="url(#Shadow)"><path d="M 271.62205 99.2126 L 312.31496 99.2126 C 316.73324 99.2126 320.31496 102.79432 320.31496 107.2126 L 320.31496 119.559054 C 320.31496 123.97733 316.73324 127.559054 312.31496 127.559054 L 271.62205 127.559054 C 267.20377 127.559054 263.62205 123.97733 263.62205 119.559054 L 263.62205 107.2126 C 263.62205 102.79432 267.20377 99.2126 271.62205 99.2126 Z" fill="#fdf5dd"/><path d="M 271.62205 99.2126 L 312.31496 99.2126 C 316.73324 99.2126 320.31496 102.79432 320.31496 107.2126 L 320.31496 119.559054 C 320.31496 123.97733 316.73324 127.559054 312.31496 127.559054 L 271.62205 127.559054 C 267.20377 127.559054 263.62205 123.97733 263.62205 119.559054 L 263.62205 107.2126 C 263.62205 102.79432 267.20377 99.2126 271.62205 99.2126 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(268.62205 102.385826)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.063253" y="9" textLength="4.5664062">2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.457785" y="20" textLength="13.777344">NIC</tspan></text></g><g filter="url(#Shadow)"><path d="M 427.52756 99.2126 L 468.22047 99.2126 C 472.63875 99.2126 476.22047 102.79432 476.22047 107.2126 L 476.22047 119.559054 C 476.22047 123.97733 472.63875 127.559054 468.22047 127.559054 L 427.52756 127.559054 C 423.10928 127.559054 419.52756 123.97733 419.52756 119.559054 L 419.52756 107.2126 C 419.52756 102.79432 423.10928 99.2126 427.52756 99.2126 Z" fill="#fdf5dd"/><path d="M 427.52756 99.2126 L 468.22047 99.2126 C 472.63875 99.2126 476.22047 102.79432 476.22047 107.2126 L 476.22047 119.559054 C 476.22047 123.97733 472.63875 127.559054 468.22047 127.559054 L 427.52756 127.559054 C 423.10928 127.559054 419.52756 123.97733 419.52756 119.559054 L 419.52756 107.2126 C 419.52756 102.79432 423.10928 99.2126 427.52756 99.2126 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(424.52756 102.385826)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.063253" y="9" textLength="4.5664062">1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.457785" y="20" textLength="13.777344">NIC</tspan></text></g><g filter="url(#Shadow)"><path d="M 115.716535 226.77165 L 156.40945 226.77165 C 160.82773 226.77165 164.40945 230.35337 164.40945 234.77165 L 164.40945 247.11811 C 164.40945 251.53639 160.82773 255.11811 156.40945 255.11811 L 115.716535 255.11811 C 111.29826 255.11811 107.716535 251.53639 107.716535 247.11811 L 107.716535 234.77165 C 107.716535 230.35337 111.29826 226.77165 115.716535 226.77165 Z" fill="#fdf5dd"/><path d="M 115.716535 226.77165 L 156.40945 226.77165 C 160.82773 226.77165 164.40945 230.35337 164.40945 234.77165 L 164.40945 247.11811 C 164.40945 251.53639 160.82773 255.11811 156.40945 255.11811 L 115.716535 255.11811 C 111.29826 255.11811 107.716535 251.53639 107.716535 247.11811 L 107.716535 234.77165 C 107.716535 230.35337 111.29826 226.77165 115.716535 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(112.716535 229.94488)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.063253" y="9" textLength="4.5664062">1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.457785" y="20" textLength="13.777344">NIC</tspan></text></g><g filter="url(#Shadow)"><path d="M 271.62205 184.25197 L 312.31496 184.25197 C 316.73324 184.25197 320.31496 187.83369 320.31496 192.25197 L 320.31496 204.59842 C 320.31496 209.0167 316.73324 212.59842 312.31496 212.59842 L 271.62205 212.59842 C 267.20377 212.59842 263.62205 209.0167 263.62205 204.59842 L 263.62205 192.25197 C 263.62205 187.83369 267.20377 184.25197 271.62205 184.25197 Z" fill="#fdf5dd"/><path d="M 271.62205 184.25197 L 312.31496 184.25197 C 316.73324 184.25197 320.31496 187.83369 320.31496 192.25197 L 320.31496 204.59842 C 320.31496 209.0167 316.73324 212.59842 312.31496 212.59842 L 271.62205 212.59842 C 267.20377 212.59842 263.62205 209.0167 263.62205 204.59842 L 263.62205 192.25197 C 263.62205 187.83369 267.20377 184.25197 271.62205 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(268.62205 187.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="12.196066" y="9" textLength="22.300781">4+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.453878" y="20" textLength="17.785156">RAM</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92126 184.25197 L 238.61417 184.25197 C 243.03245 184.25197 246.61417 187.83369 246.61417 192.25197 L 246.61417 204.59842 C 246.61417 209.0167 243.03245 212.59842 238.61417 212.59842 L 197.92126 212.59842 C 193.50298 212.59842 189.92126 209.0167 189.92126 204.59842 L 189.92126 192.25197 C 189.92126 187.83369 193.50298 184.25197 197.92126 184.25197 Z" fill="#fdf5dd"/><path d="M 197.92126 184.25197 L 238.61417 184.25197 C 243.03245 184.25197 246.61417 187.83369 246.61417 192.25197 L 246.61417 204.59842 C 246.61417 209.0167 243.03245 212.59842 238.61417 212.59842 L 197.92126 212.59842 C 193.50298 212.59842 189.92126 209.0167 189.92126 204.59842 L 189.92126 192.25197 C 189.92126 187.83369 193.50298 184.25197 197.92126 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(194.92126 187.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.492941" y="9" textLength="11.707031">1-2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.381613" y="20" textLength="15.9296875">CPU</tspan></text></g><g filter="url(#Shadow)"><path d="M 271.62205 226.77165 L 312.31496 226.77165 C 316.73324 226.77165 320.31496 230.35337 320.31496 234.77165 L 320.31496 247.11811 C 320.31496 251.53639 316.73324 255.11811 312.31496 255.11811 L 271.62205 255.11811 C 267.20377 255.11811 263.62205 251.53639 263.62205 247.11811 L 263.62205 234.77165 C 263.62205 230.35337 267.20377 226.77165 271.62205 226.77165 Z" fill="#fdf5dd"/><path d="M 271.62205 226.77165 L 312.31496 226.77165 C 316.73324 226.77165 320.31496 230.35337 320.31496 234.77165 L 320.31496 247.11811 C 320.31496 251.53639 316.73324 255.11811 312.31496 255.11811 L 271.62205 255.11811 C 267.20377 255.11811 263.62205 251.53639 263.62205 247.11811 L 263.62205 234.77165 C 263.62205 230.35337 267.20377 226.77165 271.62205 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(268.62205 229.94488)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.063253" y="9" textLength="4.5664062">1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.457785" y="20" textLength="13.777344">NIC</tspan></text></g><path d="M 374.17323 113.385826 C 374.17323 113.385826 372.23726 127.63076 382.67716 136.06299 C 393.11706 144.49522 411.02362 143.149605 411.02362 143.149605" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 62.362204 240.94488 C 62.362204 240.94488 60.42624 255.18982 70.86614 263.62205 C 81.30604 272.05427 99.2126 270.70866 99.2126 270.70866" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 220.37918 255.11811 C 221.67179 258.11523 223.67854 261.12376 226.77165 263.62205 C 227.5501 264.25079 228.37005 264.82517 229.221 265.3498" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 62.362204 240.94488 C 62.362204 240.94488 60.42624 269.41085 70.86614 283.46457 C 81.30604 297.51828 99.2126 290.55118 99.2126 290.55118" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 218.45233 255.11811 C 219.14609 264.31817 221.1902 275.95107 226.77165 283.46457 C 227.382 284.28619 228.01786 285.03596 228.67418 285.71948" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 42.015748 226.77165 L 82.70866 226.77165 C 87.12694 226.77165 90.70866 230.35337 90.70866 234.77165 L 90.70866 247.11811 C 90.70866 251.53639 87.12694 255.11811 82.70866 255.11811 L 42.015748 255.11811 C 37.59747 255.11811 34.015748 251.53639 34.015748 247.11811 L 34.015748 234.77165 C 34.015748 230.35337 37.59747 226.77165 42.015748 226.77165 Z" fill="#fdf5dd"/><path d="M 42.015748 226.77165 L 82.70866 226.77165 C 87.12694 226.77165 90.70866 230.35337 90.70866 234.77165 L 90.70866 247.11811 C 90.70866 251.53639 87.12694 255.11811 82.70866 255.11811 L 42.015748 255.11811 C 37.59747 255.11811 34.015748 251.53639 34.015748 247.11811 L 34.015748 234.77165 C 34.015748 230.35337 37.59747 226.77165 42.015748 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(39.015748 229.94488)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.6296597" y="9" textLength="31.433594">100+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.637472" y="20" textLength="15.878906">Stor</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.356222" y="20" textLength="13.699219">age</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92126 226.77165 L 238.61417 226.77165 C 243.03245 226.77165 246.61417 230.35337 246.61417 234.77165 L 246.61417 247.11811 C 246.61417 251.53639 243.03245 255.11811 238.61417 255.11811 L 197.92126 255.11811 C 193.50298 255.11811 189.92126 251.53639 189.92126 247.11811 L 189.92126 234.77165 C 189.92126 230.35337 193.50298 226.77165 197.92126 226.77165 Z" fill="#fdf5dd"/><path d="M 197.92126 226.77165 L 238.61417 226.77165 C 243.03245 226.77165 246.61417 230.35337 246.61417 234.77165 L 246.61417 247.11811 C 246.61417 251.53639 243.03245 255.11811 238.61417 255.11811 L 197.92126 255.11811 C 193.50298 255.11811 189.92126 251.53639 189.92126 247.11811 L 189.92126 234.77165 C 189.92126 230.35337 193.50298 226.77165 197.92126 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(194.92126 229.94488)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.6296597" y="9" textLength="31.433594">100+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.637472" y="20" textLength="15.878906">Stor</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.356222" y="20" textLength="13.699219">age</tspan></text></g><g filter="url(#Shadow)"><path d="M 389.76378 136.06299 L 432.28346 136.06299 C 436.1973 136.06299 439.37008 139.23578 439.37008 143.149605 L 439.37008 143.149605 C 439.37008 147.06343 436.1973 150.23622 432.28346 150.23622 L 389.76378 150.23622 C 385.84995 150.23622 382.67716 147.06343 382.67716 143.149605 L 382.67716 143.149605 C 382.67716 139.23578 385.84995 136.06299 389.76378 136.06299 Z" fill="#fdf5dd"/><path d="M 389.76378 136.06299 L 432.28346 136.06299 C 436.1973 136.06299 439.37008 139.23578 439.37008 143.149605 L 439.37008 143.149605 C 439.37008 147.06343 436.1973 150.23622 432.28346 150.23622 L 389.76378 150.23622 C 385.84995 150.23622 382.67716 147.06343 382.67716 143.149605 L 382.67716 143.149605 C 382.67716 139.23578 385.84995 136.06299 389.76378 136.06299 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(387.67716 137.649605)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.4363003" y="9" textLength="12.714844">/de</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="18.990988" y="9" textLength="21.265625">v/sdb</tspan></text></g><g filter="url(#Shadow)"><path d="M 77.952755 263.62205 L 120.47244 263.62205 C 124.38627 263.62205 127.559054 266.79483 127.559054 270.70866 L 127.559054 270.70866 C 127.559054 274.62249 124.38627 277.79527 120.47244 277.79527 L 77.952755 277.79527 C 74.038927 277.79527 70.86614 274.62249 70.86614 270.70866 L 70.86614 270.70866 C 70.86614 266.79483 74.038927 263.62205 77.952755 263.62205 Z" fill="#fdf5dd"/><path d="M 77.952755 263.62205 L 120.47244 263.62205 C 124.38627 263.62205 127.559054 266.79483 127.559054 270.70866 L 127.559054 270.70866 C 127.559054 274.62249 124.38627 277.79527 120.47244 277.79527 L 77.952755 277.79527 C 74.038927 277.79527 70.86614 274.62249 70.86614 270.70866 L 70.86614 270.70866 C 70.86614 266.79483 74.038927 263.62205 77.952755 263.62205 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(75.86614 265.20866)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.4363003" y="9" textLength="12.714844">/de</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="18.990988" y="9" textLength="21.265625">v/sdb</tspan></text></g><g filter="url(#Shadow)"><path d="M 77.952755 283.46457 L 120.47244 283.46457 C 124.38627 283.46457 127.559054 286.63735 127.559054 290.55118 L 127.559054 290.55118 C 127.559054 294.465 124.38627 297.6378 120.47244 297.6378 L 77.952755 297.6378 C 74.038927 297.6378 70.86614 294.465 70.86614 290.55118 L 70.86614 290.55118 C 70.86614 286.63735 74.038927 283.46457 77.952755 283.46457 Z" fill="#fdf5dd"/><path d="M 77.952755 283.46457 L 120.47244 283.46457 C 124.38627 283.46457 127.559054 286.63735 127.559054 290.55118 L 127.559054 290.55118 C 127.559054 294.465 124.38627 297.6378 120.47244 297.6378 L 77.952755 297.6378 C 74.038927 297.6378 70.86614 294.465 70.86614 290.55118 L 70.86614 290.55118 C 70.86614 286.63735 74.038927 283.46457 77.952755 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(75.86614 285.05118)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.948019" y="9" textLength="12.714844">/de</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="19.502707" y="9" textLength="20.242188">v/sdc</tspan></text></g><g filter="url(#Shadow)"><path d="M 233.85827 263.62205 L 276.37795 263.62205 C 280.29178 263.62205 283.46457 266.79483 283.46457 270.70866 L 283.46457 270.70866 C 283.46457 274.62249 280.29178 277.79527 276.37795 277.79527 L 233.85827 277.79527 C 229.94444 277.79527 226.77165 274.62249 226.77165 270.70866 L 226.77165 270.70866 C 226.77165 266.79483 229.94444 263.62205 233.85827 263.62205 Z" fill="#fdf5dd"/><path d="M 233.85827 263.62205 L 276.37795 263.62205 C 280.29178 263.62205 283.46457 266.79483 283.46457 270.70866 L 283.46457 270.70866 C 283.46457 274.62249 280.29178 277.79527 276.37795 277.79527 L 233.85827 277.79527 C 229.94444 277.79527 226.77165 274.62249 226.77165 270.70866 L 226.77165 270.70866 C 226.77165 266.79483 229.94444 263.62205 233.85827 263.62205 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 265.20866)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.4363003" y="9" textLength="12.714844">/de</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="18.990988" y="9" textLength="21.265625">v/sdb</tspan></text></g><g filter="url(#Shadow)"><path d="M 233.85827 283.46457 L 276.37795 283.46457 C 280.29178 283.46457 283.46457 286.63735 283.46457 290.55118 L 283.46457 290.55118 C 283.46457 294.465 280.29178 297.6378 276.37795 297.6378 L 233.85827 297.6378 C 229.94444 297.6378 226.77165 294.465 226.77165 290.55118 L 226.77165 290.55118 C 226.77165 286.63735 229.94444 283.46457 233.85827 283.46457 Z" fill="#fdf5dd"/><path d="M 233.85827 283.46457 L 276.37795 283.46457 C 280.29178 283.46457 283.46457 286.63735 283.46457 290.55118 L 283.46457 290.55118 C 283.46457 294.465 280.29178 297.6378 276.37795 297.6378 L 233.85827 297.6378 C 229.94444 297.6378 226.77165 294.465 226.77165 290.55118 L 226.77165 290.55118 C 226.77165 286.63735 229.94444 283.46457 233.85827 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 285.05118)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.948019" y="9" textLength="12.714844">/de</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="19.502707" y="9" textLength="20.242188">v/sdc</tspan></text></g><g filter="url(#Shadow)"><path d="M 42.015748 184.25197 L 82.70866 184.25197 C 87.12694 184.25197 90.70866 187.83369 90.70866 192.25197 L 90.70866 204.59842 C 90.70866 209.0167 87.12694 212.59842 82.70866 212.59842 L 42.015748 212.59842 C 37.59747 212.59842 34.015748 209.0167 34.015748 204.59842 L 34.015748 192.25197 C 34.015748 187.83369 37.59747 184.25197 42.015748 184.25197 Z" fill="#fdf5dd"/><path d="M 42.015748 184.25197 L 82.70866 184.25197 C 87.12694 184.25197 90.70866 187.83369 90.70866 192.25197 L 90.70866 204.59842 C 90.70866 209.0167 87.12694 212.59842 82.70866 212.59842 L 42.015748 212.59842 C 37.59747 212.59842 34.015748 209.0167 34.015748 204.59842 L 34.015748 192.25197 C 34.015748 187.83369 37.59747 184.25197 42.015748 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(39.015748 187.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.492941" y="9" textLength="11.707031">1-2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.381613" y="20" textLength="15.9296875">CPU</tspan></text></g><g filter="url(#Shadow)"><path d="M 115.716535 184.25197 L 156.40945 184.25197 C 160.82773 184.25197 164.40945 187.83369 164.40945 192.25197 L 164.40945 204.59842 C 164.40945 209.0167 160.82773 212.59842 156.40945 212.59842 L 115.716535 212.59842 C 111.29826 212.59842 107.716535 209.0167 107.716535 204.59842 L 107.716535 192.25197 C 107.716535 187.83369 111.29826 184.25197 115.716535 184.25197 Z" fill="#fdf5dd"/><path d="M 115.716535 184.25197 L 156.40945 184.25197 C 160.82773 184.25197 164.40945 187.83369 164.40945 192.25197 L 164.40945 204.59842 C 164.40945 209.0167 160.82773 212.59842 156.40945 212.59842 L 115.716535 212.59842 C 111.29826 212.59842 107.716535 209.0167 107.716535 204.59842 L 107.716535 192.25197 C 107.716535 187.83369 111.29826 184.25197 115.716535 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(112.716535 187.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="12.196066" y="9" textLength="22.300781">4+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="14.453878" y="20" textLength="17.785156">RAM</tspan></text></g><path d="M 374.17323 113.385826 C 374.17323 113.385826 372.23726 141.8518 382.67716 155.90551 C 383.2875 156.72713 383.92338 157.4769 384.5797 158.16042" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 353.82677 99.2126 L 394.51968 99.2126 C 398.93796 99.2126 402.51968 102.79432 402.51968 107.2126 L 402.51968 119.559054 C 402.51968 123.97733 398.93796 127.559054 394.51968 127.559054 L 353.82677 127.559054 C 349.4085 127.559054 345.82677 123.97733 345.82677 119.559054 L 345.82677 107.2126 C 345.82677 102.79432 349.4085 99.2126 353.82677 99.2126 Z" fill="#fdf5dd"/><path d="M 353.82677 99.2126 L 394.51968 99.2126 C 398.93796 99.2126 402.51968 102.79432 402.51968 107.2126 L 402.51968 119.559054 C 402.51968 123.97733 398.93796 127.559054 394.51968 127.559054 L 353.82677 127.559054 C 349.4085 127.559054 345.82677 123.97733 345.82677 119.559054 L 345.82677 107.2126 C 345.82677 102.79432 349.4085 99.2126 353.82677 99.2126 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(350.82677 102.385826)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.6296597" y="9" textLength="31.433594">100+ GB</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.637472" y="20" textLength="15.878906">Stor</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.356222" y="20" textLength="13.699219">age</tspan></text></g><g filter="url(#Shadow)"><path d="M 389.76378 155.90551 L 432.28346 155.90551 C 436.1973 155.90551 439.37008 159.0783 439.37008 162.992125 L 439.37008 162.992125 C 439.37008 166.90595 436.1973 170.07874 432.28346 170.07874 L 389.76378 170.07874 C 385.84995 170.07874 382.67716 166.90595 382.67716 162.992125 L 382.67716 162.992125 C 382.67716 159.0783 385.84995 155.90551 389.76378 155.90551 Z" fill="#fdf5dd"/><path d="M 389.76378 155.90551 L 432.28346 155.90551 C 436.1973 155.90551 439.37008 159.0783 439.37008 162.992125 L 439.37008 162.992125 C 439.37008 166.90595 436.1973 170.07874 432.28346 170.07874 L 389.76378 170.07874 C 385.84995 170.07874 382.67716 166.90595 382.67716 162.992125 L 382.67716 162.992125 C 382.67716 159.0783 385.84995 155.90551 389.76378 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(387.67716 157.492125)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.948019" y="9" textLength="12.714844">/de</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="19.502707" y="9" textLength="20.242188">v/sdc</tspan></text></g></g></g></svg>
