#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc


class ExtensionFlowCheckBase(object):
    def __init__(self, br_int):
        self.int_br = br_int

    @property
    def reports(self):
        return self.int_br.reports

    def clear(self):
        del self.int_br.reports[:]

    @abc.abstractmethod
    def prepare_flow(self, port_info):
        pass

    @abc.abstractmethod
    def do_check(self, context, result_map, ports):
        pass
