# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from oslo_log import log as logging

from neutron.agent.common import ovs_lib
from neutron.agent.l3 import dvr_fip_ns
from neutron.agent.l3 import dvr_snat_ns
from neutron.agent.l3 import namespace_manager
from neutron.agent.l3 import namespaces
from neutron.agent.linux import interface as ovs_interface

LOG = logging.getLogger(__name__)


class IPTablesManagerMock(object):
    """An object to handle l3 agent's use of iptables from the router"""

    class CatchClass(object):

        def __init__(self, type):
            self.type = type

        def add_rule(self, c, r):
            pass

    def __init__(self):
        self.ipv4 = {}
        self.ipv6 = {}

        types = ['filter', 'mangle', 'nat']

        for type in types:
            self.ipv4[type] = self.CatchClass("IPV4_" + type)
            self.ipv6[type] = self.CatchClass("IPV6_" + type)

    def apply(self):
        pass


class NameSpaceManagerMock(namespace_manager.NamespaceManager):

    class OVSNamespace(object):

        def __init__(self, name, agent_conf, driver, use_ipv6):
            self.name = "dvr-" + name
            self.br = ovs_lib.OVSBridge(self.name)
            self.agent_conf = agent_conf
            self.driver = driver
            self.use_ipv6 = use_ipv6

        def create(self):
            self.br.create()

        def delete(self):
            self.br.destroy()

        def exists(self):
            return self.br.bridge_exists(self.name)

    ns_prefix_to_class_map = {
        "dvr-": OVSNamespace,
        namespaces.NS_PREFIX: namespaces.RouterNamespace,
        dvr_snat_ns.SNAT_NS_PREFIX: dvr_snat_ns.SnatNamespace,
        dvr_fip_ns.FIP_NS_PREFIX: dvr_fip_ns.FipNamespace,
    }

    def __init__(self, conf, driver, metadata_driver):
        self.conf = conf
        self.driver = driver
        self.metadata_driver = metadata_driver
        self._clean_stale = True
        self.br = ovs_lib.OVSBridge("br-int")

    def keep_router(self, router_id):
        self._ids_to_keep.add(router_id[:10])

    def list_all(self):
        bridges = self.br.get_bridges()
        bridges = [x for x in bridges if "br-" not in x]
        return bridges

    def _cleanup(self, ns_prefix, ns_id):
        try:
            if not ns_prefix or not ns_id:
                return
            self.br.delete_bridge(ns_prefix + ns_id)
        except RuntimeError:
            LOG.exception('Failed to destroy stale dvr_bridge %s',
                          ns_prefix + ns_id)


class OVSInterfaceDriver(ovs_interface.OVSInterfaceDriver):

    def get_ipv6_llas(self, device_name, namespace):
        return []

    def add_ipv6_addr(self, device_name, v6addr, namespace,
                      scope='global'):
        pass

    def delete_ipv6_addr(self, device_name, v6addr, namespace):
        pass

    def plug_new(self, network_id, port_id, device_name, mac_address,
                 bridge=None, namespace=None, prefix=None, mtu=None):
        pass

    def plug(self, network_id, port_id, device_name, mac_address,
             bridge=None, namespace=None, prefix=None, mtu=None):
        pass

    def unplug(self, device_name, bridge=None, namespace=None,
               prefix=None):
        pass
