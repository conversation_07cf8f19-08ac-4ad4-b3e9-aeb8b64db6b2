<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 533 444" width="533pt" height="37pc" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 17:55:59 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><text transform="translate(129.72441 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".3515625" y="17" textLength="285.03906">Open vSwitch - High-availability with D</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="285.23438" y="17" textLength="19.414062">VR</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="24.841797" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="81.79492" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="86.453125" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="104.3125" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="129.37891" y="35" textLength="150.7793">w - North/South Scenario 2</tspan></text><circle cx="42.519685" cy="411.02362" r="8.5039505" fill="#738a05"/><text transform="translate(56.023622 399.01575)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="72.700195">vider network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="94.91406">VLAN 101, 203.0.113.0/24</tspan></text><path d="M 36.346457 56.692913 L 530.58267 56.692913 C 535.00095 56.692913 538.58267 60.274635 538.58267 64.692913 L 538.58267 295.30708 C 538.58267 299.72536 535.00095 303.30708 530.58267 303.30708 L 36.346457 303.30708 C 31.928179 303.30708 28.346457 299.72536 28.346457 295.30708 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="207.5351" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" fill="#fdf5dd"/><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="127.559054" x2="170.07874" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="127.559054" x2="212.59842" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="127.559054" x2="255.11811" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(16)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" fill="#2076c8"/><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7882012" y="9" textLength="14.203125">(14)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" fill="#2076c8"/><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(15)</tspan></text></g><circle cx="42.519685" cy="382.67716" r="8.5039505" fill="#bd3612"/><text transform="translate(56.023622 370.6693)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text><g filter="url(#Shadow)"><path d="M 192.25197 184.25197 L 360.50393 184.25197 C 364.92221 184.25197 368.50393 187.83369 368.50393 192.25197 L 368.50393 238.61417 C 368.50393 243.03245 364.92221 246.61417 360.50393 246.61417 L 192.25197 246.61417 C 187.83369 246.61417 184.25197 243.03245 184.25197 238.61417 L 184.25197 192.25197 C 184.25197 187.83369 187.83369 184.25197 192.25197 184.25197 Z" fill="#fdf5dd"/><path d="M 192.25197 184.25197 L 360.50393 184.25197 C 364.92221 184.25197 368.50393 187.83369 368.50393 192.25197 L 368.50393 238.61417 C 368.50393 243.03245 364.92221 246.61417 360.50393 246.61417 L 192.25197 246.61417 C 187.83369 246.61417 184.25197 243.03245 184.25197 238.61417 L 184.25197 192.25197 C 184.25197 187.83369 187.83369 184.25197 192.25197 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 189.25197)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="43.252937" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="49.471687" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="83.93653" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="77.736824" y="18" textLength="18.77832">br-int</tspan></text></g><path d="M 255.11811 127.559054 C 255.11811 127.559054 238.81769 141.97131 226.77165 170.07874 C 214.72561 198.18616 212.59842 226.77165 212.59842 226.77165" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" fill="#2076c8"/><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(13)</tspan></text></g><g filter="url(#Shadow)"><path d="M 206.4252 212.59842 L 218.77165 212.59842 C 223.18993 212.59842 226.77165 216.18015 226.77165 220.59842 L 226.77165 232.94488 C 226.77165 237.36316 223.18993 240.94488 218.77165 240.94488 L 206.4252 240.94488 C 202.00692 240.94488 198.4252 237.36316 198.4252 232.94488 L 198.4252 220.59842 C 198.4252 216.18015 202.00692 212.59842 206.4252 212.59842 Z" fill="#2076c8"/><path d="M 206.4252 212.59842 L 218.77165 212.59842 C 223.18993 212.59842 226.77165 216.18015 226.77165 220.59842 L 226.77165 232.94488 C 226.77165 237.36316 223.18993 240.94488 218.77165 240.94488 L 206.4252 240.94488 C 202.00692 240.94488 198.4252 237.36316 198.4252 232.94488 L 198.4252 220.59842 C 198.4252 216.18015 202.00692 212.59842 206.4252 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 221.27165)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><circle cx="184.25197" cy="382.67716" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 369.68504)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="93.63281">Self-service network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(197.7559 403.3622)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="7.290039">D</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="7.192383" y="11" textLength="91.875">VR internal network</tspan></text><circle cx="184.25197" cy="411.02362" r="8.5039505" fill="#c71b6f"/><g filter="url(#Shadow)"><path d="M 316.97638 85.03937 L 400.18897 85.03937 C 404.60725 85.03937 408.18897 88.62109 408.18897 93.03937 L 408.18897 153.5748 C 408.18897 157.99308 404.60725 161.5748 400.18897 161.5748 L 316.97638 161.5748 C 312.5581 161.5748 308.97638 157.99308 308.97638 153.5748 L 308.97638 93.03937 C 308.97638 88.62109 312.5581 85.03937 316.97638 85.03937 Z" fill="#fdf5dd"/><path d="M 316.97638 85.03937 L 400.18897 85.03937 C 404.60725 85.03937 408.18897 88.62109 408.18897 93.03937 L 408.18897 153.5748 C 408.18897 157.99308 404.60725 161.5748 400.18897 161.5748 L 316.97638 161.5748 C 312.5581 161.5748 308.97638 157.99308 308.97638 153.5748 L 308.97638 93.03937 C 308.97638 88.62109 312.5581 85.03937 316.97638 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(313.97638 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.6160645" y="9" textLength="74.058594">Distributed Router </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.979346" y="20" textLength="45.253906">Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="31.725684" y="29" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.964942" y="29" textLength="18.521973">outer</tspan></text></g><g filter="url(#Shadow)"><path d="M 433.19685 85.03937 L 516.40945 85.03937 C 520.82772 85.03937 524.40945 88.62109 524.40945 93.03937 L 524.40945 153.5748 C 524.40945 157.99308 520.82772 161.5748 516.40945 161.5748 L 433.19685 161.5748 C 428.77857 161.5748 425.19685 157.99308 425.19685 153.5748 L 425.19685 93.03937 C 425.19685 88.62109 428.77857 85.03937 433.19685 85.03937 Z" fill="#fdf5dd"/><path d="M 433.19685 85.03937 L 516.40945 85.03937 C 520.82772 85.03937 524.40945 88.62109 524.40945 93.03937 L 524.40945 153.5748 C 524.40945 157.99308 520.82772 161.5748 516.40945 161.5748 L 433.19685 161.5748 C 428.77857 161.5748 425.19685 157.99308 425.19685 153.5748 L 425.19685 93.03937 C 425.19685 88.62109 428.77857 85.03937 433.19685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(430.19685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="24.426611" y="9" textLength="40.359375">Floating IP</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.979346" y="20" textLength="45.253906">Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="40.17832" y="29" textLength="4.494629">fi</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="44.67295" y="29" textLength="4.361328">p</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 184.25197 L 133.73228 184.25197 C 138.15056 184.25197 141.73228 187.83369 141.73228 192.25197 L 141.73228 238.61417 C 141.73228 243.03245 138.15056 246.61417 133.73228 246.61417 L 50.519685 246.61417 C 46.101407 246.61417 42.519685 243.03245 42.519685 238.61417 L 42.519685 192.25197 C 42.519685 187.83369 46.101407 184.25197 50.519685 184.25197 Z" fill="#fdf5dd"/><path d="M 50.519685 184.25197 L 133.73228 184.25197 C 138.15056 184.25197 141.73228 187.83369 141.73228 192.25197 L 141.73228 238.61417 C 141.73228 243.03245 138.15056 246.61417 133.73228 246.61417 L 50.519685 246.61417 C 46.101407 246.61417 42.519685 243.03245 42.519685 238.61417 L 42.519685 192.25197 C 42.519685 187.83369 46.101407 184.25197 50.519685 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 189.25197)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="4.9441895" y="9" textLength="8.375"> O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="13.2410645" y="9" textLength="19.824219">VS Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="32.905127" y="9" textLength="4.8867188">o</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="37.63169" y="9" textLength="46.63672">vider Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="25.547705" y="18" textLength="17.01123">br-pr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="42.4188" y="18" textLength="4.275879">o</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.55454" y="18" textLength="17.110352">vider</tspan></text></g><line x1="379.84252" y1="141.73228" x2="453.5433" y2="141.73228" stroke="#c71b6f" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 337.32283 141.73228 C 337.32283 141.73228 337.22722 174.33312 325.98425 198.4252 C 314.74128 222.51727 297.6378 226.77165 297.6378 226.77165" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="484.8392" y1="149.26358" x2="340.15748" y2="226.77165" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="340.15748" x2="70.86614" y2="283.46457" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="283.46457" x2="70.86614" y2="226.77165" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 113.385826 226.77165 C 113.385826 226.77165 135.594566 255.11811 175.74803 255.11811 C 215.9015 255.11811 255.11811 226.77165 255.11811 226.77165" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 212.59842 L 261.29134 212.59842 C 265.70962 212.59842 269.29134 216.18015 269.29134 220.59842 L 269.29134 232.94488 C 269.29134 237.36316 265.70962 240.94488 261.29134 240.94488 L 248.94488 240.94488 C 244.5266 240.94488 240.94488 237.36316 240.94488 232.94488 L 240.94488 220.59842 C 240.94488 216.18015 244.5266 212.59842 248.94488 212.59842 Z" fill="#708284"/><path d="M 248.94488 212.59842 L 261.29134 212.59842 C 265.70962 212.59842 269.29134 216.18015 269.29134 220.59842 L 269.29134 232.94488 C 269.29134 237.36316 265.70962 240.94488 261.29134 240.94488 L 248.94488 240.94488 C 244.5266 240.94488 240.94488 237.36316 240.94488 232.94488 L 240.94488 220.59842 C 240.94488 216.18015 244.5266 212.59842 248.94488 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 221.27165)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><g filter="url(#Shadow)"><path d="M 373.6693 127.559054 L 386.01575 127.559054 C 390.43402 127.559054 394.01575 131.14078 394.01575 135.559054 L 394.01575 147.90551 C 394.01575 152.32379 390.43402 155.90551 386.01575 155.90551 L 373.6693 155.90551 C 369.25101 155.90551 365.6693 152.32379 365.6693 147.90551 L 365.6693 135.559054 C 365.6693 131.14078 369.25101 127.559054 373.6693 127.559054 Z" fill="#c71b6f"/><path d="M 373.6693 127.559054 L 386.01575 127.559054 C 390.43402 127.559054 394.01575 131.14078 394.01575 135.559054 L 394.01575 147.90551 C 394.01575 152.32379 390.43402 155.90551 386.01575 155.90551 L 373.6693 155.90551 C 369.25101 155.90551 365.6693 152.32379 365.6693 147.90551 L 365.6693 135.559054 C 365.6693 131.14078 369.25101 127.559054 373.6693 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(370.6693 136.23228)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><g filter="url(#Shadow)"><path d="M 331.1496 127.559054 L 343.49606 127.559054 C 347.91434 127.559054 351.49606 131.14078 351.49606 135.559054 L 351.49606 147.90551 C 351.49606 152.32379 347.91434 155.90551 343.49606 155.90551 L 331.1496 155.90551 C 326.73133 155.90551 323.1496 152.32379 323.1496 147.90551 L 323.1496 135.559054 C 323.1496 131.14078 326.73133 127.559054 331.1496 127.559054 Z" fill="#2076c8"/><path d="M 331.1496 127.559054 L 343.49606 127.559054 C 347.91434 127.559054 351.49606 131.14078 351.49606 135.559054 L 351.49606 147.90551 C 351.49606 152.32379 347.91434 155.90551 343.49606 155.90551 L 331.1496 155.90551 C 326.73133 155.90551 323.1496 152.32379 323.1496 147.90551 L 323.1496 135.559054 C 323.1496 131.14078 326.73133 127.559054 331.1496 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(328.1496 136.23228)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(10)</tspan></text></g><g filter="url(#Shadow)"><path d="M 447.37007 127.559054 L 459.71653 127.559054 C 464.1348 127.559054 467.71653 131.14078 467.71653 135.559054 L 467.71653 147.90551 C 467.71653 152.32379 464.1348 155.90551 459.71653 155.90551 L 447.37007 155.90551 C 442.9518 155.90551 439.37007 152.32379 439.37007 147.90551 L 439.37007 135.559054 C 439.37007 131.14078 442.9518 127.559054 447.37007 127.559054 Z" fill="#c71b6f"/><path d="M 447.37007 127.559054 L 459.71653 127.559054 C 464.1348 127.559054 467.71653 131.14078 467.71653 135.559054 L 467.71653 147.90551 C 467.71653 152.32379 464.1348 155.90551 459.71653 155.90551 L 447.37007 155.90551 C 442.9518 155.90551 439.37007 152.32379 439.37007 147.90551 L 439.37007 135.559054 C 439.37007 131.14078 442.9518 127.559054 447.37007 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(444.37007 136.23228)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 492.7244 127.559054 L 505.07086 127.559054 C 509.48914 127.559054 513.07086 131.14078 513.07086 135.559054 L 513.07086 147.90551 C 513.07086 152.32379 509.48914 155.90551 505.07086 155.90551 L 492.7244 155.90551 C 488.30613 155.90551 484.7244 152.32379 484.7244 147.90551 L 484.7244 135.559054 C 484.7244 131.14078 488.30613 127.559054 492.7244 127.559054 Z" fill="#738a05"/><path d="M 492.7244 127.559054 L 505.07086 127.559054 C 509.48914 127.559054 513.07086 131.14078 513.07086 135.559054 L 513.07086 147.90551 C 513.07086 152.32379 509.48914 155.90551 505.07086 155.90551 L 492.7244 155.90551 C 488.30613 155.90551 484.7244 152.32379 484.7244 147.90551 L 484.7244 135.559054 C 484.7244 131.14078 488.30613 127.559054 492.7244 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(489.7244 136.23228)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 212.59842 L 119.559054 212.59842 C 123.97733 212.59842 127.559054 216.18015 127.559054 220.59842 L 127.559054 232.94488 C 127.559054 237.36316 123.97733 240.94488 119.559054 240.94488 L 107.2126 240.94488 C 102.79432 240.94488 99.2126 237.36316 99.2126 232.94488 L 99.2126 220.59842 C 99.2126 216.18015 102.79432 212.59842 107.2126 212.59842 Z" fill="#708284"/><path d="M 107.2126 212.59842 L 119.559054 212.59842 C 123.97733 212.59842 127.559054 216.18015 127.559054 220.59842 L 127.559054 232.94488 C 127.559054 237.36316 123.97733 240.94488 119.559054 240.94488 L 107.2126 240.94488 C 102.79432 240.94488 99.2126 237.36316 99.2126 232.94488 L 99.2126 220.59842 C 99.2126 216.18015 102.79432 212.59842 107.2126 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 221.27165)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 212.59842 L 77.03937 212.59842 C 81.45765 212.59842 85.03937 216.18015 85.03937 220.59842 L 85.03937 232.94488 C 85.03937 237.36316 81.45765 240.94488 77.03937 240.94488 L 64.692913 240.94488 C 60.274635 240.94488 56.692913 237.36316 56.692913 232.94488 L 56.692913 220.59842 C 56.692913 216.18015 60.274635 212.59842 64.692913 212.59842 Z" fill="#bd3612"/><path d="M 64.692913 212.59842 L 77.03937 212.59842 C 81.45765 212.59842 85.03937 216.18015 85.03937 220.59842 L 85.03937 232.94488 C 85.03937 237.36316 81.45765 240.94488 77.03937 240.94488 L 64.692913 240.94488 C 60.274635 240.94488 56.692913 237.36316 56.692913 232.94488 L 56.692913 220.59842 C 56.692913 216.18015 60.274635 212.59842 64.692913 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 221.27165)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 269.29134 L 77.03937 269.29134 C 81.45765 269.29134 85.03937 272.87306 85.03937 277.29134 L 85.03937 289.6378 C 85.03937 294.05607 81.45765 297.6378 77.03937 297.6378 L 64.692913 297.6378 C 60.274635 297.6378 56.692913 294.05607 56.692913 289.6378 L 56.692913 277.29134 C 56.692913 272.87306 60.274635 269.29134 64.692913 269.29134 Z" fill="#bd3612"/><path d="M 64.692913 269.29134 L 77.03937 269.29134 C 81.45765 269.29134 85.03937 272.87306 85.03937 277.29134 L 85.03937 289.6378 C 85.03937 294.05607 81.45765 297.6378 77.03937 297.6378 L 64.692913 297.6378 C 60.274635 297.6378 56.692913 294.05607 56.692913 289.6378 L 56.692913 277.29134 C 56.692913 272.87306 60.274635 269.29134 64.692913 269.29134 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 277.96457)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><path d="M 56.19736 341.67997 C 49.783464 340.15748 52.341165 327.34063 62.57276 329.52756 C 63.522028 325.26453 75.42 325.95647 75.342216 329.52756 C 82.802607 324.9601 92.33654 334.06753 85.941693 338.635 C 93.61519 340.84942 85.84486 352.78044 79.547243 350.7874 C 79.043243 354.10932 67.784995 355.2718 66.796837 350.7874 C 60.421833 355.57653 47.128932 348.21297 56.19736 341.67997 Z" fill="#bd3612"/><path d="M 56.19736 341.67997 C 49.783464 340.15748 52.341165 327.34063 62.57276 329.52756 C 63.522028 325.26453 75.42 325.95647 75.342216 329.52756 C 82.802607 324.9601 92.33654 334.06753 85.941693 338.635 C 93.61519 340.84942 85.84486 352.78044 79.547243 350.7874 C 79.043243 354.10932 67.784995 355.2718 66.796837 350.7874 C 60.421833 355.57653 47.128932 348.21297 56.19736 341.67997 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.976378 334.65748)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.071404" y="9" textLength="9.636719">(1)</tspan></text><g filter="url(#Shadow)"><path d="M 333.98425 212.59842 L 346.3307 212.59842 C 350.74898 212.59842 354.3307 216.18015 354.3307 220.59842 L 354.3307 232.94488 C 354.3307 237.36316 350.74898 240.94488 346.3307 240.94488 L 333.98425 240.94488 C 329.56597 240.94488 325.98425 237.36316 325.98425 232.94488 L 325.98425 220.59842 C 325.98425 216.18015 329.56597 212.59842 333.98425 212.59842 Z" fill="#738a05"/><path d="M 333.98425 212.59842 L 346.3307 212.59842 C 350.74898 212.59842 354.3307 216.18015 354.3307 220.59842 L 354.3307 232.94488 C 354.3307 237.36316 350.74898 240.94488 346.3307 240.94488 L 333.98425 240.94488 C 329.56597 240.94488 325.98425 237.36316 325.98425 232.94488 L 325.98425 220.59842 C 325.98425 216.18015 329.56597 212.59842 333.98425 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 221.27165)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 291.46457 212.59842 L 303.81102 212.59842 C 308.2293 212.59842 311.81102 216.18015 311.81102 220.59842 L 311.81102 232.94488 C 311.81102 237.36316 308.2293 240.94488 303.81102 240.94488 L 291.46457 240.94488 C 287.04629 240.94488 283.46457 237.36316 283.46457 232.94488 L 283.46457 220.59842 C 283.46457 216.18015 287.04629 212.59842 291.46457 212.59842 Z" fill="#2076c8"/><path d="M 291.46457 212.59842 L 303.81102 212.59842 C 308.2293 212.59842 311.81102 216.18015 311.81102 220.59842 L 311.81102 232.94488 C 311.81102 237.36316 308.2293 240.94488 303.81102 240.94488 L 291.46457 240.94488 C 287.04629 240.94488 283.46457 237.36316 283.46457 232.94488 L 283.46457 220.59842 C 283.46457 216.18015 287.04629 212.59842 291.46457 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 221.27165)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><text transform="translate(75.86614 307.48825)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text></g></g></svg>
