function plugin_agent_add_l3_agent_extension {
    local l3_agent_extension=$1
    if [[ -z "$L3_AGENT_EXTENSIONS" ]]; then
        L3_AGENT_EXTENSIONS=$l3_agent_extension
    elif [[ ! ,${L3_AGENT_EXTENSIONS}, =~ ,${l3_agent_extension}, ]]; then
        L3_AGENT_EXTENSIONS+=",$l3_agent_extension"
    fi
}


function configure_l3_agent {
    iniset $NEUTRON_L3_CONF agent extensions "$L3_AGENT_EXTENSIONS"
}
