..
      Copyright 2014 Hewlett-Packard Development Company, L.P.

      Licensed under the Apache License, Version 2.0 (the "License"); you may
      not use this file except in compliance with the License. You may obtain
      a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
      WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
      License for the specific language governing permissions and limitations
      under the License.

Neutron Stadium
================

This section contains information on policies and procedures for the so called
Neutron Stadium. The Neutron Stadium is the list of projects that show up in the
OpenStack `Governance Document <https://governance.openstack.org/tc/reference/projects/neutron.html>`_.

The list includes projects that the Neutron PTL and core team are directly
involved in, and manage on a day to day basis. To do so, the PTL and team
ensure that common practices and guidelines are followed throughout the Stadium,
for all aspects that pertain software development, from inception, to coding,
testing, documentation and more.

The Stadium is not to be intended as a VIP club for OpenStack networking
projects, or an upper tier within OpenStack. It is simply the list of projects
the Neutron team and PTL claim responsibility for when producing Neutron
deliverables throughout the release `cycles <https://github.com/openstack/releases>`_.

For more details on the Stadium, and what it takes for a project to be
considered an integral part of the Stadium, please read on.

.. toctree::
   :maxdepth: 3

   governance
   guidelines
