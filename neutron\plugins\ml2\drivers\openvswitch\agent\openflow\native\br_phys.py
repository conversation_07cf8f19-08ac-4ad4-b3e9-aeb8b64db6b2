# Copyright (C) 2014,2015 VA Linux Systems Japan K.K.
# Copyright (C) 2014,2015 YAMAMOT<PERSON> Ta<PERSON> <yamamoto at valinux co jp>
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from ryu.lib.packet import arp
from ryu.lib.packet import ether_types
from ryu.lib.packet import in_proto

from neutron.common import constants as common_constants
from neutron.plugins.ml2.drivers.openvswitch.agent.common import constants
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.native \
    import br_dvr_process
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.native \
    import ovs_bridge


class OVSPhysicalBridge(ovs_bridge.OVSAgentBridge,
                        br_dvr_process.OVSDVRProcessMixin):
    """openvswitch agent physical bridge specific logic."""

    # Used by OVSDVRProcessMixin
    dvr_process_table_id = constants.DVR_PROCESS_VLAN
    dvr_process_next_table_id = constants.LOCAL_VLAN_TRANSLATION
    of_tables = constants.PHY_BR_ALL_TABLES

    def setup_default_table(self):
        # table=0 in_port=NIC -> direct_output_to_patch -> ingress NORMAL
        self.install_goto(dest_table_id=constants.PHYSICAL_INRESS_OUTPUT)
        self.install_goto(dest_table_id=constants.PHYSICAL_INRESS_NORMAL,
                          table_id=constants.PHYSICAL_INRESS_OUTPUT)
        self.install_normal(table_id=constants.PHYSICAL_INRESS_NORMAL)

        # table=0 in_port=patch -> direct_output_to_NIC -> egress NORMAL
        self.install_goto(dest_table_id=constants.PHYSICAL_EGRESS_NORMAL,
                          table_id=constants.PHYSICAL_EGRESS_OUTPUT)
        self.install_normal(table_id=constants.PHYSICAL_EGRESS_NORMAL)

    def setup_egress_output(self, patch_inport, phy_ofport):
        if not phy_ofport:
            return
        (_dp, _ofp, ofpp) = self._get_dp()
        match = ofpp.OFPMatch(in_port=patch_inport)
        self.install_output(phy_ofport,
                            table_id=constants.PHYSICAL_EGRESS_OUTPUT,
                            priority=10,
                            match=match)

    def setup_ingress_output(self, patch_inport, phy_ofport, segmentation_id):
        if not phy_ofport:
            return
        (_dp, ofp, _ofpp) = self._get_dp()

        # Allow the physical VLAN unicast
        self.install_output(patch_inport,
                            table_id=constants.PHYSICAL_INRESS_OUTPUT,
                            priority=10,
                            in_port=phy_ofport,
                            vlan_vid=segmentation_id | ofp.OFPVID_PRESENT,
                            eth_dst=('00:00:00:00:00:00',
                                     '01:00:00:00:00:00'))
        # Allow the physical VLAN multicast/broadcast
        self.install_output(patch_inport,
                            table_id=constants.PHYSICAL_INRESS_OUTPUT,
                            priority=10,
                            in_port=phy_ofport,
                            vlan_vid=segmentation_id | ofp.OFPVID_PRESENT,
                            eth_dst=('01:00:00:00:00:00',
                                     '01:00:00:00:00:00'))

    def network_path_defaults(self, pvid, to_int_ofport, metadata_ofport,
                              metadata_host_info):
        for table in [constants.NP_EGRESS_NAT,
                      constants.NP_EGRESS_NAT_CLASSIFY,
                      constants.NP_EGRESS_TCP,
                      constants.NP_EGRESS_UDP,
                      constants.NP_PROVIDER_IP_ARP_RESPONDER,
                      constants.NP_INGRESS_DST_DIRECT]:
            self.install_drop(table_id=table)
        self.egress_direct(to_int_ofport)
        self.ingress_direct(pvid, in_port=metadata_ofport)
        self.arp_direct(pvid, in_port=metadata_ofport)
        self.gateway_arp_response(
            pvid,
            metadata_host_info.pop('gateway_ip', '0.0.0.0'))
        self.path_classify_metadata(pvid,
                                    metadata_ofport,
                                    metadata_host_info)

    def baremetal_arp_request_learn(self, vlan_id, bm_ip):
        (_dp, ofp, ofpp) = self._get_dp()
        actions = [
            ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0),
        ]
        self.arp_op_to_controller(vlan_id, arp.ARP_REQUEST, bm_ip,
                                  extra_actions=actions)

    def gateway_arp_response(self, pvid, gateway_ip):
        self.arp_op_to_controller(pvid, arp.ARP_REPLY, gateway_ip)

    def arp_op_to_controller(self, vlan_id, arp_op, arp_spa,
                             extra_actions=None):
        (_dp, ofp, ofpp) = self._get_dp()
        match = ofpp.OFPMatch(vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
                              eth_type=ether_types.ETH_TYPE_ARP,
                              arp_op=arp_op,
                              arp_spa=arp_spa)
        actions = [
            ofpp.OFPActionOutput(ofp.OFPP_CONTROLLER, 0),
        ]
        if extra_actions:
            actions.extend(extra_actions)
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.install_instructions(table_id=constants.LOCAL_SWITCHING,
                                  priority=202,
                                  instructions=instructions,
                                  match=match)

    def remove_arp_op_to_controller(self, vlan_id, arp_op, arp_spa):
        (_dp, ofp, _ofpp) = self._get_dp()
        self.uninstall_flows(table_id=constants.LOCAL_SWITCHING,
                             vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
                             eth_type=ether_types.ETH_TYPE_ARP,
                             arp_op=arp_op,
                             arp_spa=arp_spa)

    def egress_direct(self, port,
                      ipv4_dst=common_constants.METADATA_DEFAULT_IP):
        self.install_goto(table_id=constants.LOCAL_SWITCHING,
                          priority=201,
                          in_port=port,
                          ipv4_dst=ipv4_dst,
                          eth_type=ether_types.ETH_TYPE_IP,
                          dest_table_id=constants.NP_EGRESS_NAT)

    def ingress_direct(self, pvid, in_port=None):
        (_dp, ofp, ofpp) = self._get_dp()
        if not in_port:
            match = ofpp.OFPMatch(vlan_vid=pvid | ofp.OFPVID_PRESENT,
                                  eth_type=ether_types.ETH_TYPE_IP)
        else:
            match = ofpp.OFPMatch(in_port=in_port,
                                  eth_type=ether_types.ETH_TYPE_IP)

        if in_port:
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT)
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(
                            table_id=constants.NP_INGRESS_SRC_PORT)
            ]
        else:
            instructions = [
                ofpp.OFPInstructionGotoTable(
                            table_id=constants.NP_INGRESS_DST_DIRECT)
            ]
        self.install_instructions(table_id=constants.LOCAL_SWITCHING,
                                  priority=201,
                                  instructions=instructions,
                                  match=match)

        # In table 81 goto table 91 by default
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=constants.NP_INGRESS_DST_DIRECT)]
        self.install_instructions(table_id=constants.NP_INGRESS_SRC_PORT,
                                  priority=0,
                                  instructions=instructions)

    def ingress_source_port_change_flow(self, path):
        (_dp, ofp, ofpp) = self._get_dp()
        if path['protocol'] == 'tcp':
            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_src=path['dest_ip'],
                tcp_src=path['dest_port'],
                ip_proto=in_proto.IPPROTO_TCP)
            actions = [
                ofpp.OFPActionSetField(tcp_src=path['orig_port']),
            ]
        elif path['protocol'] == 'udp':
            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_src=path['dest_ip'],
                udp_src=path['dest_port'],
                ip_proto=in_proto.IPPROTO_UDP)
            actions = [
                ofpp.OFPActionSetField(udp_src=path['orig_port']),
            ]
        else:
            return
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=constants.NP_INGRESS_DST_DIRECT)
        ]
        self.install_instructions(table_id=constants.NP_INGRESS_SRC_PORT,
                                  priority=201,
                                  instructions=instructions,
                                  match=match)

    def remove_ingress_source_port_change_flow(self, path):
        if path['protocol'] == 'tcp':
            self.uninstall_flows(
                table_id=constants.NP_INGRESS_SRC_PORT,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_src=path['dest_ip'],
                tcp_src=path['dest_port'],
                ip_proto=in_proto.IPPROTO_TCP)
        elif path['protocol'] == 'udp':
            self.uninstall_flows(
                table_id=constants.NP_INGRESS_SRC_PORT,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_src=path['dest_ip'],
                udp_src=path['dest_port'],
                ip_proto=in_proto.IPPROTO_UDP)

    def arp_direct(self, pvid, in_port=None):
        (_dp, ofp, ofpp) = self._get_dp()
        if not in_port:
            match = ofpp.OFPMatch(vlan_vid=pvid | ofp.OFPVID_PRESENT,
                                  eth_type=ether_types.ETH_TYPE_ARP)
        else:
            match = ofpp.OFPMatch(in_port=in_port,
                                  eth_type=ether_types.ETH_TYPE_ARP)

        instructions = [
            ofpp.OFPInstructionGotoTable(
                table_id=constants.NP_PROVIDER_IP_ARP_RESPONDER)
        ]

        self.install_instructions(table_id=constants.LOCAL_SWITCHING,
                                  priority=201,
                                  instructions=instructions,
                                  match=match)

    def path_classify_metadata(self, pvid, metadata_ofport,
                               metadata_host_info):
        agent_metadata_ip = metadata_host_info.get("provider_ip")
        agent_metadata_mac = metadata_host_info.get("mac_address")
        listen_port = metadata_host_info.get("service_protocol_port")

        if agent_metadata_ip and agent_metadata_mac:
            (_dp, ofp, ofpp) = self._get_dp()
            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=common_constants.METADATA_DEFAULT_IP,
                tcp_dst=80,
                ip_proto=in_proto.IPPROTO_TCP)
            actions = [
                # ofpp.OFPActionPushVlan(),
                # ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
                ofpp.OFPActionSetField(eth_dst=agent_metadata_mac),
                ofpp.OFPActionSetField(ipv4_dst=agent_metadata_ip),
                ofpp.OFPActionSetField(tcp_dst=listen_port),
                ofpp.OFPActionOutput(metadata_ofport, 0),
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ]
            self.install_instructions(
                table_id=constants.NP_EGRESS_NAT_CLASSIFY,
                priority=202,
                match=match,
                instructions=instructions)

    def path_classify_nat(self, ipv4_dst=common_constants.METADATA_DEFAULT_IP):
        (_dp, _ofp, ofpp) = self._get_dp()

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ipv4_dst=ipv4_dst,
                              ip_proto=in_proto.IPPROTO_TCP)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=constants.NP_EGRESS_TCP)]
        self.install_instructions(table_id=constants.NP_EGRESS_NAT_CLASSIFY,
                                  priority=201,
                                  instructions=instructions,
                                  match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ipv4_dst=ipv4_dst,
                              ip_proto=in_proto.IPPROTO_UDP)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=constants.NP_EGRESS_UDP)]
        self.install_instructions(table_id=constants.NP_EGRESS_NAT_CLASSIFY,
                                  priority=201,
                                  instructions=instructions,
                                  match=match)

    def install_ports_networking_path_flow(self, path, pvid, nic_ofport):
        (_dp, ofp, ofpp) = self._get_dp()
        if path['protocol'] == 'tcp':
            table = constants.NP_EGRESS_TCP
            match = ofpp.OFPMatch(
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=path['orig_ip'],
                tcp_dst=path['orig_port'],
                ip_proto=in_proto.IPPROTO_TCP)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
                ofpp.OFPActionSetField(eth_dst=path['mac_address']),
                ofpp.OFPActionSetField(ipv4_dst=path['dest_ip']),
                ofpp.OFPActionSetField(tcp_dst=path['dest_port']),
                ofpp.OFPActionOutput(nic_ofport, 0),
            ]
        elif path['protocol'] == 'udp':
            table = constants.NP_EGRESS_UDP
            match = ofpp.OFPMatch(
                    eth_type=ether_types.ETH_TYPE_IP,
                    ipv4_dst=path['orig_ip'],
                    udp_dst=path['orig_port'],
                    ip_proto=in_proto.IPPROTO_UDP)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
                ofpp.OFPActionSetField(eth_dst=path['mac_address']),
                ofpp.OFPActionSetField(ipv4_dst=path['dest_ip']),
                ofpp.OFPActionSetField(udp_dst=path['dest_port']),
                ofpp.OFPActionOutput(nic_ofport, 0),
            ]
        else:
            return
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.install_instructions(table_id=table,
                                  priority=201,
                                  match=match,
                                  instructions=instructions)

    def remove_ports_networking_path_flow(self, path, pvid, nic_ofport):
        if path['protocol'] == 'tcp':
            table = constants.NP_EGRESS_TCP
            self.uninstall_flows(
                table_id=table,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=path['orig_ip'],
                tcp_dst=path['orig_port'],
                ip_proto=in_proto.IPPROTO_TCP)
        elif path['protocol'] == 'udp':
            table = constants.NP_EGRESS_UDP
            self.uninstall_flows(
                table_id=table,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=path['orig_ip'],
                udp_dst=path['orig_port'],
                ip_proto=in_proto.IPPROTO_UDP)

    @staticmethod
    def _local_vlan_match(ofp, ofpp, port, lvid):
        return ofpp.OFPMatch(in_port=port, vlan_vid=lvid | ofp.OFPVID_PRESENT)

    def provision_local_vlan(self, port, lvid, segmentation_id, distributed):
        table_id = constants.LOCAL_VLAN_TRANSLATION if distributed else 0
        (_dp, ofp, ofpp) = self._get_dp()
        match = self._local_vlan_match(ofp, ofpp, port, lvid)
        if segmentation_id is None:
            actions = [ofpp.OFPActionPopVlan()]
        else:
            vlan_vid = segmentation_id | ofp.OFPVID_PRESENT
            actions = [ofpp.OFPActionSetField(vlan_vid=vlan_vid)]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=constants.PHYSICAL_EGRESS_OUTPUT)
        ]
        self.install_instructions(table_id=table_id,
                                  priority=4,
                                  instructions=instructions,
                                  match=match)

    def provision_local_vlan_service_path(self, port, lvid, segmentation_id):
        (_dp, ofp, ofpp) = self._get_dp()
        match = self._local_vlan_match(ofp, ofpp, port, lvid)
        vlan_vid = segmentation_id | ofp.OFPVID_PRESENT
        actions = [ofpp.OFPActionSetField(vlan_vid=vlan_vid),
                   ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0)]
        self.install_apply_actions(table_id=80,
                                   priority=4,
                                   match=match,
                                   actions=actions)

    def reclaim_local_vlan(self, port, lvid):
        (_dp, ofp, ofpp) = self._get_dp()
        match = self._local_vlan_match(ofp, ofpp, port, lvid)
        self.uninstall_flows(match=match)

    def add_dvr_mac_vlan(self, mac, port):
        self.install_output(table_id=constants.DVR_NOT_LEARN_VLAN,
            priority=2, eth_src=mac, port=port)

    def remove_dvr_mac_vlan(self, mac):
        # REVISIT(yamamoto): match in_port as well?
        self.uninstall_flows(table_id=constants.DVR_NOT_LEARN_VLAN,
            eth_src=mac)
