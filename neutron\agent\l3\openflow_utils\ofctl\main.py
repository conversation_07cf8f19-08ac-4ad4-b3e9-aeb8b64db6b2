#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron.agent.l3.openflow_utils.ofctl import dvr_bridge
from neutron.agent import l3_agent


def main(manager='neutron.agent.l3.agent.L3NATAgentWithStateReport'):
    l3_agent.main(manager=manager,
                  bridge_class=dvr_bridge.L3AgentBridge)
