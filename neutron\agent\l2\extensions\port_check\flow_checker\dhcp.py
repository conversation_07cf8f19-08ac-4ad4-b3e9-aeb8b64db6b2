#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib import constants
from neutron_lib.plugins import utils as p_utils
from oslo_config import cfg

from neutron.agent.common import ovs_lib
from neutron.agent.common import utils
from neutron.agent.l2.extensions.dhcp import extension as dhcp_ext
from neutron.agent.l2.extensions.port_check.flow_checker import base
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const


class DHCPAgentFlowCheck(base.ExtensionFlowCheckBase,
                         dhcp_ext.DHCPAgentExtension):
    def __init__(self, br_int, agent_api, *args, **kwargs):
        super(DHCPAgentFlowCheck, self).__init__(br_int)
        self.enable_tunneling = bool(cfg.CONF.AGENT.tunnel_types or [])
        self.consume_api(agent_api)

    def consume_api(self, agent_api):
        self.agent_api = agent_api

    def allow_nonlocal_dhcp(self):
        for _pnet, bridge in self.agent_api.bridge_mappings.items():
            port_name = p_utils.get_interface_name(
                bridge, prefix=p_const.PEER_INTEGRATION_PREFIX)
            in_port = self.int_br.get_port_ofport(port_name)
            if in_port != ovs_lib.INVALID_OFPORT:
                self.int_br.allow_traditional_dhcp(in_port,
                                                   cfg.CONF.DHCP.enable_ipv6)

        if not self.enable_tunneling:
            return
        tun_name = cfg.CONF.OVS.int_peer_patch_port
        patch_ofport = self.int_br.get_port_ofport(tun_name)
        if patch_ofport != ovs_lib.INVALID_OFPORT:
            self.int_br.allow_traditional_dhcp(patch_ofport,
                                               cfg.CONF.DHCP.enable_ipv6)

    def prepare_flow(self, port_info):
        if cfg.CONF.DHCP.enable_nonlocal_dhcp_req:
            self.allow_nonlocal_dhcp()

        self.int_br.init_dhcp(enable_openflow_dhcp=True,
                              enable_dhcpv6=cfg.CONF.DHCP.enable_ipv6)

        fixed_ips = port_info.get('fixed_ips')
        device_owner = port_info['device_owner']
        extra_allowed_device_owners = cfg.CONF.DHCP.extra_allowed_device_owners
        if (not (device_owner.startswith(
                constants.DEVICE_OWNER_COMPUTE_PREFIX) or (
                    device_owner in extra_allowed_device_owners)) or (
                        not fixed_ips)):
            return
        if device_owner in extra_allowed_device_owners:
            self.int_br.add_dhcp_ipv4_flow(port_info['port_id'],
                                           port_info['ofport'])
        else:
            self.int_br.add_dhcp_ipv4_flow(port_info['port_id'],
                                           port_info['ofport'],
                                           port_info['mac_address'])
        if cfg.CONF.DHCP.enable_ipv6:
            if device_owner in extra_allowed_device_owners:
                self.int_br.add_dhcp_ipv6_flow(port_info['port_id'],
                                               port_info['ofport'])
            else:
                self.int_br.add_dhcp_ipv6_flow(port_info['port_id'],
                                               port_info['ofport'],
                                               port_info['mac_address'])

    def do_check(self, context, result_map, ports):
        for port in ports:
            if not port.device_owner.startswith(
                    constants.DEVICE_OWNER_COMPUTE_PREFIX):
                return
            reports = result_map[port.id]['dhcp']
            port_name = utils.get_port_name_by_id(self.int_br, port.id)
            if not port_name:
                reports.add('port %s not found on bridge %s',
                            port.id, self.int_br.br_name)
                continue
            port_ofport = self.int_br.get_port_ofport(port_name)
            port_info = {"port_id": port.id,
                         "device_owner": port.device_owner,
                         "mac_address": str(port.mac_address),
                         "fixed_ips": [{'ip_address': str(ip['ip_address']),
                                        'subnet_id': ip['subnet_id']}
                                       for ip in port.fixed_ips],
                         "ofport": port_ofport}
            self.clear()
            self.prepare_flow(port_info)
            reports.extend(list(set(self.reports)))
