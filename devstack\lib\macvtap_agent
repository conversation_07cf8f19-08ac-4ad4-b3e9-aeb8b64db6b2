# Neutron Macvtap L2 agent
# ------------------------

# Save trace setting
_XTRACE_NEUTRON_MACVTAP=$(set +o | grep xtrace)
set +o xtrace

function is_neutron_ovs_base_plugin {
    # macvtap doesn't use OVS
    return 1
}

function neutron_plugin_create_nova_conf {
    :
}

function neutron_plugin_install_agent_packages {
    :
}

function neutron_plugin_configure_debug_command {
    :
}

function neutron_plugin_configure_dhcp_agent {
    :
}

function neutron_plugin_configure_l3_agent {
    :
}

function neutron_plugin_configure_plugin_agent {
    # Only the NooPFirewallDriver is supported. If not set, the agent will
    # terminate.
    iniset /$NEUTRON_CORE_PLUGIN_CONF securitygroup firewall_driver noop
    AGENT_BINARY="$NEUTRON_BIN_DIR/neutron-macvtap-agent"
}

function neutron_plugin_setup_interface_driver {
    :
}

function neutron_plugin_check_adv_test_requirements {
    :
}

# Restore xtrace
$_XTRACE_NEUTRON_MACVTAP

