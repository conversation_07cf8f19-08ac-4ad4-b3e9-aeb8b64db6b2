# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

from alembic import op
import sqlalchemy as sa

from neutron_lib.db import constants

"""Add flow log

Revision ID: fffa5194a987
Revises: eb4819f04fb2
Create Date: 2024-07-17 14:57:42.067083

"""

# revision identifiers, used by Alembic.
revision = 'fffa5194a987'
down_revision = 'eb4819f04fb2'

FLOW_LOG = 'flow_logs'
FLOW_LOG_PORT_BINDINGS = 'flow_log_port_bindings'
FLOW_LOG_NETWORK_BINDINGS = 'flow_log_network_bindings'

traffic_type_enum = sa.Enum('all', 'accept', 'reject',
                            name='flow_log_traffic_type')
resource_type_enum = sa.Enum('port', 'network',
                             name='flow_log_resource_type')


def upgrade():
    exist_flow_logs = False
    exist_flow_log_port_bindings = False
    exist_flow_log_network_bindings = False

    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == FLOW_LOG:
            exist_flow_logs = True
        if table == FLOW_LOG_PORT_BINDINGS:
            exist_flow_log_port_bindings = True
        if table == FLOW_LOG_NETWORK_BINDINGS:
            exist_flow_log_network_bindings = True

    if not exist_flow_logs:
        op.create_table(
            FLOW_LOG,
            sa.Column('id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      primary_key=True,
                      nullable=False),
            sa.Column('name', sa.String(length=255), nullable=True),
            sa.Column('project_id',
                      sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                      nullable=True,
                      index=True),
            sa.Column('collection_interval', sa.Integer(), nullable=True),
            sa.Column('enabled', sa.Boolean(), nullable=True),
            # sa.Column('priority', sa.Integer(), nullable=True),
            # sa.Column('resource_type', resource_type_enum, nullable=False),
            # sa.Column('resource_id',
            #           sa.String(length=constants.UUID_FIELD_SIZE),
            #           nullable=False),
            sa.Column('traffic_type', traffic_type_enum, nullable=False),
            sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
            sa.ForeignKeyConstraint(['standard_attr_id'],
                                    ['standardattributes.id'],
                                    ondelete='CASCADE'),
            sa.UniqueConstraint('standard_attr_id'))

    if not exist_flow_log_port_bindings:
        op.create_table(
            FLOW_LOG_PORT_BINDINGS,
            sa.Column('port_id', sa.String(constants.UUID_FIELD_SIZE),
                      sa.ForeignKey('ports.id', ondelete='CASCADE'),
                      nullable=False, unique=True),
            sa.Column('flow_log_id', sa.String(constants.UUID_FIELD_SIZE),
                      sa.ForeignKey('flow_logs.id', ondelete='CASCADE'),
                      nullable=False))

    if not exist_flow_log_network_bindings:
        op.create_table(
            FLOW_LOG_NETWORK_BINDINGS,
            sa.Column('network_id', sa.String(constants.UUID_FIELD_SIZE),
                      sa.ForeignKey('networks.id', ondelete='CASCADE'),
                      nullable=False, unique=True),
            sa.Column('flow_log_id', sa.String(constants.UUID_FIELD_SIZE),
                      sa.ForeignKey('flow_logs.id', ondelete='CASCADE'),
                      nullable=False))
