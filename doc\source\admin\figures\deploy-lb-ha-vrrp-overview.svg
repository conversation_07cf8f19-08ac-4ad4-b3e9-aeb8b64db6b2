<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 476 656" width="476pt" height="656pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-04 14:46:19 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><line x1="113.385826" y1="538.58267" x2="113.385826" y2="416.6929" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="113.385826" y1="538.58267" x2="113.385826" y2="371.33858" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 234.77165 56.692913 L 473.88976 56.692913 C 478.30804 56.692913 481.88976 60.274635 481.88976 64.692913 L 481.88976 281.13386 C 481.88976 285.55213 478.30804 289.13386 473.88976 289.13386 L 234.77165 289.13386 C 230.35337 289.13386 226.77165 285.55213 226.77165 281.13386 L 226.77165 64.692913 C 226.77165 60.274635 230.35337 56.692913 234.77165 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="75.55515" y="13" textLength="94.00781"> Compute Nodes</tspan></text><line x1="354.3307" y1="172.91338" x2="311.81102" y2="180" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(107.52362 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".12890625" y="17" textLength="303.74219">Linux Bridge - High-availability with VRRP</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="126.069336" y="35" textLength="42.767578">Overvie</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="168.59668" y="35" textLength="9.3339844">w</tspan></text><line x1="361.82614" y1="396.8504" x2="425.19685" y2="396.8504" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 397.95483 398.37288 C 386.0433 396.8504 390.79332 384.03354 409.79486 386.22047 C 411.55778 381.95745 433.65401 382.64938 433.50956 386.22047 C 447.36457 381.653 465.07045 390.76044 453.1943 395.3279 C 467.4451 397.54233 453.01447 409.47335 441.3189 407.48031 C 440.3829 410.80223 419.47472 411.96472 417.63957 407.48031 C 405.80027 412.26945 381.11346 404.90589 397.95483 398.37288 Z" fill="#fdf5dd"/><path d="M 397.95483 398.37288 C 386.0433 396.8504 390.79332 384.03354 409.79486 386.22047 C 411.55778 381.95745 433.65401 382.64938 433.50956 386.22047 C 447.36457 381.653 465.07045 390.76044 453.1943 395.3279 C 467.4451 397.54233 453.01447 409.47335 441.3189 407.48031 C 440.3829 410.80223 419.47472 411.96472 417.63957 407.48031 C 405.80027 412.26945 381.11346 404.90589 397.95483 398.37288 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(404.40157 391.3504)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="5.0179317" y="9" textLength="31.554688">Internet</tspan></text><circle cx="184.25197" cy="623.62204" r="8.5039505" fill="#738a05"/><text transform="translate(197.7559 615.96063)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan></text><path d="M 36.346457 56.692913 L 190.4252 56.692913 C 194.84347 56.692913 198.4252 60.274635 198.4252 64.692913 L 198.4252 252.7874 C 198.4252 257.20568 194.84347 260.7874 190.4252 260.7874 L 36.346457 260.7874 C 31.928179 260.7874 28.346457 257.20568 28.346457 252.7874 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="33.71808" y="13" textLength="34.435547"> Contr</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="67.913393" y="13" textLength="58.447266">oller Node</tspan></text><path d="M 70.86614 99.2126 C 70.86614 99.2126 92.83585 101.57882 104.88189 141.73228 C 116.92793 181.88575 113.385826 240.94488 113.385826 240.94488" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="141.73228" x2="113.385826" y2="240.94488" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="129.64497" y1="204.09081" x2="113.385826" y2="240.94488" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 105.385826 C 99.2126 109.804104 95.630876 113.385826 91.2126 113.385826 L 50.519685 113.385826 C 46.101407 113.385826 42.519685 109.804104 42.519685 105.385826 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 105.385826 C 99.2126 109.804104 95.630876 113.385826 91.2126 113.385826 L 50.519685 113.385826 C 46.101407 113.385826 42.519685 109.804104 42.519685 105.385826 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 88.2126)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.8308315" y="9" textLength="15.03125">SQL</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="5.133566" y="20" textLength="36.425781">Database</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 127.559054 L 91.2126 127.559054 C 95.630876 127.559054 99.2126 131.14078 99.2126 135.559054 L 99.2126 147.90551 C 99.2126 152.32379 95.630876 155.90551 91.2126 155.90551 L 50.519685 155.90551 C 46.101407 155.90551 42.519685 152.32379 42.519685 147.90551 L 42.519685 135.559054 C 42.519685 131.14078 46.101407 127.559054 50.519685 127.559054 Z" fill="#fdf5dd"/><path d="M 50.519685 127.559054 L 91.2126 127.559054 C 95.630876 127.559054 99.2126 131.14078 99.2126 135.559054 L 99.2126 147.90551 C 99.2126 152.32379 95.630876 155.90551 91.2126 155.90551 L 50.519685 155.90551 C 46.101407 155.90551 42.519685 152.32379 42.519685 147.90551 L 42.519685 135.559054 C 42.519685 131.14078 46.101407 127.559054 50.519685 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 130.73228)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.606222" y="9" textLength="33.480469">Message</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.219503" y="20" textLength="14.253906">Bus</tspan></text></g><g filter="url(#Shadow)"><path d="M 129.88976 85.03937 L 181.92126 85.03937 C 186.33954 85.03937 189.92126 88.62109 189.92126 93.03937 L 189.92126 196.09449 C 189.92126 200.51276 186.33954 204.09449 181.92126 204.09449 L 129.88976 204.09449 C 125.471485 204.09449 121.88976 200.51276 121.88976 196.09449 L 121.88976 93.03937 C 121.88976 88.62109 125.471485 85.03937 129.88976 85.03937 Z" fill="#fdf5dd"/><path d="M 129.88976 85.03937 L 181.92126 85.03937 C 186.33954 85.03937 189.92126 88.62109 189.92126 93.03937 L 189.92126 196.09449 C 189.92126 200.51276 186.33954 204.09449 181.92126 204.09449 L 129.88976 204.09449 C 125.471485 204.09449 121.88976 200.51276 121.88976 196.09449 L 121.88976 93.03937 C 121.88976 88.62109 125.471485 85.03937 129.88976 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(126.88976 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.4044197" y="9" textLength="45.222656">Networking</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.3751228" y="20" textLength="51.28125">Management</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 170.07874 L 176.25197 170.07874 C 180.67025 170.07874 184.25197 173.66046 184.25197 178.07874 L 184.25197 190.4252 C 184.25197 194.84347 180.67025 198.4252 176.25197 198.4252 L 135.559054 198.4252 C 131.14078 198.4252 127.559054 194.84347 127.559054 190.4252 L 127.559054 178.07874 C 127.559054 173.66046 131.14078 170.07874 135.559054 170.07874 Z" fill="#eae3cc"/><path d="M 135.559054 170.07874 L 176.25197 170.07874 C 180.67025 170.07874 184.25197 173.66046 184.25197 178.07874 L 184.25197 190.4252 C 184.25197 194.84347 180.67025 198.4252 176.25197 198.4252 L 135.559054 198.4252 C 131.14078 198.4252 127.559054 194.84347 127.559054 190.4252 L 127.559054 178.07874 C 127.559054 173.66046 131.14078 170.07874 135.559054 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 178.75197)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".8835659" y="9" textLength="44.92578">ML2 Plug-in</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 127.559054 L 176.25197 127.559054 C 180.67025 127.559054 184.25197 131.14078 184.25197 135.559054 L 184.25197 147.90551 C 184.25197 152.32379 180.67025 155.90551 176.25197 155.90551 L 135.559054 155.90551 C 131.14078 155.90551 127.559054 152.32379 127.559054 147.90551 L 127.559054 135.559054 C 127.559054 131.14078 131.14078 127.559054 135.559054 127.559054 Z" fill="#eae3cc"/><path d="M 135.559054 127.559054 L 176.25197 127.559054 C 180.67025 127.559054 184.25197 131.14078 184.25197 135.559054 L 184.25197 147.90551 C 184.25197 152.32379 180.67025 155.90551 176.25197 155.90551 L 135.559054 155.90551 C 131.14078 155.90551 127.559054 152.32379 127.559054 147.90551 L 127.559054 135.559054 C 127.559054 131.14078 131.14078 127.559054 135.559054 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 136.23228)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.020285" y="9" textLength="12.652344">API</tspan></text></g><line x1="311.81102" y1="180" x2="269.29134" y2="212.59842" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="311.81102" y1="180" x2="269.29134" y2="170.07874" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><circle cx="42.519685" cy="595.2756" r="8.5039505" fill="#d11b24"/><text transform="translate(56.409448 583.2677)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="102.9834">Management network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.0.0/24</tspan></text><path d="M 113.385826 240.94488 C 113.385826 240.94488 142.210345 233.62646 198.4252 277.79527 C 234.1458 305.86146 270.25246 346.3305 291.8316 372.10777" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="354.3307" y1="269.29134" x2="320.20835" y2="371.6584" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 93.03937 226.77165 L 133.73228 226.77165 C 138.15056 226.77165 141.73228 230.35337 141.73228 234.77165 L 141.73228 247.11811 C 141.73228 251.53639 138.15056 255.11811 133.73228 255.11811 L 93.03937 255.11811 C 88.62109 255.11811 85.03937 251.53639 85.03937 247.11811 L 85.03937 234.77165 C 85.03937 230.35337 88.62109 226.77165 93.03937 226.77165 Z" fill="#d11b24"/><path d="M 93.03937 226.77165 L 133.73228 226.77165 C 138.15056 226.77165 141.73228 230.35337 141.73228 234.77165 L 141.73228 247.11811 C 141.73228 251.53639 138.15056 255.11811 133.73228 255.11811 L 93.03937 255.11811 C 88.62109 255.11811 85.03937 251.53639 85.03937 247.11811 L 85.03937 234.77165 C 85.03937 230.35337 88.62109 226.77165 93.03937 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 235.44488)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.6062222" y="9" textLength="41.480469">Interface 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 413.35433 170.07874 L 465.38582 170.07874 C 469.8041 170.07874 473.38582 173.66046 473.38582 178.07874 L 473.38582 230.11023 C 473.38582 234.52851 469.8041 238.11023 465.38582 238.11023 L 413.35433 238.11023 C 408.93605 238.11023 405.35433 234.52851 405.35433 230.11023 L 405.35433 178.07874 C 405.35433 173.66046 408.93605 170.07874 413.35433 170.07874 Z" fill="#fdf5dd"/><path d="M 413.35433 170.07874 L 465.38582 170.07874 C 469.8041 170.07874 473.38582 173.66046 473.38582 178.07874 L 473.38582 230.11023 C 473.38582 234.52851 469.8041 238.11023 465.38582 238.11023 L 413.35433 238.11023 C 408.93605 238.11023 405.35433 234.52851 405.35433 230.11023 L 405.35433 178.07874 C 405.35433 173.66046 408.93605 170.07874 413.35433 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(410.35433 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.421999" y="9" textLength="39.265625">Metadata </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="17.730593" y="20" textLength="22.570312">Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 413.35433 85.03937 L 465.38583 85.03937 C 469.8041 85.03937 473.38583 88.62109 473.38583 93.03937 L 473.38583 139.40157 C 473.38583 143.81985 469.8041 147.40157 465.38583 147.40157 L 413.35433 147.40157 C 408.93605 147.40157 405.35433 143.81985 405.35433 139.40157 L 405.35433 93.03937 C 405.35433 88.62109 408.93605 85.03937 413.35433 85.03937 Z" fill="#fdf5dd"/><path d="M 413.35433 85.03937 L 465.38583 85.03937 C 469.8041 85.03937 473.38583 88.62109 473.38583 93.03937 L 473.38583 139.40157 C 473.38583 143.81985 469.8041 147.40157 465.38583 147.40157 L 413.35433 147.40157 C 408.93605 147.40157 405.35433 143.81985 405.35433 139.40157 L 405.35433 93.03937 C 405.35433 88.62109 408.93605 85.03937 413.35433 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(410.35433 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="5.7559836" y="9" textLength="46.51953">DHCP Agent</tspan></text></g><line x1="269.29134" y1="269.29134" x2="302.79745" y2="369.80967" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 439.37008 218.26772 C 439.37008 218.26772 479.05512 195.77696 479.05512 170.07874 C 479.05512 144.38052 439.37008 127.559054 439.37008 127.559054" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="269.29134" x2="407.52524" y2="144.88082" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="269.29134" x2="286.83914" y2="232.44094" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="269.29134" x2="405.35433" y2="217.13386" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 243.27559 127.559054 L 380.34645 127.559054 C 384.76473 127.559054 388.34645 131.14078 388.34645 135.559054 L 388.34645 224.44094 C 388.34645 228.85922 384.76473 232.44094 380.34645 232.44094 L 243.27559 232.44094 C 238.85731 232.44094 235.27559 228.85922 235.27559 224.44094 L 235.27559 135.559054 C 235.27559 131.14078 238.85731 127.559054 243.27559 127.559054 Z" fill="#fdf5dd"/><path d="M 243.27559 127.559054 L 380.34645 127.559054 C 384.76473 127.559054 388.34645 131.14078 388.34645 135.559054 L 388.34645 224.44094 C 388.34645 228.85922 384.76473 232.44094 380.34645 232.44094 L 243.27559 232.44094 C 238.85731 232.44094 235.27559 228.85922 235.27559 224.44094 L 235.27559 135.559054 C 235.27559 131.14078 238.85731 127.559054 243.27559 127.559054 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(240.27559 132.559054)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="35.105745" y="9" textLength="72.859375">Linux Bridge Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 255.11811 L 289.6378 255.11811 C 294.05607 255.11811 297.6378 258.69983 297.6378 263.11811 L 297.6378 275.46457 C 297.6378 279.88284 294.05607 283.46457 289.6378 283.46457 L 248.94488 283.46457 C 244.5266 283.46457 240.94488 279.88284 240.94488 275.46457 L 240.94488 263.11811 C 240.94488 258.69983 244.5266 255.11811 248.94488 255.11811 Z" fill="#d11b24"/><path d="M 248.94488 255.11811 L 289.6378 255.11811 C 294.05607 255.11811 297.6378 258.69983 297.6378 263.11811 L 297.6378 275.46457 C 297.6378 279.88284 294.05607 283.46457 289.6378 283.46457 L 248.94488 283.46457 C 244.5266 283.46457 240.94488 279.88284 240.94488 275.46457 L 240.94488 263.11811 C 240.94488 258.69983 244.5266 255.11811 248.94488 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 263.79134)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.6062222" y="9" textLength="41.480469">Interface 1</tspan></text></g><line x1="269.29134" y1="99.2126" x2="269.29134" y2="170.07874" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="170.07874" x2="269.29134" y2="212.59842" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="439.37008" y1="127.559054" x2="354.3307" y2="212.59842" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="354.3307" y1="212.59842" x2="439.37008" y2="269.29134" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="269.29134" y1="212.59842" x2="439.37008" y2="269.29134" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 85.03937 L 289.6378 85.03937 C 294.05607 85.03937 297.6378 88.62109 297.6378 93.03937 L 297.6378 105.385826 C 297.6378 109.804104 294.05607 113.385826 289.6378 113.385826 L 248.94488 113.385826 C 244.5266 113.385826 240.94488 109.804104 240.94488 105.385826 L 240.94488 93.03937 C 240.94488 88.62109 244.5266 85.03937 248.94488 85.03937 Z" fill="#fdf5dd"/><path d="M 248.94488 85.03937 L 289.6378 85.03937 C 294.05607 85.03937 297.6378 88.62109 297.6378 93.03937 L 297.6378 105.385826 C 297.6378 109.804104 294.05607 113.385826 289.6378 113.385826 L 248.94488 113.385826 C 244.5266 113.385826 240.94488 109.804104 240.94488 105.385826 L 240.94488 93.03937 C 240.94488 88.62109 244.5266 85.03937 248.94488 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 93.7126)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 255.11811 L 374.67716 255.11811 C 379.09544 255.11811 382.67716 258.69983 382.67716 263.11811 L 382.67716 275.46457 C 382.67716 279.88284 379.09544 283.46457 374.67716 283.46457 L 333.98425 283.46457 C 329.56597 283.46457 325.98425 279.88284 325.98425 275.46457 L 325.98425 263.11811 C 325.98425 258.69983 329.56597 255.11811 333.98425 255.11811 Z" fill="#bd3612"/><path d="M 333.98425 255.11811 L 374.67716 255.11811 C 379.09544 255.11811 382.67716 258.69983 382.67716 263.11811 L 382.67716 275.46457 C 382.67716 279.88284 379.09544 283.46457 374.67716 283.46457 L 333.98425 283.46457 C 329.56597 283.46457 325.98425 279.88284 325.98425 275.46457 L 325.98425 263.11811 C 325.98425 258.69983 329.56597 255.11811 333.98425 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 419.02362 204.09449 L 459.71653 204.09449 C 464.1348 204.09449 467.71653 207.67621 467.71653 212.09449 L 467.71653 224.44094 C 467.71653 228.85922 464.1348 232.44094 459.71653 232.44094 L 419.02362 232.44094 C 414.60534 232.44094 411.02362 228.85922 411.02362 224.44094 L 411.02362 212.09449 C 411.02362 207.67621 414.60534 204.09449 419.02362 204.09449 Z" fill="#eae3cc"/><path d="M 419.02362 204.09449 L 459.71653 204.09449 C 464.1348 204.09449 467.71653 207.67621 467.71653 212.09449 L 467.71653 224.44094 C 467.71653 228.85922 464.1348 232.44094 459.71653 232.44094 L 419.02362 232.44094 C 414.60534 232.44094 411.02362 228.85922 411.02362 224.44094 L 411.02362 212.09449 C 411.02362 207.67621 414.60534 204.09449 419.02362 204.09449 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(416.02362 207.26772)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="4.7527065" y="9" textLength="37.1875">Metadata</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.617941" y="20" textLength="8.3710938">Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.828878" y="20" textLength="21.246094">ocess</tspan></text></g><g filter="url(#Shadow)"><path d="M 419.02362 113.385826 L 459.71653 113.385826 C 464.1348 113.385826 467.71653 116.96755 467.71653 121.385826 L 467.71653 133.73228 C 467.71653 138.15056 464.1348 141.73228 459.71653 141.73228 L 419.02362 141.73228 C 414.60534 141.73228 411.02362 138.15056 411.02362 133.73228 L 411.02362 121.385826 C 411.02362 116.96755 414.60534 113.385826 419.02362 113.385826 Z" fill="#eae3cc"/><path d="M 419.02362 113.385826 L 459.71653 113.385826 C 464.1348 113.385826 467.71653 116.96755 467.71653 121.385826 L 467.71653 133.73228 C 467.71653 138.15056 464.1348 141.73228 459.71653 141.73228 L 419.02362 141.73228 C 414.60534 141.73228 411.02362 138.15056 411.02362 133.73228 L 411.02362 121.385826 C 411.02362 116.96755 414.60534 113.385826 419.02362 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(416.02362 116.559054)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="12.41091" y="9" textLength="23.949219">DHCP </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 198.4252 L 374.67716 198.4252 C 379.09544 198.4252 382.67716 202.00692 382.67716 206.4252 L 382.67716 218.77165 C 382.67716 223.18993 379.09544 226.77165 374.67716 226.77165 L 333.98425 226.77165 C 329.56597 226.77165 325.98425 223.18993 325.98425 218.77165 L 325.98425 206.4252 C 325.98425 202.00692 329.56597 198.4252 333.98425 198.4252 Z" fill="#eae3cc"/><path d="M 333.98425 198.4252 L 374.67716 198.4252 C 379.09544 198.4252 382.67716 202.00692 382.67716 206.4252 L 382.67716 218.77165 C 382.67716 223.18993 379.09544 226.77165 374.67716 226.77165 L 333.98425 226.77165 C 329.56597 226.77165 325.98425 223.18993 325.98425 218.77165 L 325.98425 206.4252 C 325.98425 202.00692 329.56597 198.4252 333.98425 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 207.09842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="9" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 198.4252 L 289.6378 198.4252 C 294.05607 198.4252 297.6378 202.00692 297.6378 206.4252 L 297.6378 218.77165 C 297.6378 223.18993 294.05607 226.77165 289.6378 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" fill="#eae3cc"/><path d="M 248.94488 198.4252 L 289.6378 198.4252 C 294.05607 198.4252 297.6378 202.00692 297.6378 206.4252 L 297.6378 218.77165 C 297.6378 223.18993 294.05607 226.77165 289.6378 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 207.09842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="9" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 155.90551 L 289.6378 155.90551 C 294.05607 155.90551 297.6378 159.48723 297.6378 163.90551 L 297.6378 176.25197 C 297.6378 180.67025 294.05607 184.25197 289.6378 184.25197 L 248.94488 184.25197 C 244.5266 184.25197 240.94488 180.67025 240.94488 176.25197 L 240.94488 163.90551 C 240.94488 159.48723 244.5266 155.90551 248.94488 155.90551 Z" fill="#eae3cc"/><path d="M 248.94488 155.90551 L 289.6378 155.90551 C 294.05607 155.90551 297.6378 159.48723 297.6378 163.90551 L 297.6378 176.25197 C 297.6378 180.67025 294.05607 184.25197 289.6378 184.25197 L 248.94488 184.25197 C 244.5266 184.25197 240.94488 180.67025 240.94488 176.25197 L 240.94488 163.90551 C 240.94488 159.48723 244.5266 155.90551 248.94488 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 164.57874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.4148173" y="9" textLength="9.9375">Fir</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="18.192161" y="9" textLength="4.609375">e</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="22.64138" y="9" textLength="15.636719">wall</tspan></text></g><path d="M 36.346457 283.46457 L 190.4252 283.46457 C 194.84347 283.46457 198.4252 287.04629 198.4252 291.46457 L 198.4252 550.4252 C 198.4252 554.84347 194.84347 558.4252 190.4252 558.4252 L 36.346457 558.4252 C 31.928179 558.4252 28.346457 554.84347 28.346457 550.4252 L 28.346457 291.46457 C 28.346457 287.04629 31.928179 283.46457 36.346457 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 288.46457)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="34.971987" y="13" textLength="90.134766"> Network Nodes</tspan></text><g filter="url(#Shadow)"><path d="M 44.850393 382.67716 L 181.92126 382.67716 C 186.33954 382.67716 189.92126 386.25889 189.92126 390.67716 L 189.92126 442.70866 C 189.92126 447.12694 186.33954 450.70866 181.92126 450.70866 L 44.850393 450.70866 C 40.432115 450.70866 36.850393 447.12694 36.850393 442.70866 L 36.850393 390.67716 C 36.850393 386.25889 40.432115 382.67716 44.850393 382.67716 Z" fill="#fdf5dd"/><path d="M 44.850393 382.67716 L 181.92126 382.67716 C 186.33954 382.67716 189.92126 386.25889 189.92126 390.67716 L 189.92126 442.70866 C 189.92126 447.12694 186.33954 450.70866 181.92126 450.70866 L 44.850393 450.70866 C 40.432115 450.70866 36.850393 447.12694 36.850393 442.70866 L 36.850393 390.67716 C 36.850393 386.25889 40.432115 382.67716 44.850393 382.67716 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(41.850393 387.67716)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="35.105745" y="9" textLength="72.859375">Linux Bridge Agent</tspan></text></g><g filter="url(#Shadow)"><path d="M 87.370076 308.97638 L 139.40157 308.97638 C 143.81985 308.97638 147.40157 312.5581 147.40157 316.97638 L 147.40157 363.33858 C 147.40157 367.75686 143.81985 371.33858 139.40157 371.33858 L 87.370076 371.33858 C 82.9518 371.33858 79.370076 367.75686 79.370076 363.33858 L 79.370076 316.97638 C 79.370076 312.5581 82.9518 308.97638 87.370076 308.97638 Z" fill="#fdf5dd"/><path d="M 87.370076 308.97638 L 139.40157 308.97638 C 143.81985 308.97638 147.40157 312.5581 147.40157 316.97638 L 147.40157 363.33858 C 147.40157 367.75686 143.81985 371.33858 139.40157 371.33858 L 87.370076 371.33858 C 82.9518 371.33858 79.370076 367.75686 79.370076 363.33858 L 79.370076 316.97638 C 79.370076 312.5581 82.9518 308.97638 87.370076 308.97638 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(84.370076 313.97638)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.4591086" y="9" textLength="53.11328">Layer-3 Agent</tspan></text></g><path d="M 70.86614 430.86614 C 70.86614 430.86614 67.324037 405.1631 79.370076 382.67716 C 91.416114 360.19122 113.385823 351.49606 113.385823 351.49606" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 113.385823 351.49606 C 113.385823 351.49606 135.35553 360.19122 147.40157 382.67716 C 159.44761 405.1631 155.90551 430.86614 155.90551 430.86614" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="430.86614" x2="155.90551" y2="496.063" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="430.86614" x2="70.86614" y2="496.063" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="439.37008" y1="269.29134" x2="335.52856" y2="373.13285" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 419.02362 255.11811 L 459.71653 255.11811 C 464.1348 255.11811 467.71653 258.69983 467.71653 263.11811 L 467.71653 275.46457 C 467.71653 279.88284 464.1348 283.46457 459.71653 283.46457 L 419.02362 283.46457 C 414.60534 283.46457 411.02362 279.88284 411.02362 275.46457 L 411.02362 263.11811 C 411.02362 258.69983 414.60534 255.11811 419.02362 255.11811 Z" fill="#a57706"/><path d="M 419.02362 255.11811 L 459.71653 255.11811 C 464.1348 255.11811 467.71653 258.69983 467.71653 263.11811 L 467.71653 275.46457 C 467.71653 279.88284 464.1348 283.46457 459.71653 283.46457 L 419.02362 283.46457 C 414.60534 283.46457 411.02362 279.88284 411.02362 275.46457 L 411.02362 263.11811 C 411.02362 258.69983 414.60534 255.11811 419.02362 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(416.02362 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 3</tspan></text></g><line x1="275.52234" y1="419.9432" x2="155.90551" y2="496.063" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 291.99074 420.71518 C 270.43028 445.65896 234.23376 484.65867 198.4252 510.23622 C 142.210345 550.38968 113.385826 538.58267 113.385826 538.58267" stroke="#d11b24" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 270.42506 418.3403 C 251.56496 427.56886 227.02587 438.9319 198.4252 450.70866 C 130.164306 478.81608 70.86614 496.063 70.86614 496.063" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 50.519685 416.6929 L 91.2126 416.6929 C 95.630876 416.6929 99.2126 420.27463 99.2126 424.6929 L 99.2126 437.03937 C 99.2126 441.45765 95.630876 445.03937 91.2126 445.03937 L 50.519685 445.03937 C 46.101407 445.03937 42.519685 441.45765 42.519685 437.03937 L 42.519685 424.6929 C 42.519685 420.27463 46.101407 416.6929 50.519685 416.6929 Z" fill="#eae3cc"/><path d="M 50.519685 416.6929 L 91.2126 416.6929 C 95.630876 416.6929 99.2126 420.27463 99.2126 424.6929 L 99.2126 437.03937 C 99.2126 441.45765 95.630876 445.03937 91.2126 445.03937 L 50.519685 445.03937 C 46.101407 445.03937 42.519685 441.45765 42.519685 437.03937 L 42.519685 424.6929 C 42.519685 420.27463 46.101407 416.6929 50.519685 416.6929 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 425.36614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="9" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 416.6929 L 176.25197 416.6929 C 180.67025 416.6929 184.25197 420.27463 184.25197 424.6929 L 184.25197 437.03937 C 184.25197 441.45765 180.67025 445.03937 176.25197 445.03937 L 135.559054 445.03937 C 131.14078 445.03937 127.559054 441.45765 127.559054 437.03937 L 127.559054 424.6929 C 127.559054 420.27463 131.14078 416.6929 135.559054 416.6929 Z" fill="#eae3cc"/><path d="M 135.559054 416.6929 L 176.25197 416.6929 C 180.67025 416.6929 184.25197 420.27463 184.25197 424.6929 L 184.25197 437.03937 C 184.25197 441.45765 180.67025 445.03937 176.25197 445.03937 L 135.559054 445.03937 C 131.14078 445.03937 127.559054 441.45765 127.559054 437.03937 L 127.559054 424.6929 C 127.559054 420.27463 131.14078 416.6929 135.559054 416.6929 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 425.36614)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.8445034" y="9" textLength="25.003906">Bridge</tspan></text></g><g filter="url(#Shadow)"><path d="M 93.039367 337.32283 L 133.73228 337.32283 C 138.15056 337.32283 141.73228 340.90455 141.73228 345.32283 L 141.73228 357.66929 C 141.73228 362.08757 138.15056 365.6693 133.73228 365.6693 L 93.039367 365.6693 C 88.62109 365.6693 85.039367 362.08757 85.039367 357.66929 L 85.039367 345.32283 C 85.039367 340.90455 88.62109 337.32283 93.039367 337.32283 Z" fill="#eae3cc"/><path d="M 93.039367 337.32283 L 133.73228 337.32283 C 138.15056 337.32283 141.73228 340.90455 141.73228 345.32283 L 141.73228 357.66929 C 141.73228 362.08757 138.15056 365.6693 133.73228 365.6693 L 93.039367 365.6693 C 88.62109 365.6693 85.039367 362.08757 85.039367 357.66929 L 85.039367 345.32283 C 85.039367 340.90455 88.62109 337.32283 93.039367 337.32283 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.039367 340.49606)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.2058315" y="9" textLength="26.28125">Router</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".7195034" y="20" textLength="45.253906">Namespace</tspan></text></g><g filter="url(#Shadow)"><path d="M 93.03937 524.40945 L 133.73228 524.40945 C 138.15056 524.40945 141.73228 527.99117 141.73228 532.40945 L 141.73228 544.7559 C 141.73228 549.17418 138.15056 552.7559 133.73228 552.7559 L 93.03937 552.7559 C 88.62109 552.7559 85.03937 549.17418 85.03937 544.7559 L 85.03937 532.40945 C 85.03937 527.99117 88.62109 524.40945 93.03937 524.40945 Z" fill="#d11b24"/><path d="M 93.03937 524.40945 L 133.73228 524.40945 C 138.15056 524.40945 141.73228 527.99117 141.73228 532.40945 L 141.73228 544.7559 C 141.73228 549.17418 138.15056 552.7559 133.73228 552.7559 L 93.03937 552.7559 C 88.62109 552.7559 85.03937 549.17418 85.03937 544.7559 L 85.03937 532.40945 C 85.03937 527.99117 88.62109 524.40945 93.03937 524.40945 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 533.08267)" fill="#eae3cb"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#eae3cb" x="2.6062222" y="9" textLength="41.480469">Interface 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 481.88976 L 91.2126 481.88976 C 95.630876 481.88976 99.2126 485.47148 99.2126 489.88976 L 99.2126 502.23622 C 99.2126 506.6545 95.630876 510.23622 91.2126 510.23622 L 50.519685 510.23622 C 46.101407 510.23622 42.519685 506.6545 42.519685 502.23622 L 42.519685 489.88976 C 42.519685 485.47148 46.101407 481.88976 50.519685 481.88976 Z" fill="#bd3612"/><path d="M 50.519685 481.88976 L 91.2126 481.88976 C 95.630876 481.88976 99.2126 485.47148 99.2126 489.88976 L 99.2126 502.23622 C 99.2126 506.6545 95.630876 510.23622 91.2126 510.23622 L 50.519685 510.23622 C 46.101407 510.23622 42.519685 506.6545 42.519685 502.23622 L 42.519685 489.88976 C 42.519685 485.47148 46.101407 481.88976 50.519685 481.88976 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 490.563)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 481.88976 L 176.25197 481.88976 C 180.67025 481.88976 184.25197 485.47148 184.25197 489.88976 L 184.25197 502.23622 C 184.25197 506.6545 180.67025 510.23622 176.25197 510.23622 L 135.559054 510.23622 C 131.14078 510.23622 127.559054 506.6545 127.559054 502.23622 L 127.559054 489.88976 C 127.559054 485.47148 131.14078 481.88976 135.559054 481.88976 Z" fill="#a57706"/><path d="M 135.559054 481.88976 L 176.25197 481.88976 C 180.67025 481.88976 184.25197 485.47148 184.25197 489.88976 L 184.25197 502.23622 C 184.25197 506.6545 180.67025 510.23622 176.25197 510.23622 L 135.559054 510.23622 C 131.14078 510.23622 127.559054 506.6545 127.559054 502.23622 L 127.559054 489.88976 C 127.559054 485.47148 131.14078 481.88976 135.559054 481.88976 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 490.563)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.6062222" y="9" textLength="41.480469">Interface 3</tspan></text></g><path d="M 269.90022 399.89537 C 251.5748 396.8504 258.88252 371.2167 288.11565 375.59055 C 290.82784 367.0645 324.82205 368.44838 324.5998 375.59055 C 345.91521 366.45562 373.15502 384.67049 354.88403 393.80542 C 376.80831 398.23427 354.60737 422.0963 336.61417 418.11023 C 335.17417 424.75408 303.00775 427.07905 300.18444 418.11023 C 281.97014 427.6885 243.99042 412.96138 269.90022 399.89537 Z" fill="#fdf5dd"/><path d="M 269.90022 399.89537 C 251.5748 396.8504 258.88252 371.2167 288.11565 375.59055 C 290.82784 367.0645 324.82205 368.44838 324.5998 375.59055 C 345.91521 366.45562 373.15502 384.67049 354.88403 393.80542 C 376.80831 398.23427 354.60737 422.0963 336.61417 418.11023 C 335.17417 424.75408 303.00775 427.07905 300.18444 418.11023 C 281.97014 427.6885 243.99042 412.96138 269.90022 399.89537 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(277.12598 385.8504)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".25144539" y="9" textLength="70.945312"> Physical Network </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.685039" y="20" textLength="13.871094">Infr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.395977" y="20" textLength="35.839844">astructur</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="57.075664" y="20" textLength="4.609375">e</tspan></text><circle cx="42.519685" cy="623.62204" r="8.5039505" fill="#a57706"/><text transform="translate(56.897637 611.61417)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="184.25197" cy="595.2756" r="8.5039505" fill="#bd3612"/><text transform="translate(197.7559 582.28346)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text></g></g></svg>
