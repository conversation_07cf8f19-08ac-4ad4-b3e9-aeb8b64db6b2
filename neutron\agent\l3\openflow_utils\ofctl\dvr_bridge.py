#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import ipaddress

import netaddr
from oslo_log import log as logging

from neutron.agent.common import utils as agent_comm
from neutron.common import constants as l3_constants
from neutron.common import utils as common_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.ovs_ofctl \
    import ovs_bridge

ICMP6_SNMA = l3_constants.DVR_ICMP6_SNMA

LOG = logging.getLogger(__name__)


class L3AgentBridge(ovs_bridge.OVSAgentBridge):

    def __init__(self, *args, **kwargs):
        super(L3AgentBridge, self).__init__(*args, **kwargs)
        self.df_br = None

    def enable_defer(self):
        self.df_br = self.deferred(full_ordered=True)

    def disable_defer(self):
        self.df_br = None

    def defer_apply_flows(self):
        self.df_br.apply_flows()

    def init_bridge_flows(self):
        self.delete_flows(cookie="0/0")
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE,
            priority=1, actions="NORMAL")
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE,
            priority=1, actions="DROP")
        self.delete_flows(
            table=l3_constants.DVR_BRIDGE_INPUT_TABLE,
            cookie=0)

        self.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_AD_PORTS)
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
            priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_QOS)
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_QOS,
            priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_NAT)

        # Revisit if this is needed.
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_NAT,
            priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_ACL)
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_ACL,
            priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE)
        self.add_flow(
            table=l3_constants.DVR_BRIDGE_PRE_SNAT_ACL,
            priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE)

    def install_goto_snat_ip4(self, mac_address, router_cidr):
        br = self.df_br if self.df_br is not None else self
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        br.add_flow(priority=100, proto='ip',
                    dl_dst=mac_address,
                    table=ext_output_table,
                    nw_src=router_cidr, actions="resubmit(,%s)" %
                    l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE)

    def install_goto_snat_ip6(self, mac_address, router_cidr):
        br = self.df_br if self.df_br is not None else self
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        br.add_flow(priority=100, proto='ipv6',
                    dl_dst=mac_address,
                    table=ext_output_table,
                    ipv6_src=router_cidr,
                    actions="resubmit(,%s)" %
                    l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE)

    def install_arp_to_in_port(self):
        br = self.df_br if self.df_br is not None else self
        br.add_flow(priority=50, proto='arp',
                    table=l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE,
                    actions="IN_PORT")

    def install_snat_route(self, mac_src, mac_dst):
        br = self.df_br if self.df_br is not None else self
        br.add_flow(priority=100, dl_dst=mac_src,
                    table=l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE,
                    actions="mod_dl_src=%s,mod_dl_dst=%s,"
                            "IN_PORT" % (mac_src,
                                         mac_dst))

    def install_route_goto(self, input_table, output_table,
                           priority, mac_src, mac_dst, route_ip,
                           ip_version):
        br = self.df_br if self.df_br is not None else self
        ma_values = {'proto': 'ip' if ip_version == 4 else 'ipv6'}
        if ip_version == 4:
            ma_values['nw_dst'] = route_ip
        else:
            ma_values['ipv6_dst'] = route_ip
        ma_values['actions'] = (
            "mod_dl_src=%s,mod_dl_dst=%s,dec_ttl,resubmit(,%s)") % (
                mac_src, mac_dst, output_table)
        br.add_flow(table=input_table,
                    priority=priority,
                    **ma_values)

    def remove_route_goto(self, input_table, router_ip, ip_version):
        br = self.df_br if self.df_br is not None else self
        ma_values = {'proto': 'ip' if ip_version == 4 else 'ipv6'}
        if ip_version == 4:
            ma_values['nw_dst'] = router_ip
        else:
            ma_values['ipv6_dst'] = router_ip
        br.delete_flows(table=input_table,
                        **ma_values)

    def remove_router_interface_ip_flows(self, ip, ip_cidr, mac):
        br = self.df_br if self.df_br is not None else self
        if common_utils.get_ip_version(ip) == 6:
            br.delete_flows(proto='ipv6', ipv6_dst=ip)
            br.delete_flows(proto='icmp6', nd_target=ip)
            br.delete_flows(proto='icmp6', ipv6_src=ip)
            br.delete_flows(proto='icmp6', nd_target=ip_cidr)
            br.delete_flows(proto='ipv6', ipv6_dst=ip_cidr)
            br.delete_flows(proto='ipv6', ipv6_src=ip_cidr)

        elif common_utils.get_ip_version(ip) == 4:
            br.delete_flows(proto='ip', nw_dst=ip)
            br.delete_flows(proto='icmp', nw_dst=ip)
            br.delete_flows(proto='icmp', nw_src=ip)
            br.delete_flows(proto='arp', nw_dst=ip)
            br.delete_flows(proto='ip', nw_dst=ip_cidr)
            br.delete_flows(proto='ip', nw_src=ip_cidr)

        br.delete_flows(table=l3_constants.DVR_BRIDGE_SNAT_OUTPUT_TABLE,
                        dl_dst=mac)

    def _generate_external_gateway_ping_actions(self, ex_gw_port, gateway_mac,
                                                port):
        ipv4_actions = ""
        ipv6_actions_rsp = ""
        ipv6_actions_nsp = ""

        # Note(davidsha)
        #    Creates flows that convert incoming arp/icmpv6 packets
        #    into arp/imcpv6 requests to the external gateways to learn their
        #    mac addresses.
        #    For each traffic type a packet is sent to the gateways with the
        #    same protocol.
        for subnet in ex_gw_port['subnets']:
            for fixed_ip in ex_gw_port['fixed_ips']:
                ip = fixed_ip['ip_address']
                ip_cidr = ip + "/%s" % fixed_ip['prefixlen']
                if (common_utils.get_ip_version(ip) == 4 and
                        agent_comm.check_ip_in_subnet(ip_cidr,
                                                      subnet['cidr'])):
                    hex_str = '{:02X}{:02X}{:02X}{:02X}'
                    ip_hex = hex_str.format(*map(int, ip.split('.')))
                    gate_ip = subnet['gateway_ip']
                    gateway_ip_hex = hex_str.format(*map(int,
                                                         gate_ip.split('.')))
                    ipv4_actions += ("load:0x%s->NXM_NX_ARP_SHA[],"
                                     "mod_dl_src:%s,"
                                     "mod_dl_dst:FF:FF:FF:FF:FF:FF,"
                                     "load:0x%s->NXM_OF_ARP_SPA[],"
                                     "load:0x%s->NXM_OF_ARP_TPA[],"
                                     "load:0x0->NXM_NX_ARP_THA[],"
                                     "load:0x01->NXM_OF_ARP_OP[],"
                                     "load:0x0->NXM_OF_IN_PORT[],"
                                     "output:%s," %
                                    (gateway_mac.replace(':', ""),
                                     gateway_mac, ip_hex, gateway_ip_hex,
                                     port))
                elif (common_utils.get_ip_version(ip) == 6 and
                        agent_comm.check_ip_in_subnet(ip_cidr,
                                                      subnet['cidr'])):
                    gate_ipv6 = ipaddress.ip_address(subnet['gateway_ip'])
                    ipv6 = ipaddress.ip_address(ip)
                    ip_hex = ipv6.exploded.replace(':', "")
                    gateway_ip_hex = gate_ipv6.exploded.replace(':', "")
                    ipv6_actions_rsp += ("load:0x%s->NXM_OF_ETH_SRC[],"
                                         "load:0x%s->NXM_NX_IPV6_SRC[],"
                                         "load:0x%s->NXM_NX_IPV6_DST[],"
                                         "load:0x0->NXM_OF_ETH_DST[],"
                                         "load:0x85->NXM_NX_ICMPV6_TYPE[],"
                                         "load:0x0->NXM_OF_IN_PORT[],"
                                         "output:%s," %
                                         (gateway_mac.replace(':', ""),
                                          ip_hex, gateway_ip_hex, port))
                    ipv6_actions_nsp += ("load:0x%s->NXM_OF_ETH_SRC[],"
                                         "load:0x%s->NXM_NX_IPV6_SRC[],"
                                         "load:0x%s->NXM_NX_IPV6_DST[],"
                                         "load:0x0->NXM_OF_ETH_DST[],"
                                         "load:0x87->NXM_NX_ICMPV6_TYPE[],"
                                         "load:0x0->NXM_OF_IN_PORT[],"
                                         "output:%s," %
                                         (gateway_mac.replace(':', ""),
                                          ip_hex, gateway_ip_hex, port))
        return [ipv4_actions, ipv6_actions_rsp, ipv6_actions_nsp]

    def install_dvr_snat(self, ex_gw_port, gateway_mac, of_port):
        br = self.df_br if self.df_br is not None else self
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        acts = self._generate_external_gateway_ping_actions(
            ex_gw_port, gateway_mac, of_port)
        ipv4_actions = acts[0]
        ipv6_actions_rsp = acts[1]
        ipv6_actions_nsp = acts[2]

        if ipv4_actions:
            br.add_flow(table=mac_learning_table, priority=150,
                        proto='arp', arp_op=2,
                        actions=ipv4_actions[:-1])
        if ipv6_actions_rsp:
            br.add_flow(table=mac_learning_table, priority=150,
                        proto='icmp6', icmpv6_type=133,
                        actions=ipv6_actions_rsp[:-1])
            br.add_flow(table=mac_learning_table, priority=150,
                        proto='icmp6', icmpv6_type=135,
                        actions=ipv6_actions_nsp[:-1])

    def install_router_interface_flows(self, ip, ip_cidr, port, mac):
        br = self.df_br if self.df_br is not None else self
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        admin_state_table = l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE
        output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE

        if (common_utils.get_ip_version(ip) == 4 and
                common_utils.get_ip_version(ip_cidr) == 4):
            br.add_flow(priority=100, proto='ip', table=input_table,
                        nw_dst=ip_cidr, actions="resubmit(,%s)" %
                        admin_state_table)
            br.add_flow(priority=50, proto='arp', table=input_table,
                        nw_dst=ip, actions="resubmit(,%s)" %
                        (admin_state_table))
            br.add_flow(priority=300, proto='arp', arp_op=1,
                        nw_dst=ip, in_port=port, table=next_hop_table,
                        actions="move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                                "mod_dl_src:%s,load:0x2->NXM_OF_ARP_OP[],"
                                "move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],"
                                "move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],"
                                "set_field:%s->arp_spa,"
                                "set_field:%s->arp_sha,"
                                "resubmit(,%s),resubmit(,%s)" %
                                (mac, ip, mac, output_table,
                                 mac_learning_table))
            br.add_flow(priority=300, proto='icmp', icmp_type=8,
                        nw_dst=ip, table=next_hop_table,
                        actions="move:NXM_OF_IP_SRC[]->NXM_OF_IP_DST[],"
                                "mod_nw_src:%s,"
                                "load:0x00->NXM_OF_ICMP_TYPE[],"
                                "move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                                "mod_dl_src:%s,"
                                "resubmit(,%s)" %
                                (ip, mac, output_table))
            br.add_flow(priority=150, table=output_table, proto='icmp',
                        eth_src=mac, nw_src=ip,
                        actions='IN_PORT')
        elif (common_utils.get_ip_version(ip) == 6 and
                common_utils.get_ip_version(ip_cidr) == 6):
            ipv6_addr = ipaddress.ip_address(ip)
            br.add_flow(priority=150, proto='icmp6', table=input_table,
                        icmpv6_type=136, nd_target=ip_cidr,
                        actions="resubmit(,%s)" % (admin_state_table))
            br.add_flow(priority=150, proto='icmp6', table=input_table,
                        icmpv6_type=135, nd_target=ip_cidr,
                        actions="resubmit(,%s)" % (admin_state_table))
            br.add_flow(priority=100, proto='ipv6', table=input_table,
                        ipv6_dst=ip_cidr, actions="resubmit(,%s)" %
                        admin_state_table)
            br.add_flow(priority=300, proto='icmp6', icmpv6_type=135,
                        nd_target=ip, in_port=port,
                        table=next_hop_table,
                        actions="move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                                "mod_dl_src:%s,"
                                "move:NXM_NX_IPV6_SRC[]->NXM_NX_IPV6_DST[],"
                                "load:0x%s->NXM_NX_IPV6_DST[0..63],"
                                "load:0x%s->NXM_NX_IPV6_DST[64..103],"
                                "load:0x%s->NXM_NX_IPV6_SRC[],"
                                "load:0x88->NXM_NX_ICMPV6_TYPE[],"
                                "resubmit(,%s),resubmit(,%s)" %
                                (mac, ICMP6_SNMA.replace(":", "")[0:16],
                                 ICMP6_SNMA.replace(":", "")[16:26],
                                 ipv6_addr.exploded.replace(":", ""),
                                 output_table, mac_learning_table))
            br.add_flow(priority=300, proto='icmp6', icmpv6_type=128,
                        ipv6_dst=ip, table=next_hop_table,
                        actions="move:NXM_NX_IPV6_SRC[]->NXM_NX_IPV6_DST[],"
                                "load:0x%s->NXM_NX_IPV6_SRC[],"
                                "load:0x81->NXM_NX_ICMPV6_TYPE[],"
                                "move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                                "mod_dl_src:%s,"
                                "resubmit(,%s)" %
                                (ipv6_addr.exploded.replace(":", ""),
                                 mac, output_table))
            br.add_flow(priority=150, table=output_table, proto='icmp6',
                        eth_src=mac, icmpv6_type=136,
                        ipv6_src=ipv6_addr.exploded,
                        actions='load:0x%s->NXM_NX_ND_TLL[],IN_PORT' %
                        mac.replace(":", ""))
            br.add_flow(priority=150, table=output_table, proto='icmp6',
                        eth_src=mac, icmpv6_type=129,
                        ipv6_src=ipv6_addr.exploded,
                        actions='IN_PORT')

    def install_drop_fip_admin_state(self, fip_address):
        br = self.df_br if self.df_br is not None else self
        br.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            proto='ip',
            nw_dst=fip_address,
            priority=50,
            actions='drop')
        br.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            proto='ip',
            nw_src=fip_address,
            priority=50,
            actions='drop')

    def remove_drop_fip_admin_state(self, fip_address):
        br = self.df_br if self.df_br is not None else self
        br.delete_flows(
            table=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            proto='ip',
            nw_dst=fip_address)
        br.delete_flows(
            table=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
            proto='ip',
            nw_src=fip_address)

    def install_drop_fip_ad_ports(self, fip_address, port):
        br = self.df_br if self.df_br is not None else self
        br.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_AD_PORTS, proto='tcp',
            nw_dst=fip_address,
            tcp_dst=port,
            priority=50, actions='drop')
        br.add_flow(
            table=l3_constants.DVR_BRIDGE_FIP_AD_PORTS, proto='udp',
            nw_dst=fip_address,
            udp_dst=port,
            priority=50, actions='drop')

    def remove_drop_fip_ad_ports(self, fip_address):
        br = self.df_br if self.df_br is not None else self
        br.delete_flows(
            table=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
            nw_dst=fip_address)

    def install_fip_flows(self, float_ip, fixed_ip, gate_ip,
                          fg_mac, fixed_port_mac,
                          gateway_ip_ofport,
                          fip_subnet_cidr,
                          internal_cidr=None):
        br = self.df_br if self.df_br is not None else self
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        fip_nat_table = l3_constants.DVR_BRIDGE_FIP_NAT
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        acl_table = l3_constants.DVR_BRIDGE_ACL
        admin_state_table = l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE
        output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        mac_learn_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        int_out_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        snat_table = l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE
        pre_snat_acl_table = l3_constants.DVR_BRIDGE_PRE_SNAT_ACL

        fip = netaddr.IPAddress(float_ip)
        gwip = netaddr.IPAddress(gate_ip)

        act_out_ip = ("load:0x%s->NXM_NX_REG3[],"
                      "load:%#x->NXM_NX_REG4[],"
                      "learn(priority=100, table=%s,"
                      "idle_timeout=%s,"
                      "eth_type=0x0800,"
                      "NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],"
                      "NXM_OF_IP_SRC[]=NXM_NX_REG4[],"  # <- fixed_ip
                      "load:%#x->NXM_OF_IP_SRC[],"
                      "load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                      "load:%#x->NXM_OF_ETH_SRC[],"
                      "output:NXM_NX_REG3[])" %
                      (gateway_ip_ofport, fip,
                       output_table, l3_constants.DVR_LEARN_IDLE_TIMEOUT,
                       fip, netaddr.EUI(fg_mac, dialect=netaddr.mac_unix)))
        gate_arp_action = ("load:%#x->NXM_NX_ARP_SHA[],"
                           "mod_dl_src:%s,"
                           "mod_dl_dst:FF:FF:FF:FF:FF:FF,"
                           "load:%#x->NXM_OF_ARP_SPA[],"
                           "load:%#x->NXM_OF_ARP_TPA[],"
                           "load:0x0->NXM_NX_ARP_THA[],"
                           "load:0x01->NXM_OF_ARP_OP[],"
                           "load:0x0->NXM_OF_IN_PORT[],"
                           "output:%s," %
                           (netaddr.EUI(fg_mac, dialect=netaddr.mac_unix),
                            fg_mac,
                            fip, gwip, gateway_ip_ofport))

        act_rewrite_ip = ("resubmit(,%s)" % snat_table)

        br.add_flow(table=fip_nat_table, proto='ip',
                    nw_dst=float_ip, priority=50,
                    actions="mod_nw_dst:%s,"
                            "resubmit(,%s)" % (fixed_ip,
                                               acl_table))

        action_ip = ','.join([act_out_ip, act_rewrite_ip])

        br.add_flow(table=input_table, proto='ip',
                    nw_src=fip_subnet_cidr, nw_dst=float_ip,
                    priority=51, actions=action_ip)

        br.add_flow(table=input_table, proto='ip',
                    nw_dst=float_ip,
                    priority=50, actions=act_rewrite_ip)

        br.add_flow(priority=50, proto='arp',
                    table=input_table, nw_dst=float_ip,
                    actions="resubmit(,%s)" % admin_state_table)

        br.add_flow(priority=30, proto='ip',
                    nw_src=fixed_ip, table=input_table,
                    actions="resubmit(,%s)" % pre_snat_acl_table)
        br.add_flow(priority=60010, proto='ip',
                    nw_src=float_ip, table=acl_table,
                    actions="resubmit(,%s)" % next_hop_table)
        br.add_flow(priority=80, proto='ip',
                    nw_src=float_ip, table=next_hop_table,
                    actions="resubmit(,%s)" % output_table)
        br.add_flow(priority=30, proto='ip',
                    nw_src=fixed_ip, table=snat_table,
                    actions="mod_nw_src:%s, mod_dl_src:%s, "
                            "dec_ttl, resubmit(,%s)" %
                            (float_ip, fg_mac, admin_state_table))

        br.add_flow(priority=300, proto='arp',
                    arp_op=1, nw_dst=float_ip, table=next_hop_table,
                    actions="move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                            "mod_dl_src:%s,load:0x2->NXM_OF_ARP_OP[],"
                            "move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],"
                            "move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],"
                            "set_field:%s->arp_spa,"
                            "set_field:%s->arp_sha,"
                            "resubmit(,%s), resubmit(,%s)" %
                            (fg_mac, float_ip, fg_mac,
                             int_out_table, mac_learn_table))
        br.add_flow(table=mac_learn_table,
                    priority=150, proto='arp', arp_op=2,
                    dl_dst=fixed_port_mac, arp_tpa=fixed_ip,
                    actions=gate_arp_action)
        br.add_flow(table=mac_learn_table,
                    priority=150, proto='arp', arp_op=2, dl_src=fg_mac,
                    arp_spa=float_ip,
                    actions=gate_arp_action)
        if internal_cidr:
            br.add_flow(priority=101,
                        nw_dst=internal_cidr, nw_src=float_ip,
                        eth_type="0x0800", table=int_out_table,
                        actions="IN_PORT")

    def remove_fip_flows(self, float_ip, fixed_ip):
        br = self.df_br if self.df_br is not None else self
        br.delete_flows(proto='ip',
                        nw_src=fixed_ip)
        br.delete_flows(proto='ip',
                        nw_dst=float_ip)
        br.delete_flows(proto='ip',
                        nw_src=float_ip)
        br.delete_flows(proto='arp',
                        arp_tpa=float_ip)
        br.delete_flows(proto='arp',
                        arp_tpa=fixed_ip,
                        table=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE)
        br.delete_flows(proto='arp',
                        arp_spa=float_ip,
                        table=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE)
        br.delete_flows(proto='ip',
                        nw_dst=fixed_ip,
                        table=l3_constants.DVR_BRIDGE_ACL)

    def install_learn_action_flows(self, ofport):
        br = self.df_br if self.df_br is not None else self
        if not ofport:
            LOG.error("Invalid ofport for learn actions.")
            return
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        snat_table = l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE

        # Note(davidsha)
        #    These learn action flows are for receiving external
        #    traffic and generating next hop flows like on table 2
        #    to send traffic destined for the external gateway to the
        #    correct mac.
        ex_port = "0x%x" % ofport
        br.add_flow(table=input_table, priority=60, proto='arp',
                    in_port=ofport, reg4='0x0', arp_op=2,
                    actions="load:%s->NXM_NX_REG4[],"
                            "learn(priority=100,table=%s,"
                            "eth_type=0x0806,"
                            "NXM_OF_ARP_TPA[]=NXM_OF_ARP_SPA[],"
                            "output:NXM_NX_REG4[]),"
                            "learn(priority=99,table=%s,"
                            "eth_type=0x0800,"
                            "load:NXM_NX_ARP_SHA[]->"
                            "NXM_OF_ETH_DST[],"
                            "output:NXM_NX_REG4[])" % (
                                ex_port,
                                ext_output_table,
                                ext_output_table))

        br.add_flow(table=input_table, priority=25,
                    proto='ip',
                    actions="dec_ttl,resubmit(,%s)" % ext_output_table)
        br.add_flow(table=input_table, priority=25,
                    proto='ipv6',
                    actions="dec_ttl,resubmit(,%s)" % ext_output_table)
        br.add_flow(table=ext_output_table, priority=25,
                    actions="DROP")
        br.add_flow(
            table=snat_table, priority=1,
            actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE)

    def install_ip_forwarder_flows(self, ip, src_mac, dst_mac, port):
        br = self.df_br if self.df_br is not None else self
        LOG.debug("dvr adding ip: %s", ip)

        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE

        if common_utils.get_ip_version(ip) == 6:
            LOG.debug("Adding IPv6 flow for %s", ip)
            br.add_flow(priority=200, proto='ipv6',
                        table=next_hop_table, ipv6_dst=ip,
                        actions="mod_dl_src:%s,mod_dl_dst:%s,"
                                "dec_ttl,resubmit(,%s)"
                                % (src_mac, dst_mac, output_table))
            br.add_flow(priority=200, proto='icmp6',
                        table=next_hop_table,
                        icmpv6_type=136, nd_target=ip,
                        actions="mod_dl_src:%s,mod_dl_dst:%s,"
                                "dec_ttl,resubmit(,%s)"
                                % (src_mac, dst_mac, output_table))
            br.add_flow(priority=200, proto='icmp6',
                        table=next_hop_table,
                        icmpv6_type=135, nd_target=ip,
                        actions="mod_dl_src:%s,mod_dl_dst:%s,"
                                "dec_ttl,resubmit(,%s)"
                                % (src_mac, dst_mac, output_table))
            br.add_flow(priority=101, eth_dst=dst_mac,
                        vlan_tci='0x1000/0x1000',
                        eth_type="0x86DD",
                        table=output_table,
                        actions="strip_vlan,output:%s" % port)
            br.add_flow(priority=100, eth_dst=dst_mac,
                        eth_type="0x86DD",
                        table=output_table, actions="output:%s" % port)
        else:
            LOG.debug("Adding IPv4 flow for %s", ip)
            br.add_flow(priority=200, proto='ip', table=next_hop_table,
                        nw_dst=ip,
                        actions="mod_dl_src:%s,mod_dl_dst:%s,"
                                "dec_ttl,resubmit(,%s)"
                                % (src_mac, dst_mac, output_table))
            br.add_flow(priority=101, eth_dst=dst_mac,
                        vlan_tci='0x1000/0x1000',
                        eth_type="0x0800",
                        table=output_table,
                        actions="strip_vlan,output:%s" % port)
            br.add_flow(priority=100, eth_dst=dst_mac,
                        eth_type="0x0800",
                        table=output_table, actions="output:%s" % port)

    def remove_ip_forwarder_flows(self, mac, ip):
        br = self.df_br if self.df_br is not None else self
        if common_utils.get_ip_version(ip) == 6:
            br.delete_flows(proto='ipv6', ipv6_dst=ip)
            br.delete_flows(proto='icmp6', icmpv6_type=135,
                            nd_target=ip)
            br.delete_flows(proto='icmp6', icmpv6_type=136,
                            nd_target=ip)
            br.delete_flows(eth_dst=mac, eth_type="0x86DD")
        else:
            br.delete_flows(proto='ip', nw_dst=ip)
            br.delete_flows(eth_dst=mac, eth_type="0x0800")

    def remove_external_gateway_flows(self, ex_gw_port):
        br = self.df_br if self.df_br is not None else self

        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        gateway_mac = ex_gw_port['mac_address']

        br.delete_flows(
            table=input_table, proto='arp', reg4='0x0')
        br.delete_flows(
            table=mac_learning_table, proto='arp')
        br.delete_flows(
            table=mac_learning_table, proto='icmp6')

        for fixed_ip in ex_gw_port['fixed_ips']:
            ip = fixed_ip['ip_address']
            if common_utils.get_ip_version(ip) == 6:
                br.delete_flows(proto='ipv6', dl_src=gateway_mac)
                br.delete_flows(proto='icmp6', dl_src=gateway_mac)
                br.delete_flows(proto='ipv6', ipv6_dst=ip)
                br.delete_flows(proto='icmp6', icmpv6_type=135,
                                      nd_target=ip)
                br.delete_flows(proto='icmp6', icmpv6_type=136,
                                      nd_target=ip)
            else:
                br.delete_flows(proto='ip', dl_src=gateway_mac)
                br.delete_flows(proto='icmp', dl_src=gateway_mac)
                br.delete_flows(proto='arp', arp_tpa=ip)
                br.delete_flows(proto='ip', nw_dst=ip)

        br.delete_flows(table=ext_output_table)
