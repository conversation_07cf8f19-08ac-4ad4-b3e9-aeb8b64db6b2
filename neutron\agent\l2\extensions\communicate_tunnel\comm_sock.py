# Copyright (c) 2022 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import os

import httplib2
from neutron_lib.utils import host
from oslo_config import cfg
from oslo_log import log as logging
import webob

from neutron._i18n import _
from neutron.agent.linux import utils as agent_utils

LOG = logging.getLogger(__name__)

OVS_AGENT_EVENT_SERVER_THREAD = (1 + host.cpu_count()) // 2
OVS_AGENT_EVENT_SERVER_BACKLOG = 4096


class OvsAagentEventHandler(object):
    def __init__(self, agent):
        self.agent = agent

    @webob.dec.wsgify(RequestClass=webob.Request)
    def __call__(self, req):
        fip_info = {
            'gateway_interface_name': req.headers['X-Neutron-Gateway-Device'],
            'floating_network_id': (
                req.headers['X-Neutron-Floating-Ip-network']),
            'floating_agent_port_mac': (
                req.headers['X-Neutron-Floating-Agent-Port-Mac']),
            'floating_ip_address': req.headers['X-Neutron-Floating-Ip'],
            'fixed_ip_address': req.headers['X-Neutron-Fixed-Ip'],
            'port_id': req.headers['X-Neutron-Port-Id'],
            'router_id': req.headers['X-Neutron-Router-Id'],
            'port_network_id': req.headers['X-Neutron-Network-Id'],
            'port_device_owner': req.headers['X-Neutron-Device-Owner'],
            'port_mac_address': req.headers['X-Neutron-Mac-Address'],
            'port_device_id': req.headers['X-Neutron-Device-Id'],
            'status': req.headers['X-Neutron-Status']
        }
        self.enqueue(fip_info)

    def enqueue(self, fip_info):
        LOG.debug('Handling notification for floating IP '
                  '%(fip)s.', {'fip': fip_info})
        self.agent.ovs_agent_event_callback(fip_info)


class OvsAgentEventServer(object):
    def __init__(self, agent, conf):
        self.agent = agent
        self.conf = conf

        agent_utils.ensure_directory_exists_without_file(
            self.get_ovs_agent_notify_socket_path(self.conf))

    @classmethod
    def get_ovs_agent_notify_socket_path(cls, conf):
        return os.path.join(conf.state_path, 'ovs-agent-event')

    def run(self):
        server = agent_utils.UnixDomainWSGIServer(
            'neutron-ovs-agent-event',
            num_threads=OVS_AGENT_EVENT_SERVER_THREAD)
        server.start(OvsAagentEventHandler(self.agent),
                     self.get_ovs_agent_notify_socket_path(self.conf),
                     workers=0,
                     backlog=OVS_AGENT_EVENT_SERVER_BACKLOG)
        server.wait()


class OvsAgentEventUnixDomainConnection(agent_utils.UnixDomainHTTPConnection):
    def __init__(self, *args, **kwargs):
        # Old style super initialization is required!
        agent_utils.UnixDomainHTTPConnection.__init__(
            self, *args, **kwargs)
        self.socket_path = (
            OvsAgentEventServer.
            get_ovs_agent_notify_socket_path(cfg.CONF))


def notify_ovs_agent(interface_name, floating_ip_network,
                     fip_agent_port_mac, fip, state):
    try:
        resp, _content = httplib2.Http().request(
            # Note that the message is sent via a Unix domain socket so that
            # the URL doesn't matter.
            'http://ovs_agent.local.unix.sock/',
            headers={
                'X-Neutron-Gateway-Device': str(interface_name),
                'X-Neutron-Floating-Ip-network': str(floating_ip_network),
                'X-Neutron-Floating-Agent-Port-Mac': str(fip_agent_port_mac),
                'X-Neutron-Floating-Ip': str(fip.get('floating_ip_address')),
                'X-Neutron-Fixed-Ip': str(fip.get('fixed_ip_address')),
                'X-Neutron-Port-Id': str(fip.get('port_id')),
                'X-Neutron-Router-Id': str(fip.get('router_id')),
                'X-Neutron-Network-Id': (
                    str(fip.get('port_details', {}).get('network_id'))),
                'X-Neutron-Device-Owner': (
                    str(fip.get('port_details', {}).get('device_owner'))),
                'X-Neutron-Mac-Address': (
                    str(fip.get('port_details', {}).get('mac_address'))),
                'X-Neutron-Device-Id': (
                    str(fip.get('port_details', {}).get('device_id'))),
                'X-Neutron-Status': str(state)},
            connection_type=OvsAgentEventUnixDomainConnection)
    except Exception as e:
        msg = ("Failed to communicate with openvswitch unix socket "
               "domain: %s") % e
        LOG.error(msg)
        raise Exception(msg)

    if resp.status != 200:
        LOG.error("Failed to communicate with openvswitch unix socket "
                  "domain: %s", resp)
        raise Exception(_('Unexpected response: %s') % resp)

    LOG.debug('Notified agent Floating IP %s, state %s', fip, state)
