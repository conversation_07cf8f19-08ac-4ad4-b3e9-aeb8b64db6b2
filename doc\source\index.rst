..
      Copyright 2011-2013 OpenStack Foundation
      All Rights Reserved.

      Licensed under the Apache License, Version 2.0 (the "License"); you may
      not use this file except in compliance with the License. You may obtain
      a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
      WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
      License for the specific language governing permissions and limitations
      under the License.

Welcome to Neutron's documentation!
===================================

Neutron is an OpenStack project to provide "network connectivity as a service"
between interface devices (e.g., vNICs) managed by other OpenStack services
(e.g., nova). It implements the `Neutron API`_.

.. _`Neutron API`: https://developer.openstack.org/api-ref/networking/

This documentation is generated by the Sphinx toolkit and lives in the source
tree.  Additional documentation on Neutron and other components of OpenStack
can be found on the `OpenStack wiki`_ and the `Neutron section of the wiki`.
The `Neutron Development wiki`_ is also a good resource for new contributors.

.. _`OpenStack wiki`: https://wiki.openstack.org
.. _`Neutron section of the wiki`: https://wiki.openstack.org/Neutron
.. _`Neutron Development wiki`: https://wiki.openstack.org/NeutronDevelopment

Enjoy!

Installation Guide
------------------

.. toctree::
   :maxdepth: 2

   Installation Guide <install/index>

Networking Guide
----------------

.. toctree::
   :maxdepth: 2

   admin/index

Configuration Reference
-----------------------
.. toctree::
   :maxdepth: 2

   configuration/index

CLI Reference
-------------

.. toctree::
   :maxdepth: 2

   cli/index

Neutron Feature Classification
------------------------------

.. toctree::
   :maxdepth: 2

   feature_classification/index

Contributor Guide
-----------------

.. toctree::
   :maxdepth: 2

   contributor/index

API Extensions
--------------

Go to https://developer.openstack.org/api-ref/network/ for information about the OpenStack Network API and its extensions.

Search
------

* :ref:`Neutron document search <search>`: Search the contents of this
  document.
* `OpenStack wide search <https://docs.openstack.org>`_: Search the wider
  set of OpenStack documentation, including forums.
