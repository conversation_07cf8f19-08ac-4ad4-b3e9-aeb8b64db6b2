#!/usr/bin/env python3
"""
System Routes Management Demo

This script demonstrates the server-side management of agent-generated 
system routes (default and direct routes) in the route table functionality.

Features demonstrated:
1. Automatic generation of system routes when router interfaces/gateways change
2. Server-side mapping and management of system routes
3. Prevention of manual manipulation of system routes
4. Using existing APIs to manage system routes through type field
"""

import json
from neutronclient.v2_0 import client as neutron_client
from keystoneauth1.identity import v3
from keystoneauth1 import session


class SystemRoutesDemo:
    def __init__(self, auth_url, username, password, project_name, 
                 user_domain_name='Default', project_domain_name='Default'):
        """Initialize Neutron client."""
        auth = v3.Password(
            auth_url=auth_url,
            username=username,
            password=password,
            project_name=project_name,
            user_domain_name=user_domain_name,
            project_domain_name=project_domain_name
        )
        sess = session.Session(auth=auth)
        self.neutron = neutron_client.Client(session=sess)
        
    def create_demo_environment(self):
        """Create demo router, network, and subnet."""
        print("Creating demo environment...")
        
        # Create network
        network = self.neutron.create_network({
            'network': {
                'name': 'demo-network',
                'admin_state_up': True
            }
        })
        network_id = network['network']['id']
        print(f"Created network: {network_id}")
        
        # Create subnet
        subnet = self.neutron.create_subnet({
            'subnet': {
                'name': 'demo-subnet',
                'network_id': network_id,
                'cidr': '********/24',
                'ip_version': 4,
                'gateway_ip': '********'
            }
        })
        subnet_id = subnet['subnet']['id']
        print(f"Created subnet: {subnet_id}")
        
        # Create router
        router = self.neutron.create_router({
            'router': {
                'name': 'demo-router',
                'admin_state_up': True
            }
        })
        router_id = router['router']['id']
        print(f"Created router: {router_id}")
        
        return router_id, network_id, subnet_id
    
    def demonstrate_system_routes(self, router_id, subnet_id):
        """Demonstrate system routes functionality."""
        print("\n=== System Routes Demonstration ===")
        
        # Step 1: Add router interface (triggers system_direct route creation)
        print("\n1. Adding router interface...")
        self.neutron.add_interface_router(router_id, {'subnet_id': subnet_id})
        print("Router interface added - system_direct routes should be created")
        
        # Step 2: Get route tables for the router
        print("\n2. Getting route tables...")
        route_tables = self.neutron.list_route_tables(router_id=router_id)
        print(f"Found {len(route_tables['route_tables'])} route tables")
        
        # Find default route table
        default_rt = None
        for rt in route_tables['route_tables']:
            if rt.get('default', False):
                default_rt = rt
                break
        
        if default_rt:
            print(f"Default route table ID: {default_rt['id']}")
            
            # Step 3: Get routes from default route table
            print("\n3. Getting routes from default route table...")
            routes = self.neutron.list_route_table_routes(
                routetable_id=default_rt['id'])
            
            print("Routes in default route table:")
            for route in routes.get('routes', []):
                print(f"  - {route['destination']} via {route['nexthop']} "
                      f"(type: {route['type']})")
            
            # Step 4: Try to manually add a system route (should fail)
            print("\n4. Attempting to manually add system route...")
            try:
                self.neutron.add_route_table_routes(default_rt['id'], {
                    'route_table': {
                        'routes': [{
                            'destination': '192.168.1.0/24',
                            'nexthop': '********',
                            'type': 'system_direct'
                        }]
                    }
                })
                print("ERROR: Manual system route addition should have failed!")
            except Exception as e:
                print(f"✓ Correctly prevented manual system route addition: {e}")
            
            # Step 5: Add a regular user route (should succeed)
            print("\n5. Adding regular user route...")
            try:
                self.neutron.add_route_table_routes(default_rt['id'], {
                    'route_table': {
                        'routes': [{
                            'destination': '***********/24',
                            'nexthop': '********0',
                            'type': 'ipv4'
                        }]
                    }
                })
                print("✓ Successfully added user route")
            except Exception as e:
                print(f"Failed to add user route: {e}")
        
        # Step 6: Create external network and set as gateway
        print("\n6. Creating external network and setting as gateway...")
        try:
            # Create external network
            ext_network = self.neutron.create_network({
                'network': {
                    'name': 'demo-external-network',
                    'admin_state_up': True,
                    'router:external': True
                }
            })
            ext_network_id = ext_network['network']['id']
            print(f"Created external network: {ext_network_id}")
            
            # Create external subnet
            ext_subnet = self.neutron.create_subnet({
                'subnet': {
                    'name': 'demo-external-subnet',
                    'network_id': ext_network_id,
                    'cidr': '*************/24',
                    'ip_version': 4,
                    'gateway_ip': '*************'
                }
            })
            print(f"Created external subnet: {ext_subnet['subnet']['id']}")
            
            # Set external gateway
            self.neutron.update_router(router_id, {
                'router': {
                    'external_gateway_info': {
                        'network_id': ext_network_id
                    }
                }
            })
            print("Set external gateway - system_default routes should be created")
            
            # Check routes again
            print("\n7. Checking routes after gateway addition...")
            if default_rt:
                routes = self.neutron.list_route_table_routes(
                    routetable_id=default_rt['id'])
                
                print("Updated routes in default route table:")
                system_routes = []
                user_routes = []
                for route in routes.get('routes', []):
                    print(f"  - {route['destination']} via {route['nexthop']} "
                          f"(type: {route['type']})")
                    if route['type'] in ['system_default', 'system_direct']:
                        system_routes.append(route)
                    else:
                        user_routes.append(route)
                
                print(f"\nSummary:")
                print(f"  System routes: {len(system_routes)}")
                print(f"  User routes: {len(user_routes)}")
                
        except Exception as e:
            print(f"Failed to create external network/gateway: {e}")
    
    def cleanup_demo_environment(self, router_id, network_id, subnet_id):
        """Clean up demo resources."""
        print("\n=== Cleanup ===")
        try:
            # Remove router interface
            self.neutron.remove_interface_router(router_id, {'subnet_id': subnet_id})
            print("Removed router interface")
            
            # Delete router
            self.neutron.delete_router(router_id)
            print("Deleted router")
            
            # Delete subnet
            self.neutron.delete_subnet(subnet_id)
            print("Deleted subnet")
            
            # Delete network
            self.neutron.delete_network(network_id)
            print("Deleted network")
            
        except Exception as e:
            print(f"Cleanup error: {e}")


def main():
    """Main demo function."""
    print("System Routes Management Demo")
    print("=" * 40)
    
    # Configuration - update these values for your environment
    config = {
        'auth_url': 'http://keystone:5000/v3',
        'username': 'admin',
        'password': 'admin_password',
        'project_name': 'admin'
    }
    
    try:
        # Initialize demo
        demo = SystemRoutesDemo(**config)
        
        # Create demo environment
        router_id, network_id, subnet_id = demo.create_demo_environment()
        
        # Demonstrate system routes functionality
        demo.demonstrate_system_routes(router_id, subnet_id)
        
        # Cleanup
        demo.cleanup_demo_environment(router_id, network_id, subnet_id)
        
        print("\nDemo completed successfully!")
        
    except Exception as e:
        print(f"Demo failed: {e}")


if __name__ == '__main__':
    main()
