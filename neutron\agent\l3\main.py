#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import sys

from oslo_config import cfg
from oslo_utils import importutils

from neutron.common import config as common_config
from neutron.conf.agent import common as config
from neutron.conf.agent.l3 import config as l3_config
from neutron.conf.agent.l3 import ha as ha_conf
from neutron.conf.agent.metadata import config as meta_conf

_main_modules = {
    'ovs-ofctl': 'neutron.agent.l3.openflow_utils.ofctl.main',
    'native': 'neutron.agent.l3.openflow_utils.native.main',
}


def register_opts(conf):
    l3_config.register_l3_agent_config_opts(l3_config.OPTS, conf)
    l3_config.register_l3_agent_ovs_opts(l3_config.ovs_opts, conf)
    ha_conf.register_l3_agent_ha_opts(conf)
    meta_conf.register_meta_conf_opts(meta_conf.SHARED_OPTS, conf)
    config.register_interface_driver_opts_helper(conf)
    config.register_agent_state_opts_helper(conf)
    config.register_interface_opts(conf)
    config.register_external_process_opts(conf)
    config.register_pddriver_opts(conf)
    config.register_ra_opts(conf)
    config.register_availability_zone_opts_helper(conf)


def main(manager='neutron.agent.l3.agent.L3NATAgentWithStateReport'):
    register_opts(cfg.CONF)
    common_config.init(sys.argv[1:])
    config.setup_logging()
    config.setup_privsep()
    driver_name = cfg.CONF.OVS.of_interface
    mod_name = _main_modules[driver_name]
    mod = importutils.import_module(mod_name)
    mod.main(manager=manager)
