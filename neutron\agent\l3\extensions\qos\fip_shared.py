#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import random

from neutron_lib.agent import l3_extension
from neutron_lib import constants
from neutron_lib.services.qos import constants as qos_consts
from oslo_log import log as logging
from oslo_utils import excutils

from neutron.agent.l3.extensions.qos import base as qos_base
from neutron.agent.l3.extensions.qos import fip as qos_fip
from neutron.agent.linux import fip_shared_tc_lib
from neutron.agent.linux import ip_lib
from neutron.api.rpc.callbacks import events
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc
from neutron.common import coordination
from neutron.common import exceptions as n_exc

LOG = logging.getLogger(__name__)

GW_IFB_DEV_PREFIX = 'qi-'
MAX_RETIES = 1000


def get_policy_key(policy_id, direction):
    return "%s-%s" % (policy_id, direction)


class SharedFipRateLimitMaps(qos_fip.RouterFipRateLimitMaps):
    LOCK_NAME = "fip-shared-qos-cache"

    def __init__(self):
        """
        The router_policy_map will be:
            router_policy_map = {
                router_id_1: {"policy-1-ingress": "class_id_1",
                              "policy-1-egress": "class_id_2"}
                router_id_2: {"policy-2-ingress": "class_id_3",
                              "policy-2-egress": "class_id_4"}
            }
        """
        self.router_policy_map = {}

        """
        The router_used_class_ids will be:
            router_used_class_ids = {
                router_id_1: set("class_id_1", "class_id_2"),
                router_id_2: set("class_id_2", "class_id_3")}
            }
        """
        self.router_used_class_ids = {}

        """
        The router_policy_fips will be:
            router_policy_fips = {
                router_id_1: {
                    policy_id_1: set("fip_1", "fip_2"),
                    policy_id_2: set("fip_3", "fip_4")
                },
                router_id_2: {
                    policy_id_1: set("fip_1", "fip_2"),
                    policy_id_2: set("fip_3", "fip_4")
                }
            }
        """
        self.router_policy_fips = {}
        """
        The router_gateway_port will be:
            router_gateway_port = {
                router_id_1: gateway_port_id,
                router_id_2: gateway_port_id
            }
        """
        self.router_gateway_port = {}
        super(SharedFipRateLimitMaps, self).__init__()

    @coordination.synchronized('class-id-lock-{router_id}')
    def generate_class_id(self, router_id):
        used_class_id = self.router_used_class_ids.get(router_id, set())
        cid = None
        times = 0
        while not cid or cid in used_class_id:
            cid = random.randint(1, 4000)
            times += 1
            if times >= MAX_RETIES:
                return
        used_class_id.add(cid)
        self.router_used_class_ids[router_id] = used_class_id
        return cid

    def _remove_class_id(self, router_id, class_id):
        used_class_id = self.router_used_class_ids.get(router_id, set())
        try:
            used_class_id.remove(class_id)
        except KeyError:
            pass
        self.router_used_class_ids[router_id] = used_class_id

    @coordination.synchronized('class-id-lock-{router_id}')
    def remove_class_id(self, router_id, class_id):
        self._remove_class_id(router_id, class_id)

    @coordination.synchronized('class-id-lock-{router_id}')
    def clean_router_all_fip_cache(self, router_id):
        super(SharedFipRateLimitMaps, self).clean_router_all_fip_cache(
            router_id)
        self.router_policy_map.pop(router_id, None)
        self.router_used_class_ids.pop(router_id, None)
        self.router_policy_fips.pop(router_id, None)
        self.router_gateway_port.pop(router_id, None)

    @coordination.synchronized('class-id-lock-{router_id}')
    def check_policy_class_id_under_router(
            self, router_id, policy_id, direction):
        router_policies = self.router_policy_map.get(router_id, {})
        policy_direction = get_policy_key(policy_id, direction)
        return router_policies.get(policy_direction)

    @coordination.synchronized('class-id-lock-{router_id}')
    def set_policy_class_id_under_router(
            self, router_id, policy_id, direction, class_id):
        router_policies = self.router_policy_map.get(router_id, {})
        policy_direction = "%s-%s" % (policy_id, direction)
        router_policies[policy_direction] = class_id
        self.router_policy_map[router_id] = router_policies

    @coordination.synchronized('class-id-lock-{router_id}')
    def remove_policy_class_id_under_router(
            self, router_id, policy_id, direction):
        router_policies = self.router_policy_map.get(router_id, {})
        policy_key = get_policy_key(policy_id, direction)
        router_policies.pop(policy_key, None)
        self.router_policy_map[router_id] = router_policies

    @coordination.synchronized('class-id-lock-{router_id}')
    def reset_policy_class_id_under_router(
            self, router_id, policy_id, direction):
        router_policies = self.router_policy_map.get(router_id, {})
        policy_key = get_policy_key(policy_id, direction)
        class_id = router_policies.pop(policy_key, None)
        router_policies[policy_key] = None
        self.router_policy_map[router_id] = router_policies
        if class_id:
            self._remove_class_id(router_id, class_id)

    @coordination.synchronized('class-id-lock-{router_id}')
    def get_policy_class_id_under_router(
            self, router_id, policy_id, direction):
        router_policies = self.router_policy_map.get(router_id, {})
        key = get_policy_key(policy_id, direction)
        return router_policies.get(key)


class SharedFipQosAgentExtension(qos_base.L3QosAgentExtensionBase,
                                 l3_extension.L3AgentExtension):

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        self.resource_rpc = resources_rpc.ResourcesPullRpcApi()
        self.qos_map = SharedFipRateLimitMaps()
        self._register_rpc_consumers()

    def _handle_notification(self, context, resource_type,
                             qos_policies, event_type):
        if event_type == events.UPDATED:
            for qos_policy in qos_policies:
                self._process_update_policy(context, qos_policy)

    def _process_update_policy(self, context, qos_policy):
        old_qos_policy = self.qos_map.get_policy(qos_policy.id)
        if old_qos_policy:
            if self._policy_rules_modified(old_qos_policy, qos_policy):
                for router_id, polices in (
                        self.qos_map.router_policy_map.items()):
                    key_in = get_policy_key(
                        qos_policy.id, constants.INGRESS_DIRECTION)
                    key_out = get_policy_key(
                        qos_policy.id, constants.INGRESS_DIRECTION)
                    if key_in in polices.keys() or key_out in polices.keys():
                        router_info = self._get_router_info(router_id)
                        if not router_info:
                            continue
                        device = self._get_rate_limit_ip_device(router_info)
                        if not device:
                            LOG.debug("Router %s does not have a floating IP "
                                      "related device, skipping.", router_id)
                            continue

                        self._process_router_floatingips(
                            context, router_info, device)

            self.qos_map.update_policy(qos_policy)

    def _get_rate_limit_ip_device(self, router_info):
        ex_gw_port = router_info.get_ex_gw_port()
        if not ex_gw_port:
            return
        agent_mode = router_info.agent_conf.agent_mode
        is_distributed_router = router_info.router.get('distributed')
        if is_distributed_router and agent_mode == (
                constants.L3_AGENT_MODE_DVR_SNAT):
            # DVR edge (or DVR edge ha) router
            if not router_info._is_this_snat_host():
                return
            name = router_info.get_snat_external_device_interface_name(
                ex_gw_port)
        else:
            # DVR local router
            # Legacy/HA router
            name = router_info.get_external_device_interface_name(ex_gw_port)
        if not name:
            # DVR local router in dvr_no_external agent mode may not have
            # such rfp-device.
            return
        namespace = router_info.get_gw_ns_name()
        return ip_lib.IPDevice(name, namespace=namespace)

    def get_fip_qos_rates(self, context, policy_id):
        if policy_id is None:
            # process_ip_rate_limit will treat value 0 as
            # cleaning the tc filters if exits or no action.
            return {constants.INGRESS_DIRECTION: {
                        "rate": qos_base.IP_DEFAULT_RATE,
                        "burst": qos_base.IP_DEFAULT_BURST},
                    constants.EGRESS_DIRECTION: {
                        "rate": qos_base.IP_DEFAULT_RATE,
                        "burst": qos_base.IP_DEFAULT_BURST}}
        policy = self.resource_rpc.pull(
            context, resources.QOS_POLICY, policy_id)
        self.qos_map.update_policy(policy)
        return self.get_policy_rates(policy)

    def _get_policy_floatingips(self, floatingips):
        qos_fips = {}
        for fip in floatingips:
            qos_policy_id = fip.get('qos_policy_id')
            if not qos_policy_id:
                continue
            policy_fips = qos_fips.get(qos_policy_id, set())
            policy_fips.add(fip['floating_ip_address'])
            qos_fips[fip['qos_policy_id']] = policy_fips
        return qos_fips

    def _get_ifb_dev_name(self, router_info, gw_port_id):
        return (GW_IFB_DEV_PREFIX +
                gw_port_id)[:router_info.driver.DEV_NAME_LEN]

    def _get_tc_wrapper(self, device):
        return fip_shared_tc_lib.FipSharedTcCommand(
            device.name, namespace=device.namespace)

    def _install_class_bandwidth_rule(self, router_id, policy_id,
                                      policy_rates,
                                      direction, tc_wrapper):
        rates = policy_rates.get(direction)
        if not rates:
            return

        cid = self.qos_map.check_policy_class_id_under_router(
            router_id, policy_id, direction)

        if (rates['rate'] == qos_base.IP_DEFAULT_RATE and
                rates['burst'] == qos_base.IP_DEFAULT_BURST):
            # Policy does not have rule for this direction
            if cid:
                tc_wrapper.delete_class_with_all_filters(cid)
                self.qos_map.reset_policy_class_id_under_router(
                    router_id, policy_id, direction)
            return

        if not cid:
            cid = self.qos_map.generate_class_id(router_id)
            self.qos_map.set_policy_class_id_under_router(
                router_id, policy_id, direction, cid)

        tc_wrapper.install_class_bandwidth(
            cid,
            rates['rate'],
            rates['burst'])

    def _remove_class_bandwidth_rule(self, router_id, policy_id,
                                     direction, tc_wrapper):
        class_id = self.qos_map.get_policy_class_id_under_router(
            router_id, policy_id, direction)
        if class_id:
            tc_wrapper.remove_class_bandwidth(class_id)
        self.qos_map.remove_policy_class_id_under_router(
            router_id, policy_id, direction)
        self.qos_map.remove_class_id(router_id, class_id)

    def _add_fip_filter_under_class(self, router_id, policy_id,
                                    direction,
                                    tc_wrapper, ip_addr):
        cid = self.qos_map.check_policy_class_id_under_router(
                router_id, policy_id, direction)
        if not cid:
            return
        tc_wrapper.install_ip_filter_under_class(direction, cid, ip_addr)

    def _remove_fip_filter_under_class(self, tc_wrapper, ip):
        LOG.debug("Remove filter for IP %s on device %s",
                  ip, tc_wrapper.name)
        tc_wrapper.remove_ip_filter_under_class(ip)

    def remove_ifb_dev(self, router_info, gw_port_id, namespace):
        ifb_name = self._get_ifb_dev_name(router_info, gw_port_id)
        ifb_dev = ip_lib.IPDevice(ifb_name, namespace=namespace)
        ip_wrapper = ip_lib.IPWrapper(namespace)
        if ifb_dev.exists():
            ifb_dev.link.set_down()
            ip_wrapper.del_ifb(ifb_name)

    def get_shared_tc_wrappers(self, router_info, device):
        # check_and_create tc qdisc
        # check_and_create ifb dev
        ip_wrapper = ip_lib.IPWrapper(device.namespace)
        gw_port_id = router_info.get_ex_gw_port()['id']
        self.qos_map.router_gateway_port[router_info.router_id] = gw_port_id
        ifb_name = self._get_ifb_dev_name(router_info, gw_port_id)
        ifb_dev = ip_lib.IPDevice(ifb_name, namespace=device.namespace)
        if not ifb_dev.exists():
            try:
                ifb_dev = ip_wrapper.add_ifb(ifb_name)
                ifb_dev.link.set_qlen(qlen=1000)
            except n_exc.ProcessExecutionError as e:
                with excutils.save_and_reraise_exception() as ctx:
                    if e.returncode == 2 and "File exists" in e.message:
                        ctx.reraise = False
                        LOG.debug("Router IFB dev %s exists.", ifb_name)
                        ifb_dev = ip_lib.IPDevice(ifb_name,
                                                  namespace=device.namespace)
        ifb_dev.link.set_up()
        # check and create tc filter mirror to ifb dev
        tc_wrapper = self._get_tc_wrapper(device)
        tc_wrapper.create_egress_qdisc()

        ifb_wrapper = self._get_tc_wrapper(ifb_dev)
        ifb_wrapper.create_egress_qdisc()

        return tc_wrapper, ifb_wrapper

    def process_floating_ip_addresses(self, context, router_info):
        # For legacy and HA router, it will be all floating IPs to qg-device
        # of qrouter-namespace in (all ha router hosted) network node.
        # For DVR or DVR+HA router in network node, it will be all floating
        # IPs to qg-device of snat-namespace in (all ha router hosted)
        # network node.
        is_distributed_router = router_info.router.get('distributed')
        agent_mode = router_info.agent_conf.agent_mode
        LOG.debug("Start processing floating IP QoS for "
                  "router %(router_id)s, router "
                  "distributed: %(distributed)s, "
                  "agent mode: %(agent_mode)s",
                  {"router_id": router_info.router_id,
                   "distributed": is_distributed_router,
                   "agent_mode": agent_mode})
        if is_distributed_router and agent_mode in [
                constants.L3_AGENT_MODE_DVR_NO_EXTERNAL,
                constants.L3_AGENT_MODE_DVR]:
            # dvr and dvr_no_external agent node can not install shared rules.
            return

        device = self._get_rate_limit_ip_device(router_info)
        if not device:
            LOG.debug("No relevant QoS device found "
                      "for router: %s", router_info.router_id)
            gw_port_id = self.qos_map.router_gateway_port.get(
                router_info.router_id)
            if gw_port_id:
                namespace = router_info.get_gw_ns_name()
                self.remove_ifb_dev(router_info, gw_port_id, namespace)
            self.qos_map.router_gateway_port.pop(router_info.router_id, None)
            return

        self._process_router_floatingips(context, router_info, device)

    def _clean_original_qos_filters(self, tc_wrapper):
        for direction in constants.VALID_DIRECTIONS:
            tc_wrapper.clear_all_dedicated_filters(direction)

    def _clean_filter_and_class(self, tc_wrapper):
        tc_wrapper.delete_class_all_filters()
        tc_wrapper.delete_all_classes()

    def _should_add_fip_filter(self, direction, qos_policy_id):
        # Get policy from map since we have install class for it
        qos_policy = self.qos_map.get_policy(qos_policy_id)
        policy_rates = self.get_policy_rates(qos_policy)
        rates = policy_rates.get(direction)
        if not rates:
            return False
        if (rates['rate'] == qos_base.IP_DEFAULT_RATE and
                rates['burst'] == qos_base.IP_DEFAULT_BURST):
            return False
        return True

    def _process_router_floatingips(self, context, router_info, device):
        router_id = router_info.router_id
        LOG.debug("Process router %s floating IPs.", router_id)

        tc_wrapper, ifb_wrapper = self.get_shared_tc_wrappers(
            router_info, device)

        floating_ips = (router_info.get_floating_ips() +
                        router_info.get_port_forwarding_fips() +
                        router_info.get_elastic_snat_fips())

        # Cache is empty that means agent restart, remove all classes first.
        if not self.qos_map.router_policy_map.get(router_id):
            self._clean_original_qos_filters(tc_wrapper)
            self._clean_filter_and_class(tc_wrapper)
            self._clean_filter_and_class(ifb_wrapper)

        tc_wrapper.check_create_ifb_direct(ifb_wrapper.name)

        # create class rule for policy
        router_policy_fips = self._get_policy_floatingips(floating_ips)
        for policy_id in router_policy_fips.keys():
            policy_rates = self.get_fip_qos_rates(context, policy_id)
            self._install_class_bandwidth_rule(
                router_id,
                policy_id,
                policy_rates, constants.INGRESS_DIRECTION, ifb_wrapper)

            self._install_class_bandwidth_rule(
                router_id,
                policy_id,
                policy_rates, constants.EGRESS_DIRECTION, tc_wrapper)

        # create filter rule for floating IPs
        current_fips = self.qos_map.router_floating_ips.get(
            router_id, set())
        LOG.debug("Router %s current floating IPs: %s",
                  router_id, current_fips)
        new_fips = set()
        for fip in floating_ips:
            fip_addr = fip['floating_ip_address']
            new_fips.add(fip_addr)
            qos_policy_id = fip.get(qos_consts.QOS_POLICY_ID)
            if not qos_policy_id:
                self._remove_fip_filter_under_class(tc_wrapper, fip_addr)
                self._remove_fip_filter_under_class(ifb_wrapper, fip_addr)
                continue
            if self._should_add_fip_filter(
                    constants.INGRESS_DIRECTION, qos_policy_id):
                self._add_fip_filter_under_class(
                    router_id, qos_policy_id,
                    constants.INGRESS_DIRECTION, ifb_wrapper, fip_addr)
            else:
                self._remove_fip_filter_under_class(ifb_wrapper, fip_addr)
            if self._should_add_fip_filter(
                    constants.EGRESS_DIRECTION, qos_policy_id):
                self._add_fip_filter_under_class(
                    router_id, qos_policy_id,
                    constants.EGRESS_DIRECTION, tc_wrapper, fip_addr)
            else:
                self._remove_fip_filter_under_class(tc_wrapper, fip_addr)
        LOG.debug("Router %s new floating IPs: %s",
                  router_id, new_fips)

        # Processing floating IPs not binding to ports under this router
        self.qos_map.router_floating_ips[router_id] = new_fips
        fips_removed = current_fips - new_fips
        LOG.debug("Router %s removed floating IPs: %s",
                  router_id, fips_removed)
        for fip in fips_removed:
            self._remove_fip_filter_under_class(tc_wrapper, fip)
            self._remove_fip_filter_under_class(ifb_wrapper, fip)
            self.qos_map.clean_by_resource(fip)

        # Processing policy that not used by router
        cache_policy_fips = self.qos_map.router_policy_fips.get(
            router_id, {})
        old_policies = set(cache_policy_fips.keys())
        new_policies = set(router_policy_fips.keys())
        for policy_id in old_policies - new_policies:
            self._remove_class_bandwidth_rule(
                router_id, policy_id,
                constants.INGRESS_DIRECTION, ifb_wrapper)
            self._remove_class_bandwidth_rule(
                router_id, policy_id,
                constants.EGRESS_DIRECTION, tc_wrapper)

        self.qos_map.router_policy_fips[router_id] = router_policy_fips

    def add_router(self, context, data):
        router_info = self._get_router_info(data['id'])
        if router_info:
            self.process_floating_ip_addresses(context, router_info)

    def update_router(self, context, data):
        router_info = self._get_router_info(data['id'])
        if router_info:
            self.process_floating_ip_addresses(context, router_info)

    def delete_router(self, context, data):
        self.qos_map.clean_router_all_fip_cache(data['id'])

    def ha_state_change(self, context, data):
        pass
