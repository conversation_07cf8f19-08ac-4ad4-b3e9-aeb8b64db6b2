# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add router id to metering labels

Revision ID: ba69e870128f
Revises: ae6d48eb3934
Create Date: 2020-07-02 23:52:02.755269

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'ba69e870128f'
down_revision = 'ae6d48eb3934'


def upgrade():
    table_name = 'meteringlabels'
    index_name = 'ix_meteringlabels_router_id'
    existColumn = 0b0
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        existColumn |= 0b1 if column['name'] == 'router_id' else 0b0

    if not existColumn & 0b1:
        op.add_column(
            'meteringlabels',
            sa.Column(
                'router_id',
                sa.String(
                    length=db_const.UUID_FIELD_SIZE),
                nullable=True))

    if index_name not in [idx['name'] for idx in
                          insp.get_indexes(table_name)]:
        op.create_index(index_name, table_name, ['router_id'], unique=False)
