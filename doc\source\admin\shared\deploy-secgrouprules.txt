.. code-block:: console

   $ openstack security group rule create --proto icmp default
   +------------------+-----------+
   | Field            | Value     |
   +------------------+-----------+
   | direction        | ingress   |
   | ethertype        | IPv4      |
   | protocol         | icmp      |
   | remote_ip_prefix | 0.0.0.0/0 |
   +------------------+-----------+

   $ openstack security group rule create --ethertype IPv6 --proto ipv6-icmp default
   +-----------+-----------+
   | Field     | Value     |
   +-----------+-----------+
   | direction | ingress   |
   | ethertype | IPv6      |
   | protocol  | ipv6-icmp |
   +-----------+-----------+

   $ openstack security group rule create --proto tcp --dst-port 22 default
   +------------------+-----------+
   | Field            | Value     |
   +------------------+-----------+
   | direction        | ingress   |
   | ethertype        | IPv4      |
   | port_range_max   | 22        |
   | port_range_min   | 22        |
   | protocol         | tcp       |
   | remote_ip_prefix | 0.0.0.0/0 |
   +------------------+-----------+

   $ openstack security group rule create --ethertype IPv6 --proto tcp --dst-port 22 default
   +------------------+-----------+
   | Field            | Value     |
   +------------------+-----------+
   | direction        | ingress   |
   | ethertype        | IPv6      |
   | port_range_max   | 22        |
   | port_range_min   | 22        |
   | protocol         | tcp       |
   +------------------+-----------+
