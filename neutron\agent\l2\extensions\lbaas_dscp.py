#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib.agent import l2_extension
from neutron_lib import constants as n_const
from oslo_concurrency import lockutils
from oslo_config import cfg
from oslo_log import log as logging

from neutron.agent.common import flows_process
from neutron.api.rpc.callbacks import resources
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants

LOG = logging.getLogger(__name__)


class LbaasDscpExtensionPortInfoAPI(object):
    def __init__(self, cache_api):
        self.cache_api = cache_api

    def get_port_info(self, port_id):
        port_obj = self.cache_api.get_resource_by_id(
            resources.PORT, port_id)
        if not port_obj:
            LOG.warning('Failed to get port details, port id: %s',
                        port_id)
            return
        return port_obj

    def get_port_ip(self, port_detail):
        fixed_ips = []
        for ip in port_detail['fixed_ips']:
            subnet = self.cache_api.get_resource_by_id(
                resources.SUBNET, ip['subnet_id'])
            if subnet.network_id == port_detail['network_id']:
                fixed_ips.append((str(ip['ip_address']), subnet.id))
        return fixed_ips


class LbaasDscpAgentExtension(
    flows_process.BridgeNetworkingDataPathFlows,
    l2_extension.L2AgentExtension):

    SUPPORTED_RESOURCE_TYPES = [resources.PORT]
    PORT_DSCP_LEARN_ATTRS_CACHE = {}

    def initialize(self, connection, driver_type):
        """Initialize LbaasDscpAgentExtension agent extension."""
        self.ext_api = LbaasDscpExtensionPortInfoAPI(self.rcache_api)
        self.int_br = self.agent_api.request_int_br()

    def consume_api(self, agent_api):
        """Allows an extension to gain access to resources internal to the
           neutron agent and otherwise unavailable to the extension.
        """
        self.agent_api = agent_api
        self.plugin_rpc = agent_api.plugin_rpc
        self.rcache_api = agent_api.plugin_rpc.remote_resource_cache

    @lockutils.synchronized('lbaas-dscp-learn-attrs-cache')
    def set_port_dscp_learn_attrs_cache(self, port_id, port_dscp_learn_attrs):
        self.PORT_DSCP_LEARN_ATTRS_CACHE[port_id] = port_dscp_learn_attrs

    @lockutils.synchronized('lbaas-dscp-learn-attrs-cache')
    def get_port_dscp_learn_attrs_from_cache(self, port_id):
        return self.PORT_DSCP_LEARN_ATTRS_CACHE.pop(port_id, None)

    def handle_port(self, context, port_detail):
        """Handle lbaas port dscp attributes

        This method applies dscp flows to a lbaas port.
        Update events are handled in _handle_notification.
        """
        if not cfg.CONF.AGENT.enable_lbaas_dscp:
            LOG.warning("Failed to update lbaas backend port dscp attributes "
                        "because enable_lbaas_dscp = false.")
            return
        port_data = self.ext_api.get_port_info(port_detail['port_id'])
        if not port_data:
            return
        self.delete_dscp_learn_flow(port_detail)
        old_dscp_learn_attrs = self.get_port_dscp_learn_attrs_from_cache(
            port_detail['port_id'])
        if old_dscp_learn_attrs:
            LOG.info("Delete lbaas backend port %(port_id)s dscp attributes,"
                     "original attributes are %(old_attrs)s ",
                     port_detail['port_id'], old_dscp_learn_attrs)
            self.set_port_dscp_learn_attrs_cache(port_detail['port_id'], None)
        cloud_attributes = port_data.cloud_attributes
        if not cloud_attributes:
            return
        cloud_attrs = cloud_attributes.cloud_attrs
        if not cloud_attrs:
            return
        new_dscp_learn_attrs = cloud_attrs.get('dscp_learn')
        try:
            if not isinstance(new_dscp_learn_attrs, list):
                return
            for new_attr in new_dscp_learn_attrs:
                self.process_dscp_learn_flow(port_detail, new_attr)
            dscp_learn_attrs_cache = {"port_id": port_detail['port_id'],
                                      "network_id": port_detail['network_id'],
                                      "fixed_ips": port_detail['fixed_ips'],
                                      "vif_port": port_detail['vif_port'],
                                      "dscp_learn": new_dscp_learn_attrs}
            self.set_port_dscp_learn_attrs_cache(port_detail['port_id'],
                                                 dscp_learn_attrs_cache)
            LOG.info("Update port %(port_id)s dscp attributes, current dscp "
                     "attributes are %(new_attrs)s",
                     port_detail['port_id'], dscp_learn_attrs_cache)
        except Exception as err:
            LOG.info("Failed to set lbaas backend port %s dscp attributes, "
                     "error: %s", port_detail['port_id'], err)

    def delete_port(self, context, port_detail):
        if not cfg.CONF.AGENT.enable_lbaas_dscp:
            LOG.warning("Failed to update lb backend port dscp attributes "
                        "because enable_lbaas_dscp = false.")
            return
        cloud_attrs = self.get_port_dscp_learn_attrs_from_cache(
            port_detail['port_id'])
        if not cloud_attrs:
            LOG.info("No lbaas backend port cloud attributes found for %s, "
                     "no relevant flows to remove", port_detail['port_id'])
            return
        self.delete_dscp_learn_flow(port_detail)

    def process_dscp_learn_flow(self, port_detail, dscp_learn_attrs):
        if not isinstance(dscp_learn_attrs, dict):
            return
        vif_port = port_detail['vif_port']
        if not vif_port or not port_detail['fixed_ips']:
            return
        fixed_ips = self.ext_api.get_port_ip(port_detail)
        ip_addrs = [ip[0] for ip in fixed_ips]
        for ip in ip_addrs:
            tos = dscp_learn_attrs.get('tos')
            protocol = dscp_learn_attrs.get('protocol')
            if not (tos or protocol):
                LOG.info("lbaas backend port dscp attributes need tos and "
                         "protocol two attributes.")
                return
            eth_type = ""
            proto_str = ""
            if netaddr.IPNetwork(ip).version == 4:
                eth_type = "0x0800"
            if netaddr.IPNetwork(ip).version == 6:
                eth_type = "0x86dd"
            port_dst_str = \
                "NXM_OF_%(ip_proto)s_DST[]=NXM_OF_%(ip_proto)s_SRC[]" \
                % {'ip_proto': protocol.upper()}
            port_src_str = \
                "NXM_OF_%(ip_proto)s_SRC[]=NXM_OF_%(ip_proto)s_DST[]" \
                % {'ip_proto': protocol.upper()}
            mac_match = "load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[]"
            if netaddr.IPNetwork(ip).version == 4:
                ip_src = "NXM_OF_IP_SRC[]=NXM_OF_IP_DST[]"
                ip_dst = "NXM_OF_IP_DST[]=NXM_OF_IP_SRC[]"
            else:
                ip_src = "NXM_NX_IPV6_SRC[]=NXM_NX_IPV6_DST[]"
                ip_dst = "NXM_NX_IPV6_DST[]=NXM_NX_IPV6_SRC[]"
            if protocol == n_const.PROTO_NAME_TCP:
                proto_str = "eth_type=%(eth_type)s," \
                            "ip_proto=%(ip_proto)s" \
                            % {'eth_type': eth_type,
                               'ip_proto': n_const.PROTO_NUM_TCP}
            elif protocol == n_const.PROTO_NAME_UDP:
                proto_str = "eth_type=%(eth_type)s, " \
                            "ip_proto=%(ip_proto)s" \
                            % {'eth_type': eth_type,
                               'ip_proto': n_const.PROTO_NUM_UDP}
            if cfg.CONF.SECURITYGROUP.firewall_driver == "openvswitch":
                learned_flow = ("cookie=%(cookie)s,"
                                "table=%(table)s,"
                                "priority=100,idle_timeout=%(idle_timeout)s,"
                                "hard_timeout=%(hard_timeout)s,"
                                "%(proto)s,"
                                "reg5=%(reg5)s,"
                                "%(ip_src)s,"
                                "%(ip_dst)s,"
                                "%(port_src)s,"
                                "%(port_dst)s,"
                                "%(mac_match)s" %
                                {'cookie': self.int_br.default_cookie,
                                 'table': constants.DSCP_LEARN_TABLE,
                                 'idle_timeout':
                                     cfg.CONF.AGENT.dscp_idle_timeout,
                                 'hard_timeout':
                                     cfg.CONF.AGENT.dscp_hard_timeout,
                                 'proto': proto_str,
                                 'reg5': "0x{:x}".format(vif_port.ofport),
                                 'ip_src': ip_src,
                                 'ip_dst': ip_dst,
                                 'port_dst': port_dst_str,
                                 'port_src': port_src_str,
                                 'mac_match': mac_match})
                self.int_br.add_flow(table=constants.DSCP_TABLE,
                                     priority=20,
                                     proto=proto_str,
                                     nw_tos=tos,
                                     reg5="0x{:x}".format(vif_port.ofport),
                                     dl_dst=vif_port.vif_mac,
                                     actions="learn(%s)" % learned_flow)
            if cfg.CONF.SECURITYGROUP.firewall_driver == \
                    "openvswitch_stateless":
                learned_flow = ("cookie=%(cookie)s,"
                                "table=%(table)s,"
                                "priority=100,idle_timeout=%(idle_timeout)s,"
                                "hard_timeout=%(hard_timeout)s,"
                                "NXM_OF_VLAN_TCI[0..11],"
                                "%(proto)s,"
                                "%(ip_src)s,"
                                "%(ip_dst)s,"
                                "%(port_src)s,"
                                "%(port_dst)s,"
                                "%(mac_match)s" %
                                {'cookie': self.int_br.default_cookie,
                                 'table': constants.DSCP_LEARN_TABLE,
                                 'idle_timeout':
                                     cfg.CONF.AGENT.dscp_idle_timeout,
                                 'hard_timeout':
                                     cfg.CONF.AGENT.dscp_hard_timeout,
                                 'proto': proto_str,
                                 'ip_src': ip_src,
                                 'ip_dst': ip_dst,
                                 'port_dst': port_dst_str,
                                 'port_src': port_src_str,
                                 'mac_match': mac_match})
                vlan = self.int_br.get_value_from_other_config(
                    vif_port.port_name, "tag", int)
                self.int_br.add_flow(table=constants.DSCP_TABLE,
                                     priority=20,
                                     proto=proto_str,
                                     dl_vlan=vlan,
                                     nw_tos=tos,
                                     dl_dst=vif_port.vif_mac,
                                     actions="learn(%s)" % learned_flow)

    def delete_dscp_learn_flow(self, port_detail):
        vif_port = port_detail['vif_port']
        fixed_ips = self.ext_api.get_port_ip(port_detail)
        ip_addrs = [ip[0] for ip in fixed_ips]
        try:
            self.int_br.delete_flows(
                table=constants.DSCP_TABLE,
                dl_dst=vif_port.vif_mac)
            for ip in ip_addrs:
                if netaddr.IPNetwork(ip).version == 4:
                    self.int_br.delete_flows(
                        table=constants.DSCP_LEARN_TABLE,
                        nw_src=ip)
                else:
                    self.int_br.delete_flows(
                        table=constants.DSCP_LEARN_TABLE,
                        ipv6_src=ip)

        except Exception as err:
            LOG.debug("Failed to remove dscp learn dscp flows for lbaas "
                      "backend port %s, error: %s",
                      port_detail['port_id'], err)
