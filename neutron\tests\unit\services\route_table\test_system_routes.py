#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import context
from neutron_lib import exceptions as n_exc

from neutron.extensions import _route_table as apidef
from neutron.services.route_table import plugin as rt_plugin
from neutron.tests.unit.extensions import test_route_table


class TestSystemRoutes(test_route_table.TestRouteTableExtension):

    def setUp(self):
        super(TestSystemRoutes, self).setUp()
        self.plugin = self.rt_plugin

    def test_system_routes_generation(self):
        """Test system routes are generated correctly."""
        # Create a router
        router = self._create_router()
        router_id = router['id']
        
        # Create internal interface
        subnet = self._create_subnet()
        self.l3_plugin.add_router_interface(
            self.ctx, router_id, {'subnet_id': subnet['id']})
        
        # Set external gateway
        ext_net = self._create_external_network()
        ext_subnet = self._create_subnet(network_id=ext_net['id'], 
                                       cidr='***********/24',
                                       gateway_ip='***********')
        self.l3_plugin.update_router(
            self.ctx, router_id,
            {'router': {'external_gateway_info': {
                'network_id': ext_net['id']}}})
        
        # Get system routes
        system_routes = self.plugin.get_system_routes(self.ctx, router_id)
        
        # Verify system routes are created
        self.assertGreater(len(system_routes), 0)
        
        # Check for system_direct routes
        direct_routes = [r for r in system_routes if r['type'] == 'system_direct']
        self.assertGreater(len(direct_routes), 0)
        
        # Check for system_default routes
        default_routes = [r for r in system_routes if r['type'] == 'system_default']
        self.assertGreater(len(default_routes), 0)

    def test_prevent_manual_system_route_addition(self):
        """Test that users cannot manually add system routes."""
        router = self._create_router()
        router_id = router['id']
        
        # Get default route table
        default_rt_id = self.plugin._get_default_rt_id(self.ctx, router_id)
        
        # Try to add a system route manually
        route_table = {
            'route_table': {
                'routes': [{
                    'destination': '0.0.0.0/0',
                    'nexthop': '***********',
                    'type': 'system_default'
                }]
            }
        }
        
        # Should raise BadRequest
        self.assertRaises(
            n_exc.BadRequest,
            self.plugin.add_route_table_routes,
            self.ctx, default_rt_id, route_table)

    def test_prevent_manual_system_route_removal(self):
        """Test that users cannot manually remove system routes."""
        router = self._create_router()
        router_id = router['id']
        
        # Create internal interface to generate system routes
        subnet = self._create_subnet()
        self.l3_plugin.add_router_interface(
            self.ctx, router_id, {'subnet_id': subnet['id']})
        
        # Get default route table
        default_rt_id = self.plugin._get_default_rt_id(self.ctx, router_id)
        
        # Get system routes
        system_routes = self.plugin.get_system_routes(self.ctx, router_id)
        if system_routes:
            # Try to remove a system route manually
            route_table = {
                'route_table': {
                    'routes': [system_routes[0]]
                }
            }
            
            # Should raise BadRequest
            self.assertRaises(
                n_exc.BadRequest,
                self.plugin.remove_route_table_routes,
                self.ctx, default_rt_id, route_table)

    def test_system_routes_sync_on_interface_changes(self):
        """Test system routes are synced when interfaces change."""
        router = self._create_router()
        router_id = router['id']
        
        # Initially no system routes
        initial_routes = self.plugin.get_system_routes(self.ctx, router_id)
        
        # Add interface
        subnet = self._create_subnet()
        self.l3_plugin.add_router_interface(
            self.ctx, router_id, {'subnet_id': subnet['id']})
        
        # Should have system routes now
        after_add_routes = self.plugin.get_system_routes(self.ctx, router_id)
        self.assertGreater(len(after_add_routes), len(initial_routes))
        
        # Remove interface
        self.l3_plugin.remove_router_interface(
            self.ctx, router_id, {'subnet_id': subnet['id']})
        
        # System routes should be updated
        after_remove_routes = self.plugin.get_system_routes(self.ctx, router_id)
        self.assertEqual(len(after_remove_routes), len(initial_routes))

    def test_manual_sync_system_routes(self):
        """Test manual synchronization of system routes."""
        router = self._create_router()
        router_id = router['id']
        
        # Manually trigger sync
        synced_routes = self.plugin.sync_system_routes(self.ctx, router_id)
        
        # Should return current system routes
        self.assertIsInstance(synced_routes, list)

    def test_get_system_routes_with_filters(self):
        """Test getting system routes with type filters."""
        router = self._create_router()
        router_id = router['id']
        
        # Create internal interface and external gateway
        subnet = self._create_subnet()
        self.l3_plugin.add_router_interface(
            self.ctx, router_id, {'subnet_id': subnet['id']})
        
        ext_net = self._create_external_network()
        ext_subnet = self._create_subnet(network_id=ext_net['id'], 
                                       cidr='***********/24',
                                       gateway_ip='***********')
        self.l3_plugin.update_router(
            self.ctx, router_id,
            {'router': {'external_gateway_info': {
                'network_id': ext_net['id']}}})
        
        # Get only direct routes
        direct_routes = self.plugin.get_system_routes(
            self.ctx, router_id, filters={'type': ['system_direct']})
        
        # All returned routes should be system_direct
        for route in direct_routes:
            self.assertEqual(route['type'], 'system_direct')
        
        # Get only default routes
        default_routes = self.plugin.get_system_routes(
            self.ctx, router_id, filters={'type': ['system_default']})
        
        # All returned routes should be system_default
        for route in default_routes:
            self.assertEqual(route['type'], 'system_default')
