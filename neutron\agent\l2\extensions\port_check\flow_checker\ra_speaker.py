#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib import constants

from neutron.agent.common import utils
from neutron.agent.l2.extensions.port_check.flow_checker import base
from neutron.agent.l2.extensions import ra_speaker


class RASpeakerFlowCheck(base.ExtensionFlowCheckBase,
                         ra_speaker.RASpeakerAgentExtension):
    def __init__(self, br_int):
        super(RASpeakerFlowCheck, self).__init__(br_int)

    def prepare_flow(self, port_info):
        self.add_ipv6_ns_flow(port_info['ofport'], port_info['mac_address'])

    def do_check(self, context, result_map, ports):
        for port in ports:
            if not port.device_owner.startswith(
                    constants.DEVICE_OWNER_COMPUTE_PREFIX):
                return
            reports = result_map[port.id]['ra_speaker']
            port_name = utils.get_port_name_by_id(self.int_br, port.id)
            if not port_name:
                reports.add('port %s not found on bridge %s',
                            port.id, self.int_br.br_name)
                continue
            port_ofport = self.int_br.get_port_ofport(port_name)
            port_info = {"device_owner": port.device_owner,
                         "mac_address": str(port.mac_address),
                         "ofport": port_ofport}
            self.clear()
            self.prepare_flow(port_info)
            reports.extend(list(self.reports))
