<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 320 387" width="320pt" height="387pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:01:59 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><path d="M 36.346457 184.25197 L 232.94488 184.25197 C 237.36316 184.25197 240.94488 187.83369 240.94488 192.25197 L 240.94488 315.1496 C 240.94488 319.56788 237.36316 323.1496 232.94488 323.1496 L 36.346457 323.1496 C 31.928179 323.1496 28.346457 319.56788 28.346457 315.1496 L 28.346457 192.25197 C 28.346457 187.83369 31.928179 184.25197 36.346457 184.25197 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 189.25197)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="13.355853" y="13" textLength="118.52344">Physical Network Infr</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="131.639056" y="13" textLength="51.111328">astructur</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="182.51015" y="13" textLength="6.732422">e</tspan></text><text transform="translate(52.807085 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".71875" y="17" textLength="122.64844">Linux Bridge - Pr</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="123.046875" y="17" textLength="9.6640625">o</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="132.390625" y="17" textLength="112.890625">vider Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x=".33105469" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="57.28418" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="61.942383" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="79.80176" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="104.868164" y="35" textLength="140.80078">w - North/South Scenario</tspan></text><circle cx="155.90551" cy="354.3307" r="8.5039505" fill="#738a05"/><text transform="translate(169.40945 342.32283)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="72.700195">vider network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="94.91406">VLAN 101, 203.0.113.0/24</tspan></text><path d="M 36.346457 56.692913 L 317.98425 56.692913 C 322.40253 56.692913 325.98425 60.274635 325.98425 64.692913 L 325.98425 145.070865 C 325.98425 149.48914 322.40253 153.070865 317.98425 153.070865 L 36.346457 153.070865 C 31.928179 153.070865 28.346457 149.48914 28.346457 145.070865 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="101.23589" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 135.559054 212.59842 L 218.77165 212.59842 C 223.18993 212.59842 226.77165 216.18015 226.77165 220.59842 L 226.77165 309.48031 C 226.77165 313.8986 223.18993 317.48031 218.77165 317.48031 L 135.559054 317.48031 C 131.14078 317.48031 127.559054 313.8986 127.559054 309.48031 L 127.559054 220.59842 C 127.559054 216.18015 131.14078 212.59842 135.559054 212.59842 Z" fill="#fdf5dd"/><path d="M 135.559054 212.59842 L 218.77165 212.59842 C 223.18993 212.59842 226.77165 216.18015 226.77165 220.59842 L 226.77165 309.48031 C 226.77165 313.8986 223.18993 317.48031 218.77165 317.48031 L 135.559054 317.48031 C 131.14078 317.48031 127.559054 313.8986 127.559054 309.48031 L 127.559054 220.59842 C 127.559054 216.18015 131.14078 212.59842 135.559054 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 217.59842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="31.93247" y="9" textLength="25.347656">Switch</tspan></text></g><line x1="198.4252" y1="297.6378" x2="283.46457" y2="297.6378" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 50.519685 212.59842 L 91.2126 212.59842 C 95.630876 212.59842 99.2126 216.18015 99.2126 220.59842 L 99.2126 309.48031 C 99.2126 313.8986 95.630876 317.48031 91.2126 317.48031 L 50.519685 317.48031 C 46.101407 317.48031 42.519685 313.8986 42.519685 309.48031 L 42.519685 220.59842 C 42.519685 216.18015 46.101407 212.59842 50.519685 212.59842 Z" fill="#fdf5dd"/><path d="M 50.519685 212.59842 L 91.2126 212.59842 C 95.630876 212.59842 99.2126 216.18015 99.2126 220.59842 L 99.2126 309.48031 C 99.2126 313.8986 95.630876 317.48031 91.2126 317.48031 L 50.519685 317.48031 C 46.101407 317.48031 42.519685 313.8986 42.519685 309.48031 L 42.519685 220.59842 C 42.519685 216.18015 46.101407 212.59842 50.519685 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 217.59842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.2058315" y="9" textLength="26.28125">Router</tspan></text></g><path d="M 268.79578 299.16028 C 262.38189 297.6378 264.93959 284.82094 275.17119 287.00787 C 276.12045 282.74485 288.01842 283.43679 287.94064 287.00787 C 295.40103 282.4404 304.93497 291.54784 298.54012 296.1153 C 306.21362 298.32973 298.44329 310.26075 292.14567 308.26771 C 291.64167 311.58964 280.38342 312.75212 279.39526 308.26771 C 273.02026 313.05685 259.72736 305.69329 268.79578 299.16028 Z" fill="#fdf5dd"/><path d="M 268.79578 299.16028 C 262.38189 297.6378 264.93959 284.82094 275.17119 287.00787 C 276.12045 282.74485 288.01842 283.43679 287.94064 287.00787 C 295.40103 282.4404 304.93497 291.54784 298.54012 296.1153 C 306.21362 298.32973 298.44329 310.26075 292.14567 308.26771 C 291.64167 311.58964 280.38342 312.75212 279.39526 308.26771 C 273.02026 313.05685 259.72736 305.69329 268.79578 299.16028 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(274.5748 292.1378)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="1.788201" y="9" textLength="14.203125">(12)</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" fill="#fdf5dd"/><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="127.559054" x2="155.90551" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="127.559054" x2="198.4252" y2="127.559054" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="287.45218" y1="140.654845" x2="198.4252" y2="255.11811" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="198.4252" y1="127.559054" x2="240.94488" y2="127.559054" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#738a05"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" fill="#738a05"/><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" fill="#738a05"/><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><line x1="240.94488" y1="127.559054" x2="297.6378" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" fill="#738a05"/><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" fill="#bd3612"/><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><text transform="translate(250.13219 188.63769) rotate(-52.125016)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text><line x1="155.90551" y1="255.11811" x2="70.86614" y2="255.11811" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="297.6378" x2="155.90551" y2="297.6378" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 64.692913 240.94488 L 77.03937 240.94488 C 81.45765 240.94488 85.03937 244.5266 85.03937 248.94488 L 85.03937 261.29134 C 85.03937 265.70962 81.45765 269.29134 77.03937 269.29134 L 64.692913 269.29134 C 60.274635 269.29134 56.692913 265.70962 56.692913 261.29134 L 56.692913 248.94488 C 56.692913 244.5266 60.274635 240.94488 64.692913 240.94488 Z" fill="#738a05"/><path d="M 64.692913 240.94488 L 77.03937 240.94488 C 81.45765 240.94488 85.03937 244.5266 85.03937 248.94488 L 85.03937 261.29134 C 85.03937 265.70962 81.45765 269.29134 77.03937 269.29134 L 64.692913 269.29134 C 60.274635 269.29134 56.692913 265.70962 56.692913 261.29134 L 56.692913 248.94488 C 56.692913 244.5266 60.274635 240.94488 64.692913 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 249.61811)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 283.46457 L 77.03937 283.46457 C 81.45765 283.46457 85.03937 287.04629 85.03937 291.46457 L 85.03937 303.81102 C 85.03937 308.2293 81.45765 311.81102 77.03937 311.81102 L 64.692913 311.81102 C 60.274635 311.81102 56.692913 308.2293 56.692913 303.81102 L 56.692913 291.46457 C 56.692913 287.04629 60.274635 283.46457 64.692913 283.46457 Z" fill="#fdf5dd"/><path d="M 64.692913 283.46457 L 77.03937 283.46457 C 81.45765 283.46457 85.03937 287.04629 85.03937 291.46457 L 85.03937 303.81102 C 85.03937 308.2293 81.45765 311.81102 77.03937 311.81102 L 64.692913 311.81102 C 60.274635 311.81102 56.692913 308.2293 56.692913 303.81102 L 56.692913 291.46457 C 56.692913 287.04629 60.274635 283.46457 64.692913 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 292.1378)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><line x1="198.4252" y1="255.11811" x2="155.90551" y2="255.11811" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="297.6378" x2="155.90551" y2="297.6378" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 192.25197 283.46457 L 204.59842 283.46457 C 209.0167 283.46457 212.59842 287.04629 212.59842 291.46457 L 212.59842 303.81102 C 212.59842 308.2293 209.0167 311.81102 204.59842 311.81102 L 192.25197 311.81102 C 187.83369 311.81102 184.25197 308.2293 184.25197 303.81102 L 184.25197 291.46457 C 184.25197 287.04629 187.83369 283.46457 192.25197 283.46457 Z" fill="#fdf5dd"/><path d="M 192.25197 283.46457 L 204.59842 283.46457 C 209.0167 283.46457 212.59842 287.04629 212.59842 291.46457 L 212.59842 303.81102 C 212.59842 308.2293 209.0167 311.81102 204.59842 311.81102 L 192.25197 311.81102 C 187.83369 311.81102 184.25197 308.2293 184.25197 303.81102 L 184.25197 291.46457 C 184.25197 287.04629 187.83369 283.46457 192.25197 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 292.1378)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 240.94488 L 204.59842 240.94488 C 209.0167 240.94488 212.59842 244.5266 212.59842 248.94488 L 212.59842 261.29134 C 212.59842 265.70962 209.0167 269.29134 204.59842 269.29134 L 192.25197 269.29134 C 187.83369 269.29134 184.25197 265.70962 184.25197 261.29134 L 184.25197 248.94488 C 184.25197 244.5266 187.83369 240.94488 192.25197 240.94488 Z" fill="#bd3612"/><path d="M 192.25197 240.94488 L 204.59842 240.94488 C 209.0167 240.94488 212.59842 244.5266 212.59842 248.94488 L 212.59842 261.29134 C 212.59842 265.70962 209.0167 269.29134 204.59842 269.29134 L 192.25197 269.29134 C 187.83369 269.29134 184.25197 265.70962 184.25197 261.29134 L 184.25197 248.94488 C 184.25197 244.5266 187.83369 240.94488 192.25197 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 249.61811)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 240.94488 L 162.07874 240.94488 C 166.49702 240.94488 170.07874 244.5266 170.07874 248.94488 L 170.07874 261.29134 C 170.07874 265.70962 166.49702 269.29134 162.07874 269.29134 L 149.73228 269.29134 C 145.314005 269.29134 141.73228 265.70962 141.73228 261.29134 L 141.73228 248.94488 C 141.73228 244.5266 145.314005 240.94488 149.73228 240.94488 Z" fill="#738a05"/><path d="M 149.73228 240.94488 L 162.07874 240.94488 C 166.49702 240.94488 170.07874 244.5266 170.07874 248.94488 L 170.07874 261.29134 C 170.07874 265.70962 166.49702 269.29134 162.07874 269.29134 L 149.73228 269.29134 C 145.314005 269.29134 141.73228 265.70962 141.73228 261.29134 L 141.73228 248.94488 C 141.73228 244.5266 145.314005 240.94488 149.73228 240.94488 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 249.61811)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 283.46457 L 162.07874 283.46457 C 166.49702 283.46457 170.07874 287.04629 170.07874 291.46457 L 170.07874 303.81102 C 170.07874 308.2293 166.49702 311.81102 162.07874 311.81102 L 149.73228 311.81102 C 145.314005 311.81102 141.73228 308.2293 141.73228 303.81102 L 141.73228 291.46457 C 141.73228 287.04629 145.314005 283.46457 149.73228 283.46457 Z" fill="#fdf5dd"/><path d="M 149.73228 283.46457 L 162.07874 283.46457 C 166.49702 283.46457 170.07874 287.04629 170.07874 291.46457 L 170.07874 303.81102 C 170.07874 308.2293 166.49702 311.81102 162.07874 311.81102 L 149.73228 311.81102 C 145.314005 311.81102 141.73228 308.2293 141.73228 303.81102 L 141.73228 291.46457 C 141.73228 287.04629 145.314005 283.46457 149.73228 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 292.1378)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.0716658" y="9" textLength="14.203125">(10)</tspan></text></g><circle cx="42.519685" cy="354.3307" r="8.5039505" fill="#bd3612"/><text transform="translate(56.023622 342.32283)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text></g></g></svg>
