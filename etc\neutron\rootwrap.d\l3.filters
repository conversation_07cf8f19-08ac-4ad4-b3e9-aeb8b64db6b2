# neutron-rootwrap command filters for nodes on which neutron is
# expected to control network
#
# This file should be owned by (and only-writeable by) the root user

# format seems to be
# cmd-name: filter-name, raw-command, user, args

[Filters]

# arping
arping: CommandFilter, arping, root

# l3_agent
sysctl: CommandFilter, sysctl, root
route: CommandFilter, route, root
radvd: CommandFilter, radvd, root

# haproxy
haproxy: RegExpFilter, haproxy, root, haproxy, -f, .*
kill_haproxy: KillFilter, root, haproxy, -15, -9, -HUP
# RHEL invocation of the metadata proxy will report /usr/bin/python
# TODO(da<PERSON><PERSON>ez): Remove kill_metadata* filters in Q release since
# neutron-ns-metadata-proxy is now replaced by haproxy. We keep them for now
# for the migration process
kill_metadata: KillFilter, root, python, -15, -9
kill_metadata7: <PERSON><PERSON><PERSON><PERSON>, root, python2.7, -15, -9
kill_metadata35: <PERSON><PERSON><PERSON><PERSON>, root, python3.5, -15, -9
kill_radvd_usr: <PERSON><PERSON><PERSON><PERSON>, root, /usr/sbin/radvd, -15, -9, -<PERSON><PERSON>
kill_radvd: KillFilter, root, /sbin/radvd, -15, -9, -HUP

# ip_lib
ip: IpFilter, ip, root
find: RegExpFilter, find, root, find, /sys/class/net, -maxdepth, 1, -type, l, -printf, %.*
ip_exec: IpNetnsExecFilter, ip, root

# l3_tc_lib
l3_tc_show_qdisc: RegExpFilter, tc, root, tc, qdisc, show, dev, .+
l3_tc_add_qdisc_ingress: RegExpFilter, tc, root, tc, qdisc, add, dev, .+, ingress
l3_tc_add_qdisc_egress: RegExpFilter, tc, root, tc, qdisc, add, dev, .+, root, handle, 1:, htb
l3_tc_show_filters: RegExpFilter, tc, root, tc, -p, -s, -d, filter, show, dev, .+, parent, .+, prio, 1
l3_tc_show_v6_filters: RegExpFilter, tc, root, tc, -s, -d, filter, show, dev, .+, parent, .+, prio, 1
l3_tc_delete_filters: RegExpFilter, tc, root, tc, filter, del, dev, .+, parent, .+, prio, 1, handle, .+, u32
l3_tc_add_filter_ingress: RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, ip, prio, 1, u32, match, ip, dst, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_filter_egress:  RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, ip, prio, 1, u32, match, ip, src, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_v6_filter_ingress: RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, ipv6, prio, 1, u32, match, ip6, dst, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_v6_filter_egress:  RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, ipv6, prio, 1, u32, match, ip6, src, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_v6_all_prot_filter_ingress: RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, ip6, dst, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_v6_all_prot_filter_egress:  RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, ip6, src, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_v4_all_prot_filter_ingress: RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, ip, dst, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_v4_all_prot_filter_egress:  RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, ip, src, .+, police, rate, .+, burst, .+, mtu, 64kb, drop, flowid, :1
l3_tc_add_class_egress: RegExpFilter, tc, root, tc, class, replace, dev, .+, parent, .+, classid, .+, htb, rate, .+, burst, .+, mtu, 65535
l3_tc_show_class_egress: RegExpFilter, tc, root, tc, class, show, dev, .+, parent, .+
l3_tc_delete_class_egress: RegExpFilter, tc, root, tc, class, del, dev, .+, classid, .+
l3_tc_add_all_prot_filter_egress:  RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, .+, src, .+, flowid, .+

l3_fip_shared_mirror_add: RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, u32, 0, 0, action, mirred, egress, redirect, dev, .+, flowid, :1
l3_fip_shared_class_add: RegExpFilter, tc, root, tc, class, replace, dev, .+, parent, .+, classid, .+, htb, rate, .+, ceil, .+, burst, .+
l3_fip_shared_class_del:RegExpFilter, tc, root, tc, class, del, dev, .+, parent, .+, classid, .+
l3_tc_add_all_prot_filter_ingress:  RegExpFilter, tc, root, tc, filter, add, dev, .+, parent, .+, protocol, all, prio, 1, u32, match, .+, dst, .+, flowid, .+

# For ip monitor
kill_ip_monitor: KillFilter, root, ip, -9

# ovs_lib (if OVSInterfaceDriver is used)
ovs-vsctl: CommandFilter, ovs-vsctl, root
ovs-ofctl: CommandFilter, ovs-ofctl, root

# iptables_manager
iptables-save: CommandFilter, iptables-save, root
iptables-restore: CommandFilter, iptables-restore, root
ip6tables-save: CommandFilter, ip6tables-save, root
ip6tables-restore: CommandFilter, ip6tables-restore, root

# Keepalived
keepalived: CommandFilter, keepalived, root
kill_keepalived: KillFilter, root, keepalived, -HUP, -15, -9

# l3 agent to delete floatingip's conntrack state
conntrack: CommandFilter, conntrack, root

# keepalived state change monitor
keepalived_state_change: CommandFilter, neutron-keepalived-state-change, root
# The following filters are used to kill the keepalived state change monitor.
# Since the monitor runs as a Python script, the system reports that the
# command of the process to be killed is python.
# TODO(mlavalle) These kill filters will be updated once we come up with a
# mechanism to kill using the name of the script being executed by Python
kill_keepalived_monitor_py: KillFilter, root, python, -15
kill_keepalived_monitor_py27: KillFilter, root, python2.7, -15
kill_keepalived_monitor_py3: KillFilter, root, python3, -15
kill_keepalived_monitor_py35: KillFilter, root, python3.5, -15
kill_keepalived_monitor_py36: KillFilter, root, python3.6, -15
kill_keepalived_monitor_py37: KillFilter, root, python3.7, -15

# This is needed because we should ping
# from inside a namespace.
ping: CommandFilter, ping, root
ping6: CommandFilter, ping6, root
fping: CommandFilter, fping, root
