#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib.agent import topics
from neutron_lib import constants
from neutron_lib import context
from neutron_lib.plugins import utils as p_utils
from neutron_lib.utils import helpers
from oslo_config import cfg
from oslo_log import log as logging


from neutron._i18n import _
from neutron.agent.common import ovs_lib
from neutron.agent import rpc as agent_rpc
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const


LOG = logging.getLogger(__name__)


class HostInfos(agent_rpc.CacheBackedPluginApi):

    def __init__(self, conf, topic=topics.PLUGIN):
        super(HostInfos, self).__init__(topic=topic)

        self.conf = conf or cfg.CONF

        self.init_bridge_mappings()
        self.context = context.get_admin_context()
        self.init_privatefloating_info()

    def init_bridge_mappings(self):
        self.bridge_mappings = self._parse_bridge_mappings(
            self.conf.OVS.bridge_mappings)

    def _parse_bridge_mappings(self, bridge_mappings):
        try:
            return helpers.parse_mappings(bridge_mappings, unique_values=False)
        except ValueError as e:
            raise ValueError(_("Parsing bridge_mappings failed: %s.") % e)

    def init_privatefloating_info(self):
        self.agent_id = 'ovs-agent-%s' % cfg.CONF.host
        self.privatefloating_info = self.get_privatefloating_info(
            self.context, agent_id=self.agent_id, host=cfg.CONF.host)
        self.enable_private_floating = False
        if self.privatefloating_info:
            self.enable_private_floating = \
                self.privatefloating_info.get('privatefloating_enable')
        LOG.info("Get private floating info: %s", self.privatefloating_info)

        self.arp_timeout = self.privatefloating_info.get('arp_timeout', 300)

        self.pfn_segmentation_id = (
            self.privatefloating_info.get(
                'privatefloating_network', {}).get('provider:segmentation_id'))

        pfn_v6_net = self.privatefloating_info.get(
            'privatefloating_network_v6', {})
        if pfn_v6_net:
            self.pfn_segmentation_id_v6 = (
                pfn_v6_net.get('provider:segmentation_id'))
        else:
            self.pfn_segmentation_id_v6 = self.pfn_segmentation_id

        self.privatefloating_network = self.privatefloating_info.get(
            'privatefloating_network', {})
        self.privatefloating_port = self.privatefloating_info.get(
            'privatefloating_port', {})

        self.physnet = self.privatefloating_network.get(
            'provider:physical_network')
        self.phy_br = ovs_lib.OVSBridge(self.bridge_mappings.get(self.physnet))
        if cfg.CONF.SERVICEPATH.path_physical_dev:
            self.phy_of_port = self.phy_br.get_port_ofport(
                cfg.CONF.SERVICEPATH.path_physical_dev)
        else:
            self.phy_of_port = 0

        self.physical_bridge = self.bridge_mappings.get(self.physnet)
        phys_if_name = p_utils.get_interface_name(
            self.physical_bridge, prefix=p_const.PEER_PHYSICAL_PREFIX)
        self.ofport_phy_to_int = self.phy_br.get_port_ofport(phys_if_name)

        for ip in self.privatefloating_port['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_4:
                self.privatefloating_ip = ip['ip_address']
                break

        self.privatefloating_mac = (
            self.privatefloating_port.get('mac_address'))

        for ip in self.privatefloating_port['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_6:
                self.privatefloating_ip_6 = ip['ip_address']
                break

        self.subnet_routes = {}
        self.route_destinations = set()
        self.route_nexthops = set()
        for sub in self.privatefloating_network.get('subnets_detail', []):
            routes = set()
            for route in sub['host_routes']:
                routes.add((str(route['nexthop']),
                            str(route['destination'])))
                self.route_destinations.add(str(route['destination']))
                self.route_nexthops.add(str(route['nexthop']))
            self.subnet_routes[sub['id']] = (routes, sub['cidr'])

    def get_provider_ip(self, port_obj):
        fixed_ips = []
        pvf_subnets = self.privatefloating_network.get('subnets', [])
        for ip in port_obj.fixed_ips:
            if ip['subnet_id'] in pvf_subnets:
                fixed_ips.append((str(ip.ip_address), ip.subnet_id))
        return fixed_ips


def dump_and_check_flows(br, match=None, action=None, reports=None, **kwargs):
    flows = br.dump_flows_for(**kwargs)
    list_flows = flows.strip().split('\n')
    table = kwargs.pop('table', None)
    if (len(list_flows) == 1 and '' not in list_flows and
            ('priority=20' in list_flows[0] or match in list_flows[0])):
        return
    elif len(list_flows) > 1:
        if match and action:
            for flow in list_flows:
                if match in flow and action in flow:
                    return
    msg = 'No flow: %s table=%s' % (br.br_name, table)
    msg += ', %s,' % match if match else ', '
    msg += ','.join(['%s=%s' % (k, v) for k, v in kwargs.items()])
    msg += ' actions=%s' % action if action else ''
    reports.add(msg)


def get_fixed_ip(port_info, pvf_subnets):
    for ip in port_info['fixed_ips']:
        ip_addr = netaddr.IPAddress(ip['ip_address'])
        if ip_addr.version == 4 and ip['subnet_id'] not in pvf_subnets:
            return ip['ip_address']


def check_init_pfn_flow(br, enable_firewall=False, reports=None):
    action = ("resubmit(,%d)" % (
        p_const.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
              if cfg.CONF.AGENT.explicitly_egress_direct else "NORMAL")
    # check arp responser
    dump_and_check_flows(br, table=p_const.PFN_ARP_RESPONSER_TABLE,
                         match="priority=0,arp", action=action,
                         reports=reports)
    # private floating egress process
    dump_and_check_flows(br, table=p_const.PFN_BASE_EGRESS_TABLE,
                         match="priority=0", action=action,
                         reports=reports)
    # flow for route
    dump_and_check_flows(br, table=p_const.PFN_RULES_ROUTE_EGRESS_TABLE,
                         match="priority=0", action=action,
                         reports=reports)

    # flow for egress traffic
    dump_and_check_flows(br, table=p_const.PFN_EGRESS_TRAFFIC_TABLE,
                         match="priority=0", action=action,
                         reports=reports)
    # flow for ingress traffic
    dump_and_check_flows(br, table=p_const.PFN_INGRESS_TRAFFIC_TABLE,
                         match="priority=0", action=action,
                         reports=reports)
    # flow for private floating ingress process
    dump_and_check_flows(
        br, table=p_const.LOCAL_SWITCHING,
        match="priority=1",
        action="resubmit(,%d)" % p_const.PFN_BASE_INGRESS_TABLE,
        reports=reports)
    dump_and_check_flows(br, table=p_const.PFN_BASE_INGRESS_TABLE,
                         match="priority=0",
                         action="resubmit(,%d)" % p_const.DVR_PRE_QOS_TABLE,
                         reports=reports)
    if enable_firewall:
        dump_and_check_flows(
            br, table=p_const.ACCEPTED_EGRESS_TRAFFIC_TABLE,
            match="priority=5",
            action="resubmit(,%d)" % p_const.PFN_BASE_EGRESS_TABLE,
            reports=reports)
        if not cfg.CONF.AGENT.explicitly_egress_direct:
            dump_and_check_flows(
                br, table=p_const.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
                match="priority=5",
                action="resubmit(,%d)" % p_const.PFN_BASE_EGRESS_TABLE,
                reports=reports)
    else:
        dump_and_check_flows(
            br, table=p_const.TRANSIENT_TABLE,
            match="priority=5",
            action="resubmit(,%d)" % p_const.PFN_BASE_EGRESS_TABLE,
            reports=reports)


def check_pf_port_flow(host_info, br_int, pfn_port,
                       ofport_int_to_phy, reports=None):
    # Egress flows
    dump_and_check_flows(
        br_int, table=p_const.PFN_EGRESS_TRAFFIC_TABLE,
        match="priority=5,ip,dl_vlan=%s" % pfn_port['port_tag'],
        action="strip_vlan,mod_dl_dst:%s,output:%d" %
               (host_info.privatefloating_mac, pfn_port['port_ofport']),
        reports=reports)
    # check route table
    for routes, _subnet_id in host_info.subnet_routes.values():
        for nexthop, _dest in routes:
            ip_addr = netaddr.IPNetwork(_dest)
            if ip_addr.version == constants.IP_VERSION_6:
                continue
            nexthop_value = netaddr.IPAddress(nexthop).value
            dump_and_check_flows(
                br_int, table=p_const.PFN_RULES_ROUTE_EGRESS_TABLE,
                match="priority=20,ip", nw_dst=_dest,
                in_port=ofport_int_to_phy,
                action="load:%s->NXM_NX_REG2[], resubmit(,%d)" % (
                    "0x{:04x}".format(nexthop_value),
                    p_const.PFN_RULES_EGRESS_TABLE),
                reports=reports)
            dump_and_check_flows(
                br_int, table=p_const.PFN_RULES_ROUTE_EGRESS_TABLE,
                match="priority=10,ip", nw_dst=_dest,
                action="load:%s->NXM_NX_REG2[],mod_vlan_vid:%d,"
                       "resubmit(,%d)" % ("0x{:04x}".format(nexthop_value),
                    pfn_port['port_tag'], p_const.PFN_RULES_EGRESS_TABLE),
                reports=reports)

            action = ("resubmit(,%d)" % (
                p_const.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
                      if cfg.CONF.AGENT.explicitly_egress_direct else "NORMAL")
            dump_and_check_flows(
                br_int, table=p_const.PFN_ARP_RESPONSER_TABLE,
                match="priority=20,arp", in_port=ofport_int_to_phy,
                arp_spa=nexthop, arp_op=2,
                action="learn(table=%d,hard_timeout=%d,"
                       "fin_idle_timeout=60,fin_hard_timeout=%d,priority=10,"
                       "reg2=%s,load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                       "load:0x%s->NXM_OF_ETH_SRC[],"
                       "load:%s->OXM_OF_VLAN_VID[],"
                       "load:%s->NXM_OF_IN_PORT[],"
                       "output:NXM_OF_IN_PORT[]),%s" %
                       (p_const.PFN_EGRESS_TRAFFIC_TABLE,
                        host_info.arp_timeout,
                        host_info.arp_timeout,
                        "0x{:04x}".format(nexthop_value),
                        host_info.privatefloating_mac.replace(':', ''),
                        "0x{:x}".format(pfn_port['port_tag']),
                        "0x{:x}".format(pfn_port['port_ofport']),
                        action),
                reports=reports)
            dump_and_check_flows(
                br_int, table=p_const.PFN_ARP_RESPONSER_TABLE,
                match="priority=10,arp", arp_op=2,
                arp_spa=nexthop,
                action="learn(table=%d,hard_timeout=%d,"
                       "fin_idle_timeout=60,fin_hard_timeout=%d,priority=10,"
                       "reg2=%s,load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                       "load:0x%s->NXM_OF_ETH_SRC[],"
                       "load:%s->OXM_OF_VLAN_VID[],"
                       "load:%s->NXM_OF_IN_PORT[],"
                       "output:NXM_OF_IN_PORT[]),%s" %
                       (p_const.PFN_EGRESS_TRAFFIC_TABLE,
                        host_info.arp_timeout,
                        host_info.arp_timeout,
                        "0x{:04x}".format(nexthop_value),
                        host_info.privatefloating_mac.replace(':', ''),
                        "0x{:x}".format(pfn_port['port_tag']),
                        "0x{:x}".format(ofport_int_to_phy),
                        action),
                reports=reports)

    # Ingress flows
    dump_and_check_flows(
        br_int, table=p_const.PFN_RULES_INGRESS_TABLE,
        match="priority=0,ip", dl_vlan=host_info.pfn_segmentation_id,
        action="mod_vlan_vid:%d,NORMAL" % pfn_port['port_tag'],
        reports=reports)

    # Cross-node case
    # Ingress entrance, all ingress traffic after privatefloating handle
    dump_and_check_flows(
        br_int, table=p_const.LOCAL_SWITCHING,
        match="priority=30,arp", in_port=ofport_int_to_phy,
        dl_vlan=host_info.pfn_segmentation_id,
        action="mod_vlan_vid:%d,resubmit(,%d)" % (
            pfn_port['port_tag'], p_const.PFN_BASE_INGRESS_TABLE),
        reports=reports)
    dump_and_check_flows(
        br_int, table=p_const.LOCAL_SWITCHING,
        match="priority=30,ip", in_port=ofport_int_to_phy,
        dl_vlan=host_info.pfn_segmentation_id,
        action="resubmit(,%d)" % p_const.PFN_BASE_INGRESS_TABLE,
        reports=reports)
    dump_and_check_flows(
        br_int, table=p_const.PFN_BASE_INGRESS_TABLE,
        match="priority=10,arp", in_port=ofport_int_to_phy,
        action="resubmit(,%d)" % p_const.PFN_ARP_RESPONSER_TABLE,
        reports=reports)
    if cfg.CONF.AGENT.explicitly_egress_direct:
        dump_and_check_flows(
            br_int, table=p_const.PFN_BASE_INGRESS_TABLE,
            match="priority=10,arp", in_port=pfn_port['port_ofport'],
            action="resubmit(,%d)" % p_const.PFN_ARP_RESPONSER_TABLE,
            reports=reports)
    dump_and_check_flows(
        br_int, table=p_const.PFN_BASE_INGRESS_TABLE,
        match="priority=5", in_port=ofport_int_to_phy,
        action="resubmit(,%d)" % p_const.PFN_RULES_INGRESS_TABLE,
        reports=reports)


def check_npf_port_flow(host_info, port_info, br_int, port_tag, port_ofport,
                        ofport_int_to_phy, enable_firewall=False,
                        enable_ovs_stateless_fw=False,
                        reports=None):
    if port_info['device_owner'] == constants.DEVICE_OWNER_PRIVATEFLOATING:
        return
    # egress traffic path
    dump_and_check_flows(
        br_int, table=p_const.PFN_BASE_EGRESS_TABLE,
        match="priority=5", in_port=port_ofport,
        action="resubmit(,%d)" % p_const.PFN_RULES_ROUTE_EGRESS_TABLE,
        reports=reports)
    pvf_subnets = host_info.privatefloating_network.get('subnets', [])
    port_fixed_ip = get_fixed_ip(port_info, pvf_subnets)
    if not port_fixed_ip:
        reports.add('device port %s does not have fixed IP', port_info['id'])
        return
    for privatefloating_ip, _subnet_id in host_info.get_provider_ip(port_info):
        # egress traffic path, translate fixed-ip to privatefloating-ip
        dump_and_check_flows(br_int, table=p_const.PFN_RULES_EGRESS_TABLE,
                             match="priority=20,ip", in_port=port_ofport,
                             nw_src=port_fixed_ip,
                             action="mod_nw_src:%s,resubmit(,%d)" %
                                    (privatefloating_ip,
                                     p_const.PFN_EGRESS_TRAFFIC_TABLE),
                             reports=reports)
        if enable_firewall:
            # ingress traffic path, translate privatefloating-ip to fixed-ip
            dump_and_check_flows(
                br_int, table=p_const.PFN_RULES_INGRESS_TABLE,
                match="priority=20,ip", nw_dst=privatefloating_ip,
                action="mod_nw_dst:%s,mod_dl_src:%s,"
                       "mod_dl_dst:%s,resubmit(,%d)" %
                       (port_fixed_ip,
                        host_info.privatefloating_mac,
                        port_info['mac_address'],
                        p_const.PFN_INGRESS_TRAFFIC_TABLE),
                reports=reports)
            # Redirect to firewall ingress entrace
            dump_and_check_flows(
                br_int, table=p_const.PFN_INGRESS_TRAFFIC_TABLE,
                match="priority=20", dl_dst=port_info['mac_address'],
                dl_vlan=host_info.pfn_segmentation_id,
                action="load:%s->NXM_NX_REG5[],"
                       "load:%s->NXM_NX_REG6[],strip_vlan,"
                       "resubmit(,%d)" %
                       ("0x{:x}".format(port_ofport),
                        "0x{:x}".format(port_tag),
                        p_const.BASE_INGRESS_TABLE),
                reports=reports)
            if (not port_info.security.get('port_security_enabled', False) and
                    not port_info.get('security_groups', [])):
                dump_and_check_flows(br_int, table=p_const.BASE_INGRESS_TABLE,
                                     match="priority=1",
                                     dl_dst=port_info['mac_address'],
                                     reg6="0x{:x}".format(port_tag),
                                     action="output:%d" % port_ofport,
                                     reports=reports)
        else:
            # ingress traffic path, translate privatefloating-ip to fixed-ip
            dump_and_check_flows(br_int, table=p_const.PFN_RULES_INGRESS_TABLE,
                                 match="priority=20,ip",
                                 nw_dst=privatefloating_ip,
                                 dl_vlan=host_info.pfn_segmentation_id,
                                 action="mod_nw_dst:%s,mod_dl_src:%s,"
                                        "mod_dl_dst:%s,"
                                        "strip_vlan,output:%d" %
                                        (port_fixed_ip,
                                         host_info.privatefloating_mac,
                                         port_info['mac_address'],
                                         port_ofport),
                                 reports=reports)
        if enable_ovs_stateless_fw:
            for routes, _subnet_id in host_info.subnet_routes.values():
                for nexthop, _dest in routes:
                    dest_net = netaddr.IPNetwork(_dest)
                    if dest_net.version == constants.IP_VERSION_6:
                        continue
                    dump_and_check_flows(
                        br_int, table=p_const.BASE_INGRESS_TABLE,
                        match="priority=200,ip", dl_vlan=port_tag,
                        nw_dst=_dest,
                        action="strip_vlan,resubmit(,%d)" %
                               (p_const.PFN_BASE_EGRESS_TABLE),
                        reports=reports)
                    if (not port_info.security.get(
                            'port_security_enabled', False) and
                            not port_info.get('security_groups', [])):
                        direct_t = p_const.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
                        uni_cast = "00:00:00:00:00:00/01:00:00:00:00:00"
                        reg_net = "0x{:x}".format(port_tag)
                        dump_and_check_flows(
                            br_int, table=direct_t,
                            match="priority=11,ip",
                            dl_src=port_info['mac_address'],
                            nw_dst=_dest, dl_dst=uni_cast,
                            reg6=reg_net,
                            action="resubmit(,%d)" %
                                   p_const.PFN_BASE_EGRESS_TABLE,
                            reports=reports)
        # Cross-node case(router nexthop in annother node)
        # [ARP] arp request packet(VM-privatefloating_ip)
        # from patch port response pfip namespace MAC
        dump_and_check_flows(
            br_int, table=p_const.PFN_ARP_RESPONSER_TABLE,
            match="priority=20,arp", arp_op=1,
            in_port=ofport_int_to_phy,
            arp_tpa=privatefloating_ip,
            action="move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                   "mod_dl_src:%s,"
                   "load:0x2->NXM_OF_ARP_OP[],"
                   "move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],"
                   "move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],"
                   "load:0x%s->NXM_NX_ARP_SHA[],"
                   "load:%s->NXM_OF_ARP_SPA[],IN_PORT" %
                   (host_info.privatefloating_mac,
                    host_info.privatefloating_mac.replace(':', ''),
                    "0x{:04x}".format(
                        netaddr.IPAddress(privatefloating_ip).value)),
            reports=reports)
        # Inter-node case(router nexthop in this node)
        # [ARP] arp request packet(VM-privatefloating_ip)
        # in integrate bridge response port MAC
        dump_and_check_flows(
            br_int, table=p_const.PFN_ARP_RESPONSER_TABLE,
            match="priority=10,arp", arp_op=1,
            action="move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                   "mod_dl_src:%s,"
                   "load:0x2->NXM_OF_ARP_OP[],"
                   "move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],"
                   "move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],"
                   "load:0x%s->NXM_NX_ARP_SHA[],"
                   "load:%s->NXM_OF_ARP_SPA[],IN_PORT" %
                   (port_info['mac_address'],
                    str(port_info['mac_address']).replace(':', ''),
                    "0x{:04x}".format(
                        netaddr.IPAddress(privatefloating_ip).value)),
            reports=reports)


def check_pfn_ports_flow(host_info, port_info, br_int, port, pfn_port,
                         enable_firewall, enable_ovs_stateless_fw, reports):
    int_if_name = p_utils.get_interface_name(
        host_info.physical_bridge, prefix=p_const.PEER_INTEGRATION_PREFIX)
    ofport_int_to_phy = br_int.get_port_ofport(int_if_name)
    check_pf_port_flow(host_info, br_int, pfn_port,
                       ofport_int_to_phy, reports)
    check_npf_port_flow(host_info, port_info, br_int,
                        port['port_tag'], port['port_ofport'],
                        ofport_int_to_phy, enable_firewall,
                        enable_ovs_stateless_fw, reports)


def check_private_floating_flows(host_info, port_info, br_int,
                                 port, pfn_port, reports):
    OVS_DRIVERS = [
        'openvswitch',
        'neutron.agent.linux.openvswitch_firewall:OVSFirewallDriver'
    ]
    enable_firewall = False
    if cfg.CONF.SECURITYGROUP.firewall_driver in OVS_DRIVERS:
        enable_firewall = True

    OVS_DRIVERS = [
        'openvswitch_stateless',
        'neutron.agent.linux.openvswitch_firewall:'
        'OVSStatelessFirewallDriver',
    ]
    enable_ovs_stateless_firewall = False
    if cfg.CONF.SECURITYGROUP.firewall_driver in OVS_DRIVERS:
        enable_ovs_stateless_firewall = True

    check_init_pfn_flow(br_int, enable_firewall, reports)
    check_pfn_ports_flow(host_info, port_info, br_int,
                         port, pfn_port, enable_firewall,
                         enable_ovs_stateless_firewall, reports)
