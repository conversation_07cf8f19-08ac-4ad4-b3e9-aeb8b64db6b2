#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib import constants
from neutron_lib import context as n_context
from neutron_lib.plugins import utils as p_utils
from oslo_config import cfg
from ryu.lib.packet import icmpv6

from neutron.agent.common import utils
from neutron.agent.l2.extensions.nat import service_datapath
from neutron.agent.l2.extensions.port_check.flow_checker import base
from neutron.common import constants as comm_consts
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const


class ARPProcesser(service_datapath.ARPProcessor):
    def send_arp_normal(self, datapath, src_mac, src_ip, dest_ip, arp_op,
                        vlan_id=None, nic_ofport=None, dst_mac=None):
        pass

    def send_ns_normal(self, datapath, src_mac, src_ip, dest_ip,
                       vlan_id=None, nic_ofport=None):
        pass


class ServiceDatapathFlowCheck(base.ExtensionFlowCheckBase,
                               service_datapath.ServiceDataPathAgentExtension):
    def __init__(self, br_int, phy_br, agent_api, *args, **kwargs):
        super(ServiceDatapathFlowCheck, self).__init__(br_int)
        self.phy_br = phy_br
        self.set_path_br(self.phy_br)
        self.consume_api(agent_api)
        self.ext_api = service_datapath.NetworkingPathExtensionPortInfoAPI(
            self.rcache_api)
        self.provider_arp = ARPProcesser(self)
        self.init_privatefloating_info()

    @property
    def reports(self):
        return set(self.phy_br.reports + self.int_br.reports)

    def clear(self):
        del self.phy_br.reports[:]
        del self.int_br.reports[:]

    def send_ip6_na(self, src_mac, src_ip):
        pass

    def send_garp(self, src_mac, src_ip):
        pass

    def dump_and_clean_stale_flows(self):
        pass

    def install_ndp_responder(self, vlan_id, dest_ip, mac):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        table = p_const.LOCAL_SWITCHING
        match = ofpp.OFPMatch(ip_proto=constants.PROTO_NUM_IPV6_ICMP,
                              icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT,
                              eth_type=comm_consts.ETHERTYPE_IPV6,
                              ipv6_nd_target=dest_ip,
                              vlan_vid=vlan_id | ofp.OFPVID_PRESENT)
        actions = [
            ofpp.OFPActionSetField(icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT),
            ofpp.NXActionResubmitTable(
                table_id=p_const.NP_PROVIDER_IP_ARP_RESPONDER)
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(table_id=table,
                                          priority=205,
                                          match=match,
                                          instructions=instructions)

        table = p_const.NP_PROVIDER_IP_ARP_RESPONDER
        match = ofpp.OFPMatch(ip_proto=constants.PROTO_NUM_IPV6_ICMP,
                              icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT,
                              eth_type=comm_consts.ETHERTYPE_IPV6,
                              ipv6_nd_target=dest_ip,
                              vlan_vid=vlan_id | ofp.OFPVID_PRESENT)
        actions = [
            ofpp.NXActionRegMove(dst_field='eth_dst_nxm',
                                 src_field='eth_src_nxm',
                                 n_bits=48),
            ofpp.OFPActionSetField(eth_src=mac),
            ofpp.NXActionRegMove(dst_field='ipv6_dst_nxm',
                                 src_field='ipv6_src_nxm',
                                 n_bits=128),
            ofpp.OFPActionSetField(ipv6_src=dest_ip),
            ofpp.OFPActionSetField(icmpv6_code=0),
            ofpp.OFPActionSetField(field_524289='YAAAAA=='),
            ofpp.OFPActionSetField(ipv6_nd_tll=mac),
            ofpp.OFPActionSetField(field_524290='Ag=='),
            ofpp.OFPActionOutput(port=ofp.OFPP_IN_PORT)
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(table_id=table,
                                          priority=201,
                                          match=match,
                                          instructions=instructions)

    def get_provider_ip(self, port):
        fixed_ips = []
        pvf_subnets = self.privatefloating_network.get('subnets', [])
        for ip in port['fixed_ips']:
            if ip['subnet_id'] in pvf_subnets:
                fixed_ips.append((ip['ip_address'], ip['subnet_id']))
        return fixed_ips

    def init_privatefloating_info(self):
        self.context = n_context.get_admin_context_without_session()

        self.agent_id = 'ovs-agent-%s' % cfg.CONF.host
        self.privatefloating_info = self.plugin_rpc.get_privatefloating_info(
            self.context, agent_id=self.agent_id, host=cfg.CONF.host)
        self.enable_private_floating = False
        if self.privatefloating_info:
            self.enable_private_floating = \
                self.privatefloating_info.get('privatefloating_enable')
        self.privatefloating_network = self.privatefloating_info.get(
            'privatefloating_network', {})
        self.pfn_segmentation_id = self.privatefloating_network.get(
            'provider:segmentation_id')
        pfn_v6_net = self.privatefloating_info.get(
            'privatefloating_network_v6', {})
        if pfn_v6_net:
            self.pfn_segmentation_id_v6 = (
                pfn_v6_net.get('provider:segmentation_id'))
        else:
            self.pfn_segmentation_id_v6 = self.pfn_segmentation_id
        self.privatefloating_port = self.privatefloating_info.get(
            'privatefloating_port', {})

        self.physnet = self.privatefloating_network.get(
            'provider:physical_network')
        self.privatefloating_mac = (
            self.privatefloating_port.get('mac_address'))

        for ip in self.privatefloating_port['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_4:
                self.privatefloating_ip = ip['ip_address']
                break

        if not cfg.CONF.SERVICEPATH.path_physical_dev:
            self.phy_of_port = 0
        else:
            self.phy_of_port = self.phy_br.get_port_ofport(
                cfg.CONF.SERVICEPATH.path_physical_dev)
            if self.phy_of_port <= 0:
                self.phy_of_port = 0

        bridge = self.agent_api.bridge_mappings.get(self.physnet)
        port_name = p_utils.get_interface_name(
            bridge, prefix=p_const.PEER_INTEGRATION_PREFIX)
        self.ofport_int_to_phy = self.int_br.get_port_ofport(port_name)
        self.ofport_phy_to_int = self.agent_api.phys_ofports[self.physnet]

        self.subnet_routes = {}
        self.route_destinations = set()
        self.route_nexthops = set()
        for sub in self.privatefloating_network.get('subnets_detail', []):
            routes = set()
            for route in sub['host_routes']:
                routes.add((str(route['nexthop']), str(route['destination'])))
                self.route_destinations.add(str(route['destination']))
                self.route_nexthops.add(str(route['nexthop']))
            reg_id = 0
            self.subnet_routes[sub['id']] = (routes, reg_id, sub['cidr'])

    def prepare_flow(self, port_info):
        self.phy_br.arp_direct(self.pfn_segmentation_id)
        self.phy_br.ingress_direct(self.pfn_segmentation_id)
        self.ndp_direct_ip6(self.pfn_segmentation_id_v6)
        self.ingress_direct_ip6(self.pfn_segmentation_id_v6)

        for ip in self.privatefloating_port['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_6:
                self.privatefloating_ip_6 = ip['ip_address']
                self.ndp_op_to_controller_ip6(self.pfn_segmentation_id_v6,
                                              self.privatefloating_ip_6)
                self.install_ndp_responder(
                    self.pfn_segmentation_id_v6,
                    self.privatefloating_ip_6,
                    self.privatefloating_mac)
                break
        if not cfg.CONF.SERVICEPATH.path_gateway_mac:
            self.send_arp_to_route_destination()
        else:
            self.apply_arp_to_route_destination()
        self.init_path_flows()
        self.init_service_path_base_pipeline_flows()

        # handle port
        port_info.update({"provider_ips": self.get_provider_ip(port_info)})
        self.process_install_service_path_flows(port_info)
        for nexthop in self.route_nexthops:
            self.install_destination_mac_change_flows(nexthop, None)

    def do_check(self, context, result_map, ports):
        for port in ports:
            if not port.device_owner.startswith(
                    constants.DEVICE_OWNER_COMPUTE_PREFIX):
                return
            reports = result_map[port.id]['service_datapath']
            port_name = utils.get_port_name_by_id(self.int_br, port.id)
            if not port_name:
                reports.add('port %s not found on bridge %s',
                            port.id, self.int_br.br_name)
                continue
            vlan = self.int_br.get_port_tag_by_name(port_name)
            port_ofport = self.int_br.get_port_ofport(port_name)
            port_info = {"port_id": port.id,
                         "device_owner": port.device_owner,
                         "port_name": port_name,
                         "vlan": vlan,
                         "mac_address": str(port.mac_address),
                         "fixed_ips": [{'ip_address': str(ip['ip_address']),
                                        'subnet_id': ip['subnet_id']}
                                       for ip in port.fixed_ips],
                         "ofport": port_ofport}
            self.clear()
            self.prepare_flow(port_info)
            reports.extend(list(self.reports))
