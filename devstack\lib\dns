function configure_dns_extension {
    neutron_ml2_extension_driver_add "dns_domain_ports"
}
function configure_dns_integration {
    iniset $NEUTRON_CONF DEFAULT external_dns_driver designate
    iniset $NEUTRON_CONF designate url "$DESIGNATE_SERVICE_PROTOCOL://$DESIGNATE_SERVICE_HOST:$DESIGNATE_SERVICE_PORT/v2"
    configure_auth_token_middleware $NEUTRON_CONF designate $NEUTRON_AUTH_CACHE_DIR designate
}
function post_config_dns_extension {
    iniset $NEUTRON_CONF DEFAULT dns_domain openstackgate.local
}
