#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib.agent import l3_extension
from neutron_lib import constants
from oslo_log import log as logging

from neutron.agent.linux import ip_lib
from neutron.common import coordination

LOG = logging.getLogger(__name__)
DEFAULT_ELASTIC_SNAT_CHAIN = 'esnat'


class ElasticSnatAgentExtension(l3_extension.L3AgentExtension):
    router_esnat_ip_cidrs = {}

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        pass

    def consume_api(self, agent_api):
        self.agent_api = agent_api

    def _get_router_info(self, router_id):
        router_info = self.agent_api.get_router_info(router_id)
        if router_info:
            return router_info
        LOG.debug("Router %s is not managed by this agent. "
                  "It was possibly deleted concurrently.", router_id)

    def set_snat_fip_to_dev(self, ri, device):
        old_cidrs = self.router_esnat_ip_cidrs.get(ri.router_id, set())

        is_distributed = ri.router.get('distributed')
        ha_port = ri.router.get(constants.HA_INTERFACE_KEY, None)
        fip_statuses = {}
        existing_ips = set()
        existing_cidrs = ri.get_router_cidrs(device)
        for ip_cidr in existing_cidrs:
            existing_ips.add(str(netaddr.IPNetwork(ip_cidr).ip))
        new_cidrs = set()
        for rule in ri.router.get('_elastic_snat_rules', []):
            fip_ip = str(rule['floating_ip_address'])
            fip_cidr = str(netaddr.IPNetwork(fip_ip))
            new_cidrs.add(fip_cidr)
            status = ''
            if fip_ip not in existing_ips:
                try:
                    if not is_distributed:
                        fip_statuses[rule['floatingip_id']] = (
                            ri.add_floating_ip(
                                {'floating_ip_address': fip_ip},
                                device.name, device))
                    else:
                        if not ha_port:
                            device.addr.add(fip_cidr)
                            ip_lib.send_ip_addr_adv_notif(device.namespace,
                                                          device.name,
                                                          fip_ip)
                        else:
                            ri._add_vip(fip_cidr, device.name)
                        status = constants.FLOATINGIP_STATUS_ACTIVE
                except Exception as e:
                    status = constants.FLOATINGIP_STATUS_ERROR
                    LOG.warning("Unable to configure floating IP %(fip_id)s "
                                "for elastic floating SNAT %(esnat_id)s, "
                                "error: %(error)s",
                                {'fip_id': rule['floatingip_id'],
                                 'esnat_id': rule['id'],
                                 'error': e})
            else:
                if not ha_port:
                    ip_lib.send_ip_addr_adv_notif(device.namespace,
                                                  device.name,
                                                  fip_ip)
            if status:
                fip_statuses[rule['floatingip_id']] = status

        if ha_port and ha_port['status'] == constants.PORT_STATUS_ACTIVE:
            ri.enable_keepalived()

        for cidr in old_cidrs - new_cidrs:
            device.delete_conntrack_state(cidr)
        self.router_esnat_ip_cidrs[ri.router_id] = new_cidrs

        self._sending_elastic_snat_fip_status(ri, fip_statuses)

    def _sending_elastic_snat_fip_status(self, ri, statuses):
        if not statuses:
            return
        ri.agent.plugin_rpc.update_floatingip_statuses(
            ri.agent.context, ri.router_id, statuses)

    @coordination.synchronized('elastic-snat-router-{data[id]}')
    def process_elastic_snat(self, context, data):
        ri = self._get_router_info(data['id'])

        namespace, interface_name, iptables_manager = (
            self.get_elastic_snat_dev_infos(ri))
        if not interface_name or not iptables_manager:
            return

        device = ip_lib.IPDevice(interface_name, namespace=namespace)
        self.set_snat_fip_to_dev(ri, device)

        LOG.info("Processing elastic snat rules for router %s", data['id'])
        self.setup_snat_rules(iptables_manager,
                              ri.router.get('_elastic_snat_rules', []))
        if ri.agent_conf.delete_icmp_and_udp_conntrack:
            device.delete_icmp_and_udp_conntrack()

    def get_elastic_snat_dev_infos(self, router_info):
        ex_gw_port = router_info.get_ex_gw_port()
        if not ex_gw_port:
            return None, None, None
        agent_mode = router_info.agent_conf.agent_mode
        is_distributed_router = router_info.router.get('distributed')
        if agent_mode == constants.L3_AGENT_MODE_DVR:
            return None, None, None
        if is_distributed_router and agent_mode == (
                constants.L3_AGENT_MODE_DVR_SNAT):
            # DVR edge (or DVR edge ha) router
            if not router_info._is_this_snat_host():
                return None, None, None
            dev_name = router_info.get_snat_external_device_interface_name(
                ex_gw_port)
            iptables_manager = router_info.snat_iptables_manager
            namespace = router_info.snat_namespace.name
        else:
            # Legacy/HA router
            dev_name = router_info.get_external_device_interface_name(
                ex_gw_port)
            iptables_manager = router_info.iptables_manager
            namespace = router_info.ns_name
        if not dev_name:
            # DVR local router in dvr_no_external agent mode may not have
            # such rfp-device.
            return None, None, None
        return namespace, dev_name, iptables_manager

    def setup_snat_rules(self, iptables_manager, rules):
        iptables_manager.ipv4['nat'].empty_chain(DEFAULT_ELASTIC_SNAT_CHAIN)
        cidr_fip_pairs = []
        for rule in rules:
            for cidr in rule['internal_cidrs']:
                cidr_fip_pairs.append((cidr, rule['floating_ip_address']))
        for rule in sorted(
                cidr_fip_pairs,
                key=lambda rule: netaddr.IPNetwork(rule[0]).prefixlen,
                reverse=True):
            snat_rules = self.get_snat_rule(
                iptables_manager,
                cidr=rule[0],
                snat_ip_addr=rule[1])
            for snat_rule in snat_rules:
                iptables_manager.ipv4['nat'].add_rule(*snat_rule)
        iptables_manager.apply()

    def get_snat_rule(self, iptables_manager, cidr, snat_ip_addr):
        to_source = ('-s %(cidr)s -j SNAT '
                     '--to-source %(gw_ip)s') % {
            "cidr": cidr,
            "gw_ip": snat_ip_addr}
        if iptables_manager.random_fully:
            to_source += ' --random-fully'
        return [(DEFAULT_ELASTIC_SNAT_CHAIN, to_source)]

    def add_router(self, context, data):
        self.process_elastic_snat(context, data)

    def update_router(self, context, data):
        self.process_elastic_snat(context, data)

    def delete_router(self, context, data):
        pass

    def ha_state_change(self, context, data):
        pass
