# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr

from neutron_lib import constants as lib_constants
from neutron_lib.utils import helpers
from oslo_log import log as logging

from neutron.agent.common import utils as agent_comm
from neutron.agent.l3 import namespaces
from neutron.common import constants as l3_constants
from neutron.common import utils as common_utils

INTERNAL_DEV_PREFIX = namespaces.INTERNAL_DEV_PREFIX
EXTERNAL_DEV_PREFIX = namespaces.EXTERNAL_DEV_PREFIX

BRIDGE_INTERNAL_DEF_PREFIX = "intp"

LOG = logging.getLogger(__name__)


class OpenFlowInternalRouterMixin(object):

    def _get_snat_redirect_port(self, port):
        ex_gw_port = self.get_ex_gw_port()
        if not ex_gw_port:
            return
        sn_port = self.get_snat_port_for_internal_port(port)
        if not sn_port:
            return
        return sn_port

    def _set_subnet_arp_info(self, subnet, of_port):
        subnet_ports = self.agent.get_ports_by_subnet(subnet)
        cidr = {}
        for k, v in self.inf_ip_of_ports.items():
            cidr[v] = k
        subnet_mac = self.inf_mac_addresses[cidr[of_port]]

        for p in subnet_ports:
            if p['device_owner'] not in \
                    lib_constants.ROUTER_INTERFACE_OWNERS:
                for ip in p['fixed_ips']:
                    if not ip.get('visible', True):
                        continue
                    self.br.install_ip_forwarder_flows(ip['ip_address'],
                                                       subnet_mac,
                                                       p['mac_address'],
                                                       of_port)

    def internal_network_added(self, port):
        port_id = port['id']
        mac_address = port['mac_address']
        br_int_interface_name = self.gen_name(INTERNAL_DEV_PREFIX, port_id)
        br_dvrx_interface_name = self.gen_name(
            BRIDGE_INTERNAL_DEF_PREFIX, port_id)
        self.br.add_patch_port(br_dvrx_interface_name, br_int_interface_name)

        attrs = [('type', 'patch'),
                 ('options', {'peer': br_dvrx_interface_name}),
                 ('external_ids', {'iface-id': port_id,
                                   'iface-status': 'active',
                                   'attached-mac': mac_address})]

        self.int_br.add_port(br_int_interface_name, *attrs)
        of_port = self.br.get_port_ofport(br_dvrx_interface_name)

        for router_port in port['fixed_ips']:
            router_ip = router_port['ip_address']
            self.inf_ip_of_ports[router_ip] = of_port
            self.inf_mac_addresses[router_ip] = mac_address
            router_cidr = None

            for subnet in port['subnets']:
                if router_port['subnet_id'] in subnet['id']:
                    router_cidr = subnet['cidr']

            self.br.install_router_interface_flows(router_ip, router_cidr,
                                                   of_port, mac_address)

            net = netaddr.IPNetwork(router_cidr)
            if net.version == 4:
                self.br.install_goto_snat_ip4(mac_address, router_cidr)
            if net.version == 6:
                self.br.install_goto_snat_ip6(mac_address, router_cidr)

        self.br.install_arp_to_in_port()

        snat_port = self._get_snat_redirect_port(port)
        if snat_port:
            self.br.install_snat_route(mac_address,
                                       snat_port['mac_address'])

        router_ips = [p['ip_address'] for p in port['fixed_ips']]
        for subnet in port['subnets']:
            self._set_subnet_arp_info(subnet['id'], of_port)
            for router in self.agent.router_info.values():
                router.subnet_update(subnet, router_ips, mac_address, self)

    def internal_network_removed(self, port):
        port_id = port['id']
        mac_address = port['mac_address']
        br_int_interface_name = self.gen_name(INTERNAL_DEV_PREFIX, port_id)
        br_dvrx_interface_name = self.gen_name(
            BRIDGE_INTERNAL_DEF_PREFIX, port_id)
        self.br.delete_port(br_dvrx_interface_name)
        self.int_br.delete_port(br_int_interface_name)
        for router_port in port['fixed_ips']:
            router_ip = router_port['ip_address']
            router_cidr = None
            for subnet in port['subnets']:
                if router_port['subnet_id'] in subnet['id']:
                    router_cidr = subnet['cidr']
            self.br.remove_router_interface_ip_flows(router_ip, router_cidr,
                                                     mac_address)

        for subnet in port['subnets']:
            subnet_ports = self.agent.get_ports_by_subnet(subnet['id'])
            for p in subnet_ports:
                if p['device_owner'] not in \
                        lib_constants.ROUTER_INTERFACE_OWNERS:
                    for fixed_ip in p['fixed_ips']:
                        if not fixed_ip.get('visible', True):
                            continue
                        ip = fixed_ip['ip_address']
                        self.br.remove_ip_forwarder_flows(p['mac_address'],
                                                          ip)

    def subnet_update(self, subnet, router_ips, mac_address,
                      source_router=None):
        interfaces = self.router.get('_interfaces', [])
        fip_str = 'fixed_ips'
        fixed_ips = [fip for intfc in interfaces for fip in intfc[fip_str]]
        subnets = [interface['subnets'] for interface in interfaces]

        interface_mac = None
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        int_output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE

        if [subnet] in subnets:
            for router in self.agent.router_info.values():
                ips_in_router = list(set(router_ips).intersection(
                                     router.inf_ip_of_ports.keys()))
                for router_ip in ips_in_router:
                    for snet in router._get_router_subnets():
                        if agent_comm.check_ip_in_subnet(router_ip,
                                                         snet['cidr']):
                            for fixed_ip in fixed_ips:
                                ip = fixed_ip['ip_address']
                                if (fixed_ip['subnet_id'] in snet['id'] and
                                        ip in self.inf_mac_addresses.keys()):
                                    interface_mac = self.inf_mac_addresses[ip]

                    if (common_utils.get_ip_version(router_ip) == 4 and
                            interface_mac):
                        self.br.install_route_goto(
                            next_hop_table, int_output_table, 100,
                            interface_mac, mac_address, router_ip, 4)
                    elif (common_utils.get_ip_version(router_ip) == 6 and
                            interface_mac):
                        self.br.install_route_goto(
                            next_hop_table, int_output_table, 100,
                            interface_mac, mac_address, router_ip, 6)
            if source_router:
                for ip in self.inf_mac_addresses.keys():
                    if agent_comm.check_ip_in_subnet(ip, subnet['cidr']):
                        source_router.subnet_update(subnet, [ip],
                                                    self.inf_mac_addresses[ip])

    def update_routing_table(self, operation, route):
        if route['destination'] == '0.0.0.0/0':
            LOG.error('DVR-bridge:can not add/delete 0.0.0.0/0 route')
            return
        nexthop = route['nexthop']
        interface_mac = None
        interfaces = self.router.get('_interfaces', [])
        fip_str = 'fixed_ips'
        fixed_ips = [fip for intfc in interfaces for fip in intfc[fip_str]]
        mac = None

        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        ouput_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE

        for router in self.agent.router_info.values():
            subnets = router._get_router_subnets()
            for net in subnets:
                if agent_comm.check_ip_in_subnet(nexthop, net['cidr']):
                    for fixed_ip in fixed_ips:
                        ip = fixed_ip['ip_address']
                        if (fixed_ip['subnet_id'] in net['id'] and
                                ip in self.inf_mac_addresses.keys()):
                            interface_mac = self.inf_mac_addresses[ip]
                    subnet_ports = self.agent.get_ports_by_subnet(net['id'])
                    for port in subnet_ports:
                        ips = port['fixed_ips']
                        for ip in ips:
                            if nexthop == ip['ip_address']:
                                mac = port['mac_address']

        if mac and 'replace' in operation:
            if common_utils.get_ip_version(route['nexthop']) == 6:
                self.br.install_route_goto(
                    input_table, ouput_table, 250,
                    interface_mac, mac,
                    route['destination'], 6)
            else:
                self.br.install_route_goto(
                    input_table, ouput_table, 250,
                    interface_mac, mac,
                    route['destination'], 4)
        elif 'delete' in operation and mac:
            if common_utils.get_ip_version(route['nexthop']) == 6:
                self.br.remove_route_goto(input_table,
                                          route['destination'], 6)
            else:
                self.br.remove_route_goto(input_table,
                                          route['destination'], 4)

    def routes_updated(self, old_routes, new_routes):
        adds, removes = helpers.diff_list_of_dict(old_routes,
                                                  new_routes)
        for route in adds:
            LOG.debug("Added route entry is '%s'", route)
            for del_route in removes:
                if route['destination'] == del_route['destination']:
                    removes.remove(del_route)
            self.update_routing_table('replace', route)
        for route in removes:
            LOG.debug("Removed route entry is '%s'", route)
            self.update_routing_table('delete', route)

    def _update_arp_entry(self, ip, mac, subnet_id, operation, src=None):
        """Add or delete arp entry into router namespace for the subnet."""
        port = self._get_internal_port(subnet_id)
        # update arp entry only if the subnet is attached to the router

        if not port:
            LOG.debug("dvr interface not found for subnet: %s", subnet_id)
            return False

        for fixed_ip in port['fixed_ips']:
            if fixed_ip['subnet_id'] == subnet_id:
                of_port = self.inf_ip_of_ports[fixed_ip['ip_address']]
                src_mac = self.inf_mac_addresses[fixed_ip['ip_address']]

        if operation == 'add':
            self.br.install_ip_forwarder_flows(ip, src_mac, mac, of_port)
            return True
        elif operation == 'delete':
            LOG.debug("dvr removing interface for subnet: %s", subnet_id)
            LOG.debug("dvr removing interface for port: %s", port)

            self.br.remove_ip_forwarder_flows(mac, ip)
            return True
        return False

    def internal_network_updated(self, interface_name, ip_cidrs):
        pass
