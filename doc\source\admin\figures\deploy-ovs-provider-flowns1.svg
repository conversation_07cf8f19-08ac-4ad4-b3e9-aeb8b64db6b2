<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 298 529" width="298pt" height="529pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:11:40 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><path d="M 36.346457 325.98425 L 232.94488 325.98425 C 237.36316 325.98425 240.94488 329.56597 240.94488 333.98425 L 240.94488 456.8819 C 240.94488 461.30017 237.36316 464.8819 232.94488 464.8819 L 36.346457 464.8819 C 31.928179 464.8819 28.346457 461.30017 28.346457 456.8819 L 28.346457 333.98425 C 28.346457 329.56597 31.928179 325.98425 36.346457 325.98425 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 330.98425)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="13.355853" y="13" textLength="118.52344">Physical Network Infr</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="131.639056" y="13" textLength="51.111328">astructur</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="182.51015" y="13" textLength="6.732422">e</tspan></text><text transform="translate(33.346457 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".43359375" y="17" textLength="131.21875">Open vSwitch - Pr</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="131.33203" y="17" textLength="9.6640625">o</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="140.67578" y="17" textLength="112.890625">vider Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="4.3310547" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="61.28418" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="65.942383" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="83.80176" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="108.868164" y="35" textLength="140.80078">w - North/South Scenario</tspan></text><circle cx="155.90551" cy="496.063" r="8.5039505" fill="#738a05"/><text transform="translate(169.40945 484.05512)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="72.700195">vider network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="94.91406">VLAN 101, 203.0.113.0/24</tspan></text><path d="M 36.346457 56.692913 L 289.6378 56.692913 C 294.05607 56.692913 297.6378 60.274635 297.6378 64.692913 L 297.6378 289.6378 C 297.6378 294.05607 294.05607 297.6378 289.6378 297.6378 L 36.346457 297.6378 C 31.928179 297.6378 28.346457 294.05607 28.346457 289.6378 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="87.06266" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 135.559054 354.3307 L 218.77165 354.3307 C 223.18993 354.3307 226.77165 357.91243 226.77165 362.3307 L 226.77165 451.2126 C 226.77165 455.63088 223.18993 459.2126 218.77165 459.2126 L 135.559054 459.2126 C 131.14078 459.2126 127.559054 455.63088 127.559054 451.2126 L 127.559054 362.3307 C 127.559054 357.91243 131.14078 354.3307 135.559054 354.3307 Z" fill="#fdf5dd"/><path d="M 135.559054 354.3307 L 218.77165 354.3307 C 223.18993 354.3307 226.77165 357.91243 226.77165 362.3307 L 226.77165 451.2126 C 226.77165 455.63088 223.18993 459.2126 218.77165 459.2126 L 135.559054 459.2126 C 131.14078 459.2126 127.559054 455.63088 127.559054 451.2126 L 127.559054 362.3307 C 127.559054 357.91243 131.14078 354.3307 135.559054 354.3307 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 359.3307)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="31.93247" y="9" textLength="25.347656">Switch</tspan></text></g><line x1="198.4252" y1="439.37008" x2="283.46457" y2="439.37008" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 50.519685 354.3307 L 91.2126 354.3307 C 95.630876 354.3307 99.2126 357.91243 99.2126 362.3307 L 99.2126 451.2126 C 99.2126 455.63088 95.630876 459.2126 91.2126 459.2126 L 50.519685 459.2126 C 46.101407 459.2126 42.519685 455.63088 42.519685 451.2126 L 42.519685 362.3307 C 42.519685 357.91243 46.101407 354.3307 50.519685 354.3307 Z" fill="#fdf5dd"/><path d="M 50.519685 354.3307 L 91.2126 354.3307 C 95.630876 354.3307 99.2126 357.91243 99.2126 362.3307 L 99.2126 451.2126 C 99.2126 455.63088 95.630876 459.2126 91.2126 459.2126 L 50.519685 459.2126 C 46.101407 459.2126 42.519685 455.63088 42.519685 451.2126 L 42.519685 362.3307 C 42.519685 357.91243 46.101407 354.3307 50.519685 354.3307 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 359.3307)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="10.2058315" y="9" textLength="26.28125">Router</tspan></text></g><path d="M 268.79578 440.89256 C 262.38189 439.37008 264.93959 426.55323 275.17119 428.74015 C 276.12045 424.47713 288.01842 425.16907 287.94064 428.74015 C 295.40103 424.1727 304.93497 433.28012 298.54012 437.8476 C 306.21362 440.06201 298.44329 451.99304 292.14567 450 C 291.64167 453.32192 280.38342 454.4844 279.39526 450 C 273.02026 454.78913 259.72736 447.42557 268.79578 440.89256 Z" fill="#fdf5dd"/><path d="M 268.79578 440.89256 C 262.38189 439.37008 264.93959 426.55323 275.17119 428.74015 C 276.12045 424.47713 288.01842 425.16907 287.94064 428.74015 C 295.40103 424.1727 304.93497 433.28012 298.54012 437.8476 C 306.21362 440.06201 298.44329 451.99304 292.14567 450 C 291.64167 453.32192 280.38342 454.4844 279.39526 450 C 273.02026 454.78913 259.72736 447.42557 268.79578 440.89256 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(274.5748 433.87008)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="1.788201" y="9" textLength="14.203125">(16)</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" fill="#fdf5dd"/><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="127.559054" x2="170.07874" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="127.559054" x2="212.59842" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="127.559054" x2="255.11811" y2="127.559054" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#738a05"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" fill="#738a05"/><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" fill="#738a05"/><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><text transform="translate(154.30766 306.15741) rotate(4.3665005)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text><line x1="155.90551" y1="396.8504" x2="70.86614" y2="396.8504" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="439.37008" x2="155.90551" y2="439.37008" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 64.692913 382.67716 L 77.03937 382.67716 C 81.45765 382.67716 85.03937 386.25889 85.03937 390.67716 L 85.03937 403.02362 C 85.03937 407.4419 81.45765 411.02362 77.03937 411.02362 L 64.692913 411.02362 C 60.274635 411.02362 56.692913 407.4419 56.692913 403.02362 L 56.692913 390.67716 C 56.692913 386.25889 60.274635 382.67716 64.692913 382.67716 Z" fill="#738a05"/><path d="M 64.692913 382.67716 L 77.03937 382.67716 C 81.45765 382.67716 85.03937 386.25889 85.03937 390.67716 L 85.03937 403.02362 C 85.03937 407.4419 81.45765 411.02362 77.03937 411.02362 L 64.692913 411.02362 C 60.274635 411.02362 56.692913 407.4419 56.692913 403.02362 L 56.692913 390.67716 C 56.692913 386.25889 60.274635 382.67716 64.692913 382.67716 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 391.3504)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 425.19685 L 77.03937 425.19685 C 81.45765 425.19685 85.03937 428.77857 85.03937 433.19685 L 85.03937 445.5433 C 85.03937 449.96158 81.45765 453.5433 77.03937 453.5433 L 64.692913 453.5433 C 60.274635 453.5433 56.692913 449.96158 56.692913 445.5433 L 56.692913 433.19685 C 56.692913 428.77857 60.274635 425.19685 64.692913 425.19685 Z" fill="#fdf5dd"/><path d="M 64.692913 425.19685 L 77.03937 425.19685 C 81.45765 425.19685 85.03937 428.77857 85.03937 433.19685 L 85.03937 445.5433 C 85.03937 449.96158 81.45765 453.5433 77.03937 453.5433 L 64.692913 453.5433 C 60.274635 453.5433 56.692913 449.96158 56.692913 445.5433 L 56.692913 433.19685 C 56.692913 428.77857 60.274635 425.19685 64.692913 425.19685 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 433.87008)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.0716658" y="9" textLength="14.203125">(13)</tspan></text></g><line x1="198.4252" y1="396.8504" x2="155.90551" y2="396.8504" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="439.37008" x2="155.90551" y2="439.37008" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 192.25197 425.19685 L 204.59842 425.19685 C 209.0167 425.19685 212.59842 428.77857 212.59842 433.19685 L 212.59842 445.5433 C 212.59842 449.96158 209.0167 453.5433 204.59842 453.5433 L 192.25197 453.5433 C 187.83369 453.5433 184.25197 449.96158 184.25197 445.5433 L 184.25197 433.19685 C 184.25197 428.77857 187.83369 425.19685 192.25197 425.19685 Z" fill="#fdf5dd"/><path d="M 192.25197 425.19685 L 204.59842 425.19685 C 209.0167 425.19685 212.59842 428.77857 212.59842 433.19685 L 212.59842 445.5433 C 212.59842 449.96158 209.0167 453.5433 204.59842 453.5433 L 192.25197 453.5433 C 187.83369 453.5433 184.25197 449.96158 184.25197 445.5433 L 184.25197 433.19685 C 184.25197 428.77857 187.83369 425.19685 192.25197 425.19685 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 433.87008)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.0716658" y="9" textLength="14.203125">(15)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 382.67716 L 162.07874 382.67716 C 166.49702 382.67716 170.07874 386.25889 170.07874 390.67716 L 170.07874 403.02362 C 170.07874 407.4419 166.49702 411.02362 162.07874 411.02362 L 149.73228 411.02362 C 145.314005 411.02362 141.73228 407.4419 141.73228 403.02362 L 141.73228 390.67716 C 141.73228 386.25889 145.314005 382.67716 149.73228 382.67716 Z" fill="#738a05"/><path d="M 149.73228 382.67716 L 162.07874 382.67716 C 166.49702 382.67716 170.07874 386.25889 170.07874 390.67716 L 170.07874 403.02362 C 170.07874 407.4419 166.49702 411.02362 162.07874 411.02362 L 149.73228 411.02362 C 145.314005 411.02362 141.73228 407.4419 141.73228 403.02362 L 141.73228 390.67716 C 141.73228 386.25889 145.314005 382.67716 149.73228 382.67716 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 391.3504)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 425.19685 L 162.07874 425.19685 C 166.49702 425.19685 170.07874 428.77857 170.07874 433.19685 L 170.07874 445.5433 C 170.07874 449.96158 166.49702 453.5433 162.07874 453.5433 L 149.73228 453.5433 C 145.314005 453.5433 141.73228 449.96158 141.73228 445.5433 L 141.73228 433.19685 C 141.73228 428.77857 145.314005 425.19685 149.73228 425.19685 Z" fill="#fdf5dd"/><path d="M 149.73228 425.19685 L 162.07874 425.19685 C 166.49702 425.19685 170.07874 428.77857 170.07874 433.19685 L 170.07874 445.5433 C 170.07874 449.96158 166.49702 453.5433 162.07874 453.5433 L 149.73228 453.5433 C 145.314005 453.5433 141.73228 449.96158 141.73228 445.5433 L 141.73228 433.19685 C 141.73228 428.77857 145.314005 425.19685 149.73228 425.19685 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 433.87008)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="2.0716658" y="9" textLength="14.203125">(14)</tspan></text></g><circle cx="42.519685" cy="496.063" r="8.5039505" fill="#bd3612"/><text transform="translate(56.023622 484.05512)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 170.07874 L 133.73228 170.07874 C 138.15056 170.07874 141.73228 173.66046 141.73228 178.07874 L 141.73228 224.44094 C 141.73228 228.85922 138.15056 232.44094 133.73228 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" fill="#fdf5dd"/><path d="M 50.519685 170.07874 L 133.73228 170.07874 C 138.15056 170.07874 141.73228 173.66046 141.73228 178.07874 L 141.73228 224.44094 C 141.73228 228.85922 138.15056 232.44094 133.73228 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="4.9441895" y="9" textLength="8.375"> O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="13.2410645" y="9" textLength="19.824219">VS Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="32.905127" y="9" textLength="4.8867188">o</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="37.63169" y="9" textLength="46.63672">vider Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="25.547705" y="18" textLength="17.01123">br-pr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="42.4188" y="18" textLength="4.275879">o</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.55454" y="18" textLength="17.110352">vider</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 192.25197 232.44094 C 187.83369 232.44094 184.25197 228.85922 184.25197 224.44094 L 184.25197 178.07874 C 184.25197 173.66046 187.83369 170.07874 192.25197 170.07874 Z" fill="#fdf5dd"/><path d="M 192.25197 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 192.25197 232.44094 C 187.83369 232.44094 184.25197 228.85922 184.25197 224.44094 L 184.25197 178.07874 C 184.25197 173.66046 187.83369 170.07874 192.25197 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".73325205" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.952002" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.416846" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="35.217139" y="18" textLength="18.77832">br-int</tspan></text></g><path d="M 255.11811 127.559054 C 255.11811 127.559054 292.34646 145.98666 292.34646 170.07874 C 292.34646 194.17082 255.11811 212.59842 255.11811 212.59842" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 72.55301 283.46457 C 76.83674 287.59514 84.85532 292.41697 99.2126 297.6378 C 143.38141 313.69918 198.66423 297.87682 226.77165 325.98425 C 254.87908 354.09168 198.4252 396.8504 198.4252 396.8504" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="212.59842" y1="212.59842" x2="113.385826" y2="212.59842" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" fill="#738a05"/><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 382.67716 L 204.59842 382.67716 C 209.0167 382.67716 212.59842 386.25889 212.59842 390.67716 L 212.59842 403.02362 C 212.59842 407.4419 209.0167 411.02362 204.59842 411.02362 L 192.25197 411.02362 C 187.83369 411.02362 184.25197 407.4419 184.25197 403.02362 L 184.25197 390.67716 C 184.25197 386.25889 187.83369 382.67716 192.25197 382.67716 Z" fill="#bd3612"/><path d="M 192.25197 382.67716 L 204.59842 382.67716 C 209.0167 382.67716 212.59842 386.25889 212.59842 390.67716 L 212.59842 403.02362 C 212.59842 407.4419 209.0167 411.02362 204.59842 411.02362 L 192.25197 411.02362 C 187.83369 411.02362 184.25197 407.4419 184.25197 403.02362 L 184.25197 390.67716 C 184.25197 386.25889 187.83369 382.67716 192.25197 382.67716 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 391.3504)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(10)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" fill="#738a05"/><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><g filter="url(#Shadow)"><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" fill="#708284"/><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 198.4252 L 119.559054 198.4252 C 123.97733 198.4252 127.559054 202.00692 127.559054 206.4252 L 127.559054 218.77165 C 127.559054 223.18993 123.97733 226.77165 119.559054 226.77165 L 107.2126 226.77165 C 102.79432 226.77165 99.2126 223.18993 99.2126 218.77165 L 99.2126 206.4252 C 99.2126 202.00692 102.79432 198.4252 107.2126 198.4252 Z" fill="#708284"/><path d="M 107.2126 198.4252 L 119.559054 198.4252 C 123.97733 198.4252 127.559054 202.00692 127.559054 206.4252 L 127.559054 218.77165 C 127.559054 223.18993 123.97733 226.77165 119.559054 226.77165 L 107.2126 226.77165 C 102.79432 226.77165 99.2126 223.18993 99.2126 218.77165 L 99.2126 206.4252 C 99.2126 202.00692 102.79432 198.4252 107.2126 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><line x1="70.86614" y1="269.29134" x2="70.86614" y2="212.59842" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" fill="#bd3612"/><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 255.11811 L 77.03937 255.11811 C 81.45765 255.11811 85.03937 258.69983 85.03937 263.11811 L 85.03937 275.46457 C 85.03937 279.88284 81.45765 283.46457 77.03937 283.46457 L 64.692913 283.46457 C 60.274635 283.46457 56.692913 279.88284 56.692913 275.46457 L 56.692913 263.11811 C 56.692913 258.69983 60.274635 255.11811 64.692913 255.11811 Z" fill="#bd3612"/><path d="M 64.692913 255.11811 L 77.03937 255.11811 C 81.45765 255.11811 85.03937 258.69983 85.03937 263.11811 L 85.03937 275.46457 C 85.03937 279.88284 81.45765 283.46457 77.03937 283.46457 L 64.692913 283.46457 C 60.274635 283.46457 56.692913 279.88284 56.692913 275.46457 L 56.692913 263.11811 C 56.692913 258.69983 60.274635 255.11811 64.692913 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g></g></g></svg>
