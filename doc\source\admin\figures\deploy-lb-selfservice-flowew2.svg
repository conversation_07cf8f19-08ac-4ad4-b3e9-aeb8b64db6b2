<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 369 529" width="369pt" height="529pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:00:33 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><text transform="translate(64.52756 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".4140625" y="17" textLength="266.17188">Linux Bridge - Self-service Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="13.2041016" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="70.157227" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="74.81543" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="92.674805" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="117.74121" y="35" textLength="58.253906">w - East/W</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="175.75488" y="35" textLength="78.041016">est Scenario 2</tspan></text><path d="M 36.346457 56.692913 L 317.98425 56.692913 C 322.40253 56.692913 325.98425 60.274635 325.98425 64.692913 L 325.98425 235.77953 C 325.98425 240.1978 322.40253 243.77953 317.98425 243.77953 L 36.346457 243.77953 C 31.928179 243.77953 28.346457 240.1978 28.346457 235.77953 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="101.23589" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" fill="#fdf5dd"/><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="127.559054" x2="155.90551" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="127.559054" x2="198.4252" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="127.559054" x2="240.94488" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" fill="#2076c8"/><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" fill="#2076c8"/><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><line x1="240.94488" y1="127.559054" x2="297.6378" y2="155.90551" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" fill="#2076c8"/><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><path d="M 36.346457 255.11811 L 275.46457 255.11811 C 279.88284 255.11811 283.46457 258.69983 283.46457 263.11811 L 283.46457 428.53543 C 283.46457 432.9537 279.88284 436.53543 275.46457 436.53543 L 36.346457 436.53543 C 31.928179 436.53543 28.346457 432.9537 28.346457 428.53543 L 28.346457 263.11811 C 28.346457 258.69983 31.928179 255.11811 36.346457 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 260.11811)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="81.91257" y="13" textLength="81.29297">Network Node</tspan></text><g filter="url(#Shadow)"><path d="M 135.559054 283.46457 L 218.77165 283.46457 C 223.18993 283.46457 226.77165 287.04629 226.77165 291.46457 L 226.77165 337.82677 C 226.77165 342.24505 223.18993 345.82677 218.77165 345.82677 L 135.559054 345.82677 C 131.14078 345.82677 127.559054 342.24505 127.559054 337.82677 L 127.559054 291.46457 C 127.559054 287.04629 131.14078 283.46457 135.559054 283.46457 Z" fill="#fdf5dd"/><path d="M 135.559054 283.46457 L 218.77165 283.46457 C 223.18993 283.46457 226.77165 287.04629 226.77165 291.46457 L 226.77165 337.82677 C 226.77165 342.24505 223.18993 345.82677 218.77165 345.82677 L 135.559054 345.82677 C 131.14078 345.82677 127.559054 342.24505 127.559054 337.82677 L 127.559054 291.46457 C 127.559054 287.04629 131.14078 283.46457 135.559054 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 288.46457)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="20.50083" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.806006" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.045264" y="18" textLength="4.361328">q</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 368.50393 L 218.77165 368.50393 C 223.18993 368.50393 226.77165 372.08566 226.77165 376.50393 L 226.77165 422.86614 C 226.77165 427.28442 223.18993 430.86614 218.77165 430.86614 L 135.559054 430.86614 C 131.14078 430.86614 127.559054 427.28442 127.559054 422.86614 L 127.559054 376.50393 C 127.559054 372.08566 131.14078 368.50393 135.559054 368.50393 Z" fill="#fdf5dd"/><path d="M 135.559054 368.50393 L 218.77165 368.50393 C 223.18993 368.50393 226.77165 372.08566 226.77165 376.50393 L 226.77165 422.86614 C 226.77165 427.28442 223.18993 430.86614 218.77165 430.86614 L 135.559054 430.86614 C 131.14078 430.86614 127.559054 427.28442 127.559054 422.86614 L 127.559054 376.50393 C 127.559054 372.08566 131.14078 368.50393 135.559054 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 373.50393)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="20.50083" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.806006" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.045264" y="18" textLength="4.361328">q</tspan></text></g><g filter="url(#Shadow)"><path d="M 44.850392 283.46457 L 96.88189 283.46457 C 101.30017 283.46457 104.88189 287.04629 104.88189 291.46457 L 104.88189 394.51968 C 104.88189 398.93796 101.30017 402.51968 96.88189 402.51968 L 44.850392 402.51968 C 40.432114 402.51968 36.850392 398.93796 36.850392 394.51968 L 36.850392 291.46457 C 36.850392 287.04629 40.432114 283.46457 44.850392 283.46457 Z" fill="#fdf5dd"/><path d="M 44.850392 283.46457 L 96.88189 283.46457 C 101.30017 283.46457 104.88189 287.04629 104.88189 291.46457 L 104.88189 394.51968 C 104.88189 398.93796 101.30017 402.51968 96.88189 402.51968 L 44.850392 402.51968 C 40.432114 402.51968 36.850392 398.93796 36.850392 394.51968 L 36.850392 291.46457 C 36.850392 287.04629 40.432114 283.46457 44.850392 283.46457 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(41.850392 288.46457)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.875124" y="9" textLength="28.359375">Router </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.388796" y="20" textLength="45.253906">Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="16.135134" y="29" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="23.374392" y="29" textLength="18.521973">outer</tspan></text></g><line x1="155.90551" y1="325.98425" x2="70.86614" y2="337.32283" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="379.84252" x2="153.070865" y2="411.02362" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="198.4252" y1="325.98425" x2="255.11811" y2="354.3307" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="255.11811" y1="354.3307" x2="354.3307" y2="297.6378" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="297.6378" y1="155.90551" x2="354.3307" y2="297.6378" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 149.73228 311.81102 L 162.07874 311.81102 C 166.49702 311.81102 170.07874 315.39274 170.07874 319.81102 L 170.07874 332.15748 C 170.07874 336.57576 166.49702 340.15748 162.07874 340.15748 L 149.73228 340.15748 C 145.314005 340.15748 141.73228 336.57576 141.73228 332.15748 L 141.73228 319.81102 C 141.73228 315.39274 145.314005 311.81102 149.73228 311.81102 Z" fill="#2076c8"/><path d="M 149.73228 311.81102 L 162.07874 311.81102 C 166.49702 311.81102 170.07874 315.39274 170.07874 319.81102 L 170.07874 332.15748 C 170.07874 336.57576 166.49702 340.15748 162.07874 340.15748 L 149.73228 340.15748 C 145.314005 340.15748 141.73228 336.57576 141.73228 332.15748 L 141.73228 319.81102 C 141.73228 315.39274 145.314005 311.81102 149.73228 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 320.48425)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 311.81102 L 204.59842 311.81102 C 209.0167 311.81102 212.59842 315.39274 212.59842 319.81102 L 212.59842 332.15748 C 212.59842 336.57576 209.0167 340.15748 204.59842 340.15748 L 192.25197 340.15748 C 187.83369 340.15748 184.25197 336.57576 184.25197 332.15748 L 184.25197 319.81102 C 184.25197 315.39274 187.83369 311.81102 192.25197 311.81102 Z" fill="#2076c8"/><path d="M 192.25197 311.81102 L 204.59842 311.81102 C 209.0167 311.81102 212.59842 315.39274 212.59842 319.81102 L 212.59842 332.15748 C 212.59842 336.57576 209.0167 340.15748 204.59842 340.15748 L 192.25197 340.15748 C 187.83369 340.15748 184.25197 336.57576 184.25197 332.15748 L 184.25197 319.81102 C 184.25197 315.39274 187.83369 311.81102 192.25197 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 320.48425)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 323.1496 L 77.03937 323.1496 C 81.45765 323.1496 85.03937 326.73133 85.03937 331.1496 L 85.03937 343.49606 C 85.03937 347.91434 81.45765 351.49606 77.03937 351.49606 L 64.692913 351.49606 C 60.274635 351.49606 56.692913 347.91434 56.692913 343.49606 L 56.692913 331.1496 C 56.692913 326.73133 60.274635 323.1496 64.692913 323.1496 Z" fill="#2076c8"/><path d="M 64.692913 323.1496 L 77.03937 323.1496 C 81.45765 323.1496 85.03937 326.73133 85.03937 331.1496 L 85.03937 343.49606 C 85.03937 347.91434 81.45765 351.49606 77.03937 351.49606 L 64.692913 351.49606 C 60.274635 351.49606 56.692913 347.91434 56.692913 343.49606 L 56.692913 331.1496 C 56.692913 326.73133 60.274635 323.1496 64.692913 323.1496 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 331.82283)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(10)</tspan></text></g><path d="M 339.66193 299.16028 C 333.24803 297.6378 335.80573 284.82094 346.03733 287.00787 C 346.9866 282.74485 358.88456 283.43679 358.80678 287.00787 C 366.26717 282.4404 375.8011 291.54784 369.40626 296.1153 C 377.07976 298.32973 369.30943 310.26075 363.0118 308.26771 C 362.5078 311.58964 351.24956 312.75212 350.2614 308.26771 C 343.8864 313.05685 330.5935 305.69329 339.66193 299.16028 Z" fill="#a57706"/><path d="M 339.66193 299.16028 C 333.24803 297.6378 335.80573 284.82094 346.03733 287.00787 C 346.9866 282.74485 358.88456 283.43679 358.80678 287.00787 C 366.26717 282.4404 375.8011 291.54784 369.40626 296.1153 C 377.07976 298.32973 369.30943 310.26075 363.0118 308.26771 C 362.5078 311.58964 351.24956 312.75212 350.2614 308.26771 C 343.8864 313.05685 330.5935 305.69329 339.66193 299.16028 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(345.44094 286.6378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.071404" y="9" textLength="9.636719">(6)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="20" textLength="14.203125">(15)</tspan></text><text transform="translate(293.22456 319.88633) rotate(-29.744881)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#595ab7" x=".087890625" y="20" textLength="28.824219">VNI 102</tspan></text><text transform="translate(345.47813 245.88795) rotate(68.19859)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#595ab7" x=".087890625" y="20" textLength="28.824219">VNI 102</tspan></text><circle cx="184.25197" cy="467.71653" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 455.70866)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(56.023622 455.70866)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="42.519685" cy="467.71653" r="8.5039505" fill="#a57706"/><g filter="url(#Shadow)"><path d="M 50.519685 170.07874 L 91.2126 170.07874 C 95.630876 170.07874 99.2126 173.66046 99.2126 178.07874 L 99.2126 224.44094 C 99.2126 228.85922 95.630876 232.44094 91.2126 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" fill="#fdf5dd"/><path d="M 50.519685 170.07874 L 91.2126 170.07874 C 95.630876 170.07874 99.2126 173.66046 99.2126 178.07874 L 99.2126 224.44094 C 99.2126 228.85922 95.630876 232.44094 91.2126 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 170.07874 L 261.29134 170.07874 C 265.70962 170.07874 269.29134 173.66046 269.29134 178.07874 L 269.29134 224.44094 C 269.29134 228.85922 265.70962 232.44094 261.29134 232.44094 L 135.559054 232.44094 C 131.14078 232.44094 127.559054 228.85922 127.559054 224.44094 L 127.559054 178.07874 C 127.559054 173.66046 131.14078 170.07874 135.559054 170.07874 Z" fill="#fdf5dd"/><path d="M 135.559054 170.07874 L 261.29134 170.07874 C 265.70962 170.07874 269.29134 173.66046 269.29134 178.07874 L 269.29134 224.44094 C 269.29134 228.85922 265.70962 232.44094 261.29134 232.44094 L 135.559054 232.44094 C 131.14078 232.44094 127.559054 228.85922 127.559054 224.44094 L 127.559054 178.07874 C 127.559054 173.66046 131.14078 170.07874 135.559054 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="212.59842" x2="155.90551" y2="212.59842" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="212.59842" x2="198.4252" y2="212.59842" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="212.59842" x2="240.94488" y2="212.59842" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" fill="#5959b7"/><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(20)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 214.12091 C 177.34252 212.59842 179.90022 199.78157 190.13182 201.9685 C 191.08108 197.70548 202.97905 198.39742 202.90127 201.9685 C 210.36166 197.40104 219.8956 206.50847 213.50075 211.07594 C 221.17425 213.29036 213.40392 225.22138 207.1063 223.22835 C 206.6023 226.55027 195.34405 227.71275 194.35589 223.22835 C 187.98089 228.01748 174.68799 220.65392 183.75641 214.12091 Z" fill="#5959b7"/><path d="M 183.75641 214.12091 C 177.34252 212.59842 179.90022 199.78157 190.13182 201.9685 C 191.08108 197.70548 202.97905 198.39742 202.90127 201.9685 C 210.36166 197.40104 219.8956 206.50847 213.50075 211.07594 C 221.17425 213.29036 213.40392 225.22138 207.1063 223.22835 C 206.6023 226.55027 195.34405 227.71275 194.35589 223.22835 C 187.98089 228.01748 174.68799 220.65392 183.75641 214.12091 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7882012" y="9" textLength="14.203125">(18)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 198.4252 L 162.07874 198.4252 C 166.49702 198.4252 170.07874 202.00692 170.07874 206.4252 L 170.07874 218.77165 C 170.07874 223.18993 166.49702 226.77165 162.07874 226.77165 L 149.73228 226.77165 C 145.314005 226.77165 141.73228 223.18993 141.73228 218.77165 L 141.73228 206.4252 C 141.73228 202.00692 145.314005 198.4252 149.73228 198.4252 Z" fill="#5959b7"/><path d="M 149.73228 198.4252 L 162.07874 198.4252 C 166.49702 198.4252 170.07874 202.00692 170.07874 206.4252 L 170.07874 218.77165 C 170.07874 223.18993 166.49702 226.77165 162.07874 226.77165 L 149.73228 226.77165 C 145.314005 226.77165 141.73228 223.18993 141.73228 218.77165 L 141.73228 206.4252 C 141.73228 202.00692 145.314005 198.4252 149.73228 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(19)</tspan></text></g><line x1="240.94488" y1="212.59842" x2="297.6378" y2="155.90551" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 198.4252 L 247.11811 198.4252 C 251.53639 198.4252 255.11811 202.00692 255.11811 206.4252 L 255.11811 218.77165 C 255.11811 223.18993 251.53639 226.77165 247.11811 226.77165 L 234.77165 226.77165 C 230.35337 226.77165 226.77165 223.18993 226.77165 218.77165 L 226.77165 206.4252 C 226.77165 202.00692 230.35337 198.4252 234.77165 198.4252 Z" fill="#5959b7"/><path d="M 234.77165 198.4252 L 247.11811 198.4252 C 251.53639 198.4252 255.11811 202.00692 255.11811 206.4252 L 255.11811 218.77165 C 255.11811 223.18993 251.53639 226.77165 247.11811 226.77165 L 234.77165 226.77165 C 230.35337 226.77165 226.77165 223.18993 226.77165 218.77165 L 226.77165 206.4252 C 226.77165 202.00692 230.35337 198.4252 234.77165 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(17)</tspan></text></g><g filter="url(#Shadow)"><path d="M 291.46457 141.73228 L 303.81102 141.73228 C 308.2293 141.73228 311.81102 145.314005 311.81102 149.73228 L 311.81102 162.07874 C 311.81102 166.49702 308.2293 170.07874 303.81102 170.07874 L 291.46457 170.07874 C 287.04629 170.07874 283.46457 166.49702 283.46457 162.07874 L 283.46457 149.73228 C 283.46457 145.314005 287.04629 141.73228 291.46457 141.73228 Z" fill="#a57706"/><path d="M 291.46457 141.73228 L 303.81102 141.73228 C 308.2293 141.73228 311.81102 145.314005 311.81102 149.73228 L 311.81102 162.07874 C 311.81102 166.49702 308.2293 170.07874 303.81102 170.07874 L 291.46457 170.07874 C 287.04629 170.07874 283.46457 166.49702 283.46457 162.07874 L 283.46457 149.73228 C 283.46457 145.314005 287.04629 141.73228 291.46457 141.73228 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 144.90551)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(16)</tspan></text></g><line x1="198.4252" y1="411.02362" x2="255.11811" y2="354.3307" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 340.15748 L 261.29134 340.15748 C 265.70962 340.15748 269.29134 343.7392 269.29134 348.15748 L 269.29134 360.50393 C 269.29134 364.92221 265.70962 368.50393 261.29134 368.50393 L 248.94488 368.50393 C 244.5266 368.50393 240.94488 364.92221 240.94488 360.50393 L 240.94488 348.15748 C 240.94488 343.7392 244.5266 340.15748 248.94488 340.15748 Z" fill="#a57706"/><path d="M 248.94488 340.15748 L 261.29134 340.15748 C 265.70962 340.15748 269.29134 343.7392 269.29134 348.15748 L 269.29134 360.50393 C 269.29134 364.92221 265.70962 368.50393 261.29134 368.50393 L 248.94488 368.50393 C 244.5266 368.50393 240.94488 364.92221 240.94488 360.50393 L 240.94488 348.15748 C 240.94488 343.7392 244.5266 340.15748 248.94488 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 343.3307)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="20" textLength="14.203125">(14)</tspan></text></g><g filter="url(#Shadow)"><path d="M 146.89764 396.8504 L 159.24409 396.8504 C 163.66237 396.8504 167.24409 400.43211 167.24409 404.8504 L 167.24409 417.19685 C 167.24409 421.61513 163.66237 425.19685 159.24409 425.19685 L 146.89764 425.19685 C 142.47936 425.19685 138.89764 421.61513 138.89764 417.19685 L 138.89764 404.8504 C 138.89764 400.43211 142.47936 396.8504 146.89764 396.8504 Z" fill="#5959b7"/><path d="M 146.89764 396.8504 L 159.24409 396.8504 C 163.66237 396.8504 167.24409 400.43211 167.24409 404.8504 L 167.24409 417.19685 C 167.24409 421.61513 163.66237 425.19685 159.24409 425.19685 L 146.89764 425.19685 C 142.47936 425.19685 138.89764 421.61513 138.89764 417.19685 L 138.89764 404.8504 C 138.89764 400.43211 142.47936 396.8504 146.89764 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(143.89764 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 396.8504 L 204.59842 396.8504 C 209.0167 396.8504 212.59842 400.43211 212.59842 404.8504 L 212.59842 417.19685 C 212.59842 421.61513 209.0167 425.19685 204.59842 425.19685 L 192.25197 425.19685 C 187.83369 425.19685 184.25197 421.61513 184.25197 417.19685 L 184.25197 404.8504 C 184.25197 400.43211 187.83369 396.8504 192.25197 396.8504 Z" fill="#5959b7"/><path d="M 192.25197 396.8504 L 204.59842 396.8504 C 209.0167 396.8504 212.59842 400.43211 212.59842 404.8504 L 212.59842 417.19685 C 212.59842 421.61513 209.0167 425.19685 204.59842 425.19685 L 192.25197 425.19685 C 187.83369 425.19685 184.25197 421.61513 184.25197 417.19685 L 184.25197 404.8504 C 184.25197 400.43211 187.83369 396.8504 192.25197 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(13)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 365.6693 L 77.03937 365.6693 C 81.45765 365.6693 85.03937 369.25101 85.03937 373.6693 L 85.03937 386.01575 C 85.03937 390.43402 81.45765 394.01575 77.03937 394.01575 L 64.692913 394.01575 C 60.274635 394.01575 56.692913 390.43402 56.692913 386.01575 L 56.692913 373.6693 C 56.692913 369.25101 60.274635 365.6693 64.692913 365.6693 Z" fill="#5959b7"/><path d="M 64.692913 365.6693 L 77.03937 365.6693 C 81.45765 365.6693 85.03937 369.25101 85.03937 373.6693 L 85.03937 386.01575 C 85.03937 390.43402 81.45765 394.01575 77.03937 394.01575 L 64.692913 394.01575 C 60.274635 394.01575 56.692913 390.43402 56.692913 386.01575 L 56.692913 373.6693 C 56.692913 369.25101 60.274635 365.6693 64.692913 365.6693 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 374.34252)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><circle cx="42.519685" cy="496.063" r="8.5039505" fill="#5959b7"/><text transform="translate(54.574802 484.05512)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 102, 192.168.2.0/24</tspan></text></g></g></svg>
