<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 397 330" width="397pt" height="330pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:01:04 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><text transform="translate(79.49606 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".4140625" y="17" textLength="266.17188">Linux Bridge - Self-service Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="13.2041016" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="70.157227" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="74.81543" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="92.674805" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="117.74121" y="35" textLength="58.253906">w - East/W</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="175.75488" y="35" textLength="78.041016">est Scenario 1</tspan></text><path d="M 36.346457 56.692913 L 317.98425 56.692913 C 322.40253 56.692913 325.98425 60.274635 325.98425 64.692913 L 325.98425 145.070865 C 325.98425 149.48914 322.40253 153.070865 317.98425 153.070865 L 36.346457 153.070865 C 31.928179 153.070865 28.346457 149.48914 28.346457 145.070865 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="96.24663" y="13" textLength="95.14453">Compute Node 1</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" fill="#fdf5dd"/><path d="M 135.559054 85.03937 L 261.29134 85.03937 C 265.70962 85.03937 269.29134 88.62109 269.29134 93.03937 L 269.29134 139.40157 C 269.29134 143.81985 265.70962 147.40157 261.29134 147.40157 L 135.559054 147.40157 C 131.14078 147.40157 127.559054 143.81985 127.559054 139.40157 L 127.559054 93.03937 C 127.559054 88.62109 131.14078 85.03937 135.559054 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="127.559054" x2="155.90551" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="127.559054" x2="198.4252" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="127.559054" x2="240.94488" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" fill="#2076c8"/><path d="M 183.75641 129.08154 C 177.34252 127.559054 179.90022 114.742204 190.13182 116.92913 C 191.08108 112.66611 202.97905 113.358047 202.90127 116.92913 C 210.36166 112.36167 219.8956 121.4691 213.50075 126.036566 C 221.17425 128.25099 213.40392 140.182015 207.1063 138.188976 C 206.6023 141.5109 195.34405 142.673385 194.35589 138.188976 C 187.98089 142.97811 174.68799 135.61455 183.75641 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" fill="#2076c8"/><path d="M 149.73228 113.385826 L 162.07874 113.385826 C 166.49702 113.385826 170.07874 116.96755 170.07874 121.385826 L 170.07874 133.73228 C 170.07874 138.15056 166.49702 141.73228 162.07874 141.73228 L 149.73228 141.73228 C 145.314005 141.73228 141.73228 138.15056 141.73228 133.73228 L 141.73228 121.385826 C 141.73228 116.96755 145.314005 113.385826 149.73228 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><line x1="240.94488" y1="127.559054" x2="297.6378" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" fill="#2076c8"/><path d="M 234.77165 113.385826 L 247.11811 113.385826 C 251.53639 113.385826 255.11811 116.96755 255.11811 121.385826 L 255.11811 133.73228 C 255.11811 138.15056 251.53639 141.73228 247.11811 141.73228 L 234.77165 141.73228 C 230.35337 141.73228 226.77165 138.15056 226.77165 133.73228 L 226.77165 121.385826 C 226.77165 116.96755 230.35337 113.385826 234.77165 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><line x1="297.6378" y1="127.559054" x2="382.67716" y2="184.25197" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" fill="#a57706"/><path d="M 291.46457 113.385826 L 303.81102 113.385826 C 308.2293 113.385826 311.81102 116.96755 311.81102 121.385826 L 311.81102 133.73228 C 311.81102 138.15056 308.2293 141.73228 303.81102 141.73228 L 291.46457 141.73228 C 287.04629 141.73228 283.46457 138.15056 283.46457 133.73228 L 283.46457 121.385826 C 283.46457 116.96755 287.04629 113.385826 291.46457 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><text transform="translate(334.40057 152.06757) rotate(33.690067)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan></text><circle cx="184.25197" cy="297.6378" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 285.62992)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(54.06299 285.62992)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="42.519685" cy="297.6378" r="8.5039505" fill="#a57706"/><path d="M 36.346457 170.07874 L 317.98425 170.07874 C 322.40253 170.07874 325.98425 173.66046 325.98425 178.07874 L 325.98425 258.45669 C 325.98425 262.87497 322.40253 266.45669 317.98425 266.45669 L 36.346457 266.45669 C 31.928179 266.45669 28.346457 262.87497 28.346457 258.45669 L 28.346457 178.07874 C 28.346457 173.66046 31.928179 170.07874 36.346457 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="96.24663" y="13" textLength="95.14453">Compute Node 2</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 198.4252 L 91.2126 198.4252 C 95.630876 198.4252 99.2126 202.00692 99.2126 206.4252 L 99.2126 252.7874 C 99.2126 257.20568 95.630876 260.7874 91.2126 260.7874 L 50.519685 260.7874 C 46.101407 260.7874 42.519685 257.20568 42.519685 252.7874 L 42.519685 206.4252 C 42.519685 202.00692 46.101407 198.4252 50.519685 198.4252 Z" fill="#fdf5dd"/><path d="M 50.519685 198.4252 L 91.2126 198.4252 C 95.630876 198.4252 99.2126 202.00692 99.2126 206.4252 L 99.2126 252.7874 C 99.2126 257.20568 95.630876 260.7874 91.2126 260.7874 L 50.519685 260.7874 C 46.101407 260.7874 42.519685 257.20568 42.519685 252.7874 L 42.519685 206.4252 C 42.519685 202.00692 46.101407 198.4252 50.519685 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 203.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 135.559054 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 252.7874 C 269.29134 257.20568 265.70962 260.7874 261.29134 260.7874 L 135.559054 260.7874 C 131.14078 260.7874 127.559054 257.20568 127.559054 252.7874 L 127.559054 206.4252 C 127.559054 202.00692 131.14078 198.4252 135.559054 198.4252 Z" fill="#fdf5dd"/><path d="M 135.559054 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 252.7874 C 269.29134 257.20568 265.70962 260.7874 261.29134 260.7874 L 135.559054 260.7874 C 131.14078 260.7874 127.559054 257.20568 127.559054 252.7874 L 127.559054 206.4252 C 127.559054 202.00692 131.14078 198.4252 135.559054 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(132.559054 203.4252)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="60.06585" y="18" textLength="7.3793945">br</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="67.305106" y="18" textLength="4.361328">q</tspan></text></g><line x1="70.86614" y1="240.94488" x2="155.90551" y2="240.94488" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="155.90551" y1="240.94488" x2="198.4252" y2="240.94488" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="198.4252" y1="240.94488" x2="240.94488" y2="240.94488" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 226.77165 L 77.03937 226.77165 C 81.45765 226.77165 85.03937 230.35337 85.03937 234.77165 L 85.03937 247.11811 C 85.03937 251.53639 81.45765 255.11811 77.03937 255.11811 L 64.692913 255.11811 C 60.274635 255.11811 56.692913 251.53639 56.692913 247.11811 L 56.692913 234.77165 C 56.692913 230.35337 60.274635 226.77165 64.692913 226.77165 Z" fill="#2076c8"/><path d="M 64.692913 226.77165 L 77.03937 226.77165 C 81.45765 226.77165 85.03937 230.35337 85.03937 234.77165 L 85.03937 247.11811 C 85.03937 251.53639 81.45765 255.11811 77.03937 255.11811 L 64.692913 255.11811 C 60.274635 255.11811 56.692913 251.53639 56.692913 247.11811 L 56.692913 234.77165 C 56.692913 230.35337 60.274635 226.77165 64.692913 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><g filter="url(#Shadow)"><path d="M 183.75641 242.46737 C 177.34252 240.94488 179.90022 228.12803 190.13182 230.31496 C 191.08108 226.05194 202.97905 226.74387 202.90127 230.31496 C 210.36166 225.7475 219.8956 234.85493 213.50075 239.42239 C 221.17425 241.63682 213.40392 253.56784 207.1063 251.5748 C 206.6023 254.89672 195.34405 256.05921 194.35589 251.5748 C 187.98089 256.36394 174.68799 249.00038 183.75641 242.46737 Z" fill="#2076c8"/><path d="M 183.75641 242.46737 C 177.34252 240.94488 179.90022 228.12803 190.13182 230.31496 C 191.08108 226.05194 202.97905 226.74387 202.90127 230.31496 C 210.36166 225.7475 219.8956 234.85493 213.50075 239.42239 C 221.17425 241.63682 213.40392 253.56784 207.1063 251.5748 C 206.6023 254.89672 195.34405 256.05921 194.35589 251.5748 C 187.98089 256.36394 174.68799 249.00038 183.75641 242.46737 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.53543 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(9)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 226.77165 L 162.07874 226.77165 C 166.49702 226.77165 170.07874 230.35337 170.07874 234.77165 L 170.07874 247.11811 C 170.07874 251.53639 166.49702 255.11811 162.07874 255.11811 L 149.73228 255.11811 C 145.314005 255.11811 141.73228 251.53639 141.73228 247.11811 L 141.73228 234.77165 C 141.73228 230.35337 145.314005 226.77165 149.73228 226.77165 Z" fill="#2076c8"/><path d="M 149.73228 226.77165 L 162.07874 226.77165 C 166.49702 226.77165 170.07874 230.35337 170.07874 234.77165 L 170.07874 247.11811 C 170.07874 251.53639 166.49702 255.11811 162.07874 255.11811 L 149.73228 255.11811 C 145.314005 255.11811 141.73228 251.53639 141.73228 247.11811 L 141.73228 234.77165 C 141.73228 230.35337 145.314005 226.77165 149.73228 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(10)</tspan></text></g><line x1="240.94488" y1="240.94488" x2="297.6378" y2="240.94488" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 234.77165 226.77165 L 247.11811 226.77165 C 251.53639 226.77165 255.11811 230.35337 255.11811 234.77165 L 255.11811 247.11811 C 255.11811 251.53639 251.53639 255.11811 247.11811 255.11811 L 234.77165 255.11811 C 230.35337 255.11811 226.77165 251.53639 226.77165 247.11811 L 226.77165 234.77165 C 226.77165 230.35337 230.35337 226.77165 234.77165 226.77165 Z" fill="#2076c8"/><path d="M 234.77165 226.77165 L 247.11811 226.77165 C 251.53639 226.77165 255.11811 230.35337 255.11811 234.77165 L 255.11811 247.11811 C 255.11811 251.53639 251.53639 255.11811 247.11811 255.11811 L 234.77165 255.11811 C 230.35337 255.11811 226.77165 251.53639 226.77165 247.11811 L 226.77165 234.77165 C 226.77165 230.35337 230.35337 226.77165 234.77165 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(231.77165 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><line x1="297.6378" y1="240.94488" x2="382.67716" y2="184.25197" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 291.46457 226.77165 L 303.81102 226.77165 C 308.2293 226.77165 311.81102 230.35337 311.81102 234.77165 L 311.81102 247.11811 C 311.81102 251.53639 308.2293 255.11811 303.81102 255.11811 L 291.46457 255.11811 C 287.04629 255.11811 283.46457 251.53639 283.46457 247.11811 L 283.46457 234.77165 C 283.46457 230.35337 287.04629 226.77165 291.46457 226.77165 Z" fill="#a57706"/><path d="M 291.46457 226.77165 L 303.81102 226.77165 C 308.2293 226.77165 311.81102 230.35337 311.81102 234.77165 L 311.81102 247.11811 C 311.81102 251.53639 308.2293 255.11811 303.81102 255.11811 L 291.46457 255.11811 C 287.04629 255.11811 283.46457 251.53639 283.46457 247.11811 L 283.46457 234.77165 C 283.46457 230.35337 287.04629 226.77165 291.46457 226.77165 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(288.46457 235.44488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><text transform="translate(334.40057 216.43636) rotate(-33.690067)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan></text><path d="M 368.00838 185.77446 C 361.59449 184.25197 364.15219 171.43512 374.38378 173.62205 C 375.33305 169.35902 387.23102 170.05096 387.15324 173.62205 C 394.61363 169.05458 404.14756 178.16201 397.75272 182.72948 C 405.42621 184.9439 397.65588 196.87493 391.35827 194.88189 C 390.85427 198.20381 379.59602 199.3663 378.60786 194.88189 C 372.23286 199.67102 358.93995 192.30746 368.00838 185.77446 Z" fill="#a57706"/><path d="M 368.00838 185.77446 C 361.59449 184.25197 364.15219 171.43512 374.38378 173.62205 C 375.33305 169.35902 387.23102 170.05096 387.15324 173.62205 C 394.61363 169.05458 404.14756 178.16201 397.75272 182.72948 C 405.42621 184.9439 397.65588 196.87493 391.35827 194.88189 C 390.85427 198.20381 379.59602 199.3663 378.60786 194.88189 C 372.23286 199.67102 358.93995 192.30746 368.00838 185.77446 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(373.7874 178.75197)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.071404" y="9" textLength="9.636719">(6)</tspan></text></g></g></svg>
