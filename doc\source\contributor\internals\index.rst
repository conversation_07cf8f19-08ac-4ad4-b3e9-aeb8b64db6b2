..
      Copyright 2010-2011 United States Government as represented by the
      Administrator of the National Aeronautics and Space Administration.
      All Rights Reserved.

      Licensed under the Apache License, Version 2.0 (the "License"); you may
      not use this file except in compliance with the License. You may obtain
      a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
      WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
      License for the specific language governing permissions and limitations
      under the License.

      Convention for heading levels in Neutron devref:
      =======  Heading 0 (reserved for the title in a document)
      -------  Heading 1
      ~~~~~~~  Heading 2
      +++++++  Heading 3
      '''''''  Heading 4
      (Avoid deeper levels because they do not render well.)

=================
Neutron Internals
=================

.. toctree::
   :maxdepth: 1

   address_scopes
   agent_extensions
   api_extensions
   api_layer
   calling_ml2_plugin
   db_layer
   db_models
   dns_order
   external_dns_integration
   i18n
   l2_agent_extensions
   l2_agents
   l3_agent_extensions
   layer3
   linuxbridge_agent
   live_migration
   ml2_ext_manager
   network_ip_availability
   objects_usage
   openvswitch_agent
   openvswitch_firewall
   ovs_vhostuser
   plugin-api
   policy
   provisioning_blocks
   quality_of_service
   quota
   retries
   rpc_api
   rpc_callbacks
   security_group_api
   segments
   service_extensions
   services_and_agents
   sriov_nic_agent
   tag
   upgrade
