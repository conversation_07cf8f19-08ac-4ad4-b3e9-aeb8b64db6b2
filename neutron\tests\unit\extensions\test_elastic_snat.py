#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api.definitions import external_net as extnet_apidef
from neutron_lib import context
from neutron_lib.exceptions import l3 as l3_exc
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_utils import uuidutils
from webob import exc

from neutron.db import l3_fip_qos
from neutron.db import l3_gwmode_db
from neutron.extensions import _elastic_snat as api_def
from neutron.extensions import elastic_snat as elastic_snat_ext
from neutron.extensions import l3
from neutron.services.elastic_snat.common import exceptions
from neutron.tests.unit.db import test_db_base_plugin_v2
from neutron.tests.unit.extensions import test_l3

_uuid = uuidutils.generate_uuid


class TestElasticSnatIntPlugin(test_l3.TestL3NatAgentSchedulingServicePlugin,
                               l3_gwmode_db.L3_NAT_db_mixin,
                               l3_fip_qos.FloatingQoSDbMixin):
    supported_extension_aliases = ["router", "dns-integration", "ext-gw-mode",
                                   "dvr", "qos-fip", "l3_agent_scheduler"]


class ElasticSnatTestExtensionManager(object):

    def get_resources(self):
        return (l3.L3.get_resources() +
                elastic_snat_ext.Elastic_snat.get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class TestElasticSnatExtension(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
        test_l3.L3NatTestCaseMixin):

    def setUp(self):
        svc_plugins = (
            'neutron.tests.unit.extensions.test_elastic_snat.'
            'TestElasticSnatIntPlugin',
            'neutron.services.elastic_snat.plugin.ElasticSnatPlugin',
            'neutron.services.qos.qos_plugin.QoSPlugin')
        ext_mgr = ElasticSnatTestExtensionManager()
        super(TestElasticSnatExtension, self).setUp(
            plugin='neutron.tests.unit.extensions.test_l3.TestL3NatIntPlugin',
            ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.l3_plugin = directory.get_plugin(plugin_constants.L3)
        self.elastic_snat_plugin = directory.get_plugin(api_def.ELASTIC_SNAT)

    def test_create_elastic_snat_with_subnets(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

    def test_failed_to_bind_fip_to_port_while_it_has_elastic_snats(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self.assertRaises(
                    exceptions.FipInUseByElasticSnat,
                    self.l3_plugin.update_floatingip,
                    ctx, fip['floatingip']['id'],
                    {'floatingip': {"port_id": _uuid()}})

    def test_failed_to_delete_fip_while_it_has_elastic_snats(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self.assertRaises(
                    exceptions.FipInUseByElasticSnat,
                    self.l3_plugin.delete_floatingip,
                    ctx, fip['floatingip']['id'])

    def test_failed_to_set_router_gateway_enable_snat_to_true(self):
        cfg.CONF.set_override(
            "allow_activate_router_gateway_enable_snat", False)
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                    self.router() as router:

                self._add_external_gateway_to_router(
                    router['router']['id'],
                    extnet['network']['id'],
                    enable_snat=True,
                    expected_code=exc.HTTPBadRequest.code)

    def test_allow_set_router_gateway_enable_snat_to_true(self):
        cfg.CONF.set_override(
            "allow_activate_router_gateway_enable_snat", True)
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                    self.router() as router:

                self._add_external_gateway_to_router(
                    router['router']['id'],
                    extnet['network']['id'],
                    enable_snat=True)

    def test_failed_to_remove_router_gateway_while_it_has_elastic_snats(self):
        cfg.CONF.set_override(
            "allow_remove_router_gateway_if_has_snats", False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self.assertRaises(
                    l3_exc.RouterExternalGatewayInUseByFloatingIp,
                    self.l3_plugin.update_router,
                    ctx, router['router']['id'],
                    {'router': {"external_gateway_info": {}}})

    def test_failed_to_set_enable_snat_true_while_it_has_elastic_snats(self):
        cfg.CONF.set_override(
            "allow_activate_router_gateway_enable_snat", False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self._add_external_gateway_to_router(
                    router['router']['id'],
                    extnet['network']['id'],
                    expected_code=exc.HTTPBadRequest.code,
                    enable_snat=True)

    def test_detach_router_interface_while_it_has_elastic_snats(self):
        cfg.CONF.set_override(
            "allow_remove_router_interface_if_has_snats", False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self._router_interface_action(
                    'remove',
                    router['router']['id'],
                    insub['subnet']['id'],
                    None,
                    expected_code=exc.HTTPConflict.code)

    def test_create_elastic_snat_with_internal_cidrs(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr], esnat['internal_cidrs'])

    def test_create_elastic_snat_with_no_check_internal_cidrs(self):
        cfg.CONF.set_override(
            "allow_cidrs_no_check_in_elastic_snat", ['********/8'])
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['*********/16',
                                                     '**********/24',
                                                     '************/32']}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual(['*********/16',
                                  '**********/24',
                                  '************/32'], esnat['internal_cidrs'])

    def test_create_elastic_snat_with_internal_cidrs_and_no_check_cidrs(self):
        cfg.CONF.set_override(
            "allow_cidrs_no_check_in_elastic_snat", ['********/8'])
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['*********/16',
                                                     '**********/24',
                                                     '10.0.0.0/24']}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual(['*********/16',
                                  '**********/24',
                                  '10.0.0.0/24'], esnat['internal_cidrs'])

    def test_create_elastic_snat_equal_to_no_check_cidrs(self):
        cfg.CONF.set_override(
            "allow_cidrs_no_check_in_elastic_snat", ['********/24'])
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['********/24']}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual(['********/24'], esnat['internal_cidrs'])

    def test_create_elastic_snat_greater_than_no_check_cidrs(self):
        cfg.CONF.set_override(
            "allow_cidrs_no_check_in_elastic_snat", ['********/24'])
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['********/16']}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

    def test_create_elastic_snat_with_any_cidr_avoid_check(self):
        cfg.CONF.set_override(
            "allow_cidrs_no_check_in_elastic_snat", ['0.0.0.0/0'])
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                    self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['0.0.0.0/0',
                                                     '*********/16',
                                                     '**********/24',
                                                     '************/18']}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual(['0.0.0.0/0',
                                  '*********/16',
                                  '**********/24',
                                  '************/18'], esnat['internal_cidrs'])

    def test_create_elastic_snat_with_invalid_subnets(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                non_exists_subnet = _uuid()
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [non_exists_subnet]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

    def test_create_elastic_snat_with_invalid_internal_cidrs(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['**********/24']}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

    def test_create_elastic_snat_with_same_fip_twice(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr_1 = '10.0.0.0/24'
        internal_cidr_2 = '********/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr_1) as insub1, \
                 self.subnet(network=innet, cidr=internal_cidr_2) as insub2, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr_1]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr_1], esnat['internal_cidrs'])

                elastic_snat_2 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr_2]}
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})

                self._router_interface_action('add', router['router']['id'],
                                              insub2['subnet']['id'], None)
                elastic_snat_2 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr_2]}
                self.assertRaises(
                    exceptions.DuplicatedFloatingIPForElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_create_elastic_snat_with_router_has_no_gw(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr_1 = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr_1) as insub1, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr_1]}

                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

    def test_failed_create_elastic_snat_with_router_enable_snat_true(self):
        cfg.CONF.set_override(
            "allow_activate_router_gateway_enable_snat", True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr_1 = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr_1) as insub1, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr_1]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=True)
                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)
                cfg.CONF.set_override(
                    "allow_activate_router_gateway_enable_snat", False)

                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

    def test_sucessed_create_elastic_snat_with_router_enable_snat_true(self):
        cfg.CONF.set_override(
            "allow_activate_router_gateway_enable_snat", True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr_1 = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr_1) as insub1, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr_1]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=True)
                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr_1], esnat['internal_cidrs'])

    def test_create_elastic_snat_with_subnets_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name1,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}
                name2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name2,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                esnat1 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name1, esnat1['name'])
                self.assertEqual([insub['subnet']['id']], esnat1['subnets'])
                self.assertEqual([], esnat1['internal_cidrs'])

                esnat2 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name2, esnat2['name'])
                self.assertEqual([insub['subnet']['id']], esnat2['subnets'])
                self.assertEqual([], esnat2['internal_cidrs'])

    def test_create_elastic_snat_with_internal_cidrs_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)
                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr], esnat['internal_cidrs'])

                elastic_snat_2 = {'name': name,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr,
                                                     '10.0.0.0/30']}
                self.assertRaises(
                    exceptions.FailedToCreateElasticSnatCIDROverlaps,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_create_elastic_snat_allow_subnets_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])

                name1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name1,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}
                name2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name2,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat1 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name1, esnat1['name'])
                self.assertEqual([insub['subnet']['id']], esnat1['subnets'])
                self.assertEqual([], esnat1['internal_cidrs'])

                esnat2 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name2, esnat2['name'])
                self.assertEqual([insub['subnet']['id']], esnat2['subnets'])
                self.assertEqual([], esnat2['internal_cidrs'])

    def test_create_elastic_snat_allow_internal_cidrs_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])
                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr], esnat['internal_cidrs'])

                name = "elastic_snat_2"
                elastic_snat_2 = {'name': name,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': ['10.0.0.0/27',
                                                     '10.0.0.0/30']}
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual(['10.0.0.0/27',
                                  '10.0.0.0/30'], esnat['internal_cidrs'])

    def test_create_elastic_snat_internal_cidrs_fully_overlap(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])
                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name1,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}
                esnat1 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name1, esnat1['name'])
                self.assertEqual([], esnat1['subnets'])
                self.assertEqual([internal_cidr], esnat1['internal_cidrs'])

                name2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name2,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}

                esnat2 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name2, esnat2['name'])
                self.assertEqual([], esnat2['subnets'])
                self.assertEqual([internal_cidr], esnat2['internal_cidrs'])

    def test_create_elastic_snats_cidr_and_subnet_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name1,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}
                esnat1 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name1, esnat1['name'])
                self.assertEqual([insub['subnet']['id']], esnat1['subnets'])
                self.assertEqual([], esnat1['internal_cidrs'])

                name2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name2,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}
                esnat2 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name2, esnat2['name'])
                self.assertEqual([], esnat2['subnets'])
                self.assertEqual([internal_cidr], esnat2['internal_cidrs'])

    def test_create_elastic_snat_while_fip_has_been_bound_to_port(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router, self.port(subnet=insub) as port:

                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                self.l3_plugin.update_floatingip(
                    ctx, fip['floatingip']['id'], {'floatingip': {
                        'port_id': port['port']['id']}})

                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

    def test_create_elastic_snat_with_esnats_per_router_exceeded(self):
        cfg.CONF.set_override("max_esnats_per_router", 1)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr_1 = '********/24'
        internal_cidr_2 = '********/24'
        internal_cidr_3 = '********/24'
        internal_cidr_4 = '********/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr_1) as insub1, \
                 self.subnet(network=innet, cidr=internal_cidr_2) as insub2, \
                 self.subnet(network=innet, cidr=internal_cidr_3) as insub3, \
                 self.subnet(network=innet, cidr=internal_cidr_4) as insub4, \
                    self.router() as router1, \
                    self.router() as router2:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip3 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip4 = self._make_floatingip(self.fmt, extnet['network']['id'])

                name1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name1,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router1['router']['id'],
                                  'subnets': [insub1['subnet']['id']]}
                name2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name2,
                                  'floatingip_id': fip2['floatingip']['id'],
                                  'router_id': router1['router']['id'],
                                  'subnets': [insub2['subnet']['id']]}
                name3 = "elastic_snat_3"
                elastic_snat_3 = {'name': name3,
                                  'floatingip_id': fip3['floatingip']['id'],
                                  'router_id': router2['router']['id'],
                                  'subnets': [insub3['subnet']['id']]}
                name4 = "elastic_snat_4"
                elastic_snat_4 = {'name': name4,
                                  'floatingip_id': fip4['floatingip']['id'],
                                  'router_id': router2['router']['id'],
                                  'subnets': [insub4['subnet']['id']]}

                self._add_external_gateway_to_router(router1['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._add_external_gateway_to_router(router2['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router1['router']['id'],
                                              insub1['subnet']['id'], None)
                self._router_interface_action('add', router1['router']['id'],
                                              insub2['subnet']['id'], None)
                self._router_interface_action('add', router2['router']['id'],
                                              insub3['subnet']['id'], None)
                self._router_interface_action('add', router2['router']['id'],
                                              insub4['subnet']['id'], None)

                esnat1 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name1, esnat1['name'])
                self.assertEqual([insub1['subnet']['id']], esnat1['subnets'])
                self.assertEqual([], esnat1['internal_cidrs'])
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                esnat3 = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_3})
                self.assertEqual(name3, esnat3['name'])
                self.assertEqual([insub3['subnet']['id']], esnat3['subnets'])
                self.assertEqual([], esnat3['internal_cidrs'])
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_4})

    def test_create_elastic_snat_with_esnat_rules_per_router_exceeded(self):
        cfg.CONF.set_override("max_esnat_rules_per_router", 2)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr_1 = '********/24'
        internal_cidr_2 = '********/24'
        internal_cidr_3 = '********/24'
        internal_cidr_1_1 = '11.0.0.1'
        internal_cidr_2_1 = '12.0.0.1'
        internal_cidr_2_2 = '12.0.0.2'
        internal_cidr_3_1 = '13.0.0.1'
        internal_cidr_3_2 = '13.0.0.2'
        internal_cidr_3_3 = '13.0.0.3'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr_1) as insub1, \
                 self.subnet(network=innet, cidr=internal_cidr_2) as insub2, \
                 self.subnet(network=innet, cidr=internal_cidr_3) as insub3, \
                    self.router() as router1, \
                    self.router() as router2, \
                    self.router() as router3:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip2 = self._make_floatingip(self.fmt, extnet['network']['id'])
                fip3 = self._make_floatingip(self.fmt, extnet['network']['id'])

                name1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name1,
                                'floatingip_id': fip1['floatingip']['id'],
                                'router_id': router1['router']['id'],
                                'internal_cidrs': [internal_cidr_1_1]}
                name2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name2,
                                'floatingip_id': fip2['floatingip']['id'],
                                'router_id': router2['router']['id'],
                                'internal_cidrs': [internal_cidr_2_1,
                                                   internal_cidr_2_2]}
                name3 = "elastic_snat_3"
                elastic_snat_3 = {'name': name3,
                                'floatingip_id': fip3['floatingip']['id'],
                                'router_id': router3['router']['id'],
                                'internal_cidrs': [internal_cidr_3_1,
                                                   internal_cidr_3_2,
                                                   internal_cidr_3_3]}

                self._add_external_gateway_to_router(router1['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._add_external_gateway_to_router(router2['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._add_external_gateway_to_router(router3['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router1['router']['id'],
                                              insub1['subnet']['id'], None)
                self._router_interface_action('add', router2['router']['id'],
                                              insub2['subnet']['id'], None)
                self._router_interface_action('add', router3['router']['id'],
                                              insub3['subnet']['id'], None)
                self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_3})

    def test_esnats_per_router_and_esnat_rules_per_router_setting_error(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        cidr_1 = '********'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name_1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name_1,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)
                cfg.CONF.set_override("max_esnats_per_router", -1)
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

                cfg.CONF.set_override("max_esnat_rules_per_router", -2)
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.create_elastic_snat,
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})

                cfg.CONF.set_override("max_esnats_per_router", 5)
                cfg.CONF.set_override("max_esnat_rules_per_router", 10)
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name_1, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                cfg.CONF.set_override("max_esnat_rules_per_router", -5)
                name_2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name_2,
                                  'internal_cidrs': [cidr_1]}
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.update_elastic_snat,
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_update_elastic_snat(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub1, \
                 self.subnet(network=innet, cidr='********/24') as insub2, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name_1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name_1,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub1['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name_1, esnat['name'])
                self.assertEqual([insub1['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                name_2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name_2,
                                  'subnets': [insub1['subnet']['id'],
                                              insub2['subnet']['id']]}
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.update_elastic_snat,
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

                self._router_interface_action('add', router['router']['id'],
                                              insub2['subnet']['id'], None)

                name_3 = "elastic_snat_3"
                elastic_snat_3 = {'name': name_3,
                                  'subnets': [insub1['subnet']['id'],
                                              insub2['subnet']['id']]}
                esnat = self.elastic_snat_plugin.update_elastic_snat(
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_3})
                self.assertEqual(name_3, esnat['name'])
                self.assertEqual([insub1['subnet']['id'],
                                  insub2['subnet']['id']], esnat['subnets'])

    def test_update_elastic_snat_from_subnets_to_cidrs(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        cidr_1 = '10.0.0.0/24'
        cidr_2 = '********/24'
        cidr_3 = '********/24'
        cidr_4 = '********/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=cidr_1) as insub1, \
                 self.subnet(network=innet, cidr=cidr_2) as insub2, \
                 self.subnet(network=innet, cidr=cidr_3) as insub3, \
                 self.subnet(network=innet, cidr=cidr_4) as insub4, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              insub2['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              insub3['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              insub4['subnet']['id'], None)

                name_1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name_1,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub1['subnet']['id'],
                                              insub2['subnet']['id']]}
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name_1, esnat['name'])
                self.assertEqual([insub1['subnet']['id'],
                                  insub2['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                name_2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name_2,
                                  'internal_cidrs': [cidr_3, cidr_4]}
                esnat = self.elastic_snat_plugin.update_elastic_snat(
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name_2, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([cidr_3, cidr_4], esnat['internal_cidrs'])

    def test_update_elastic_snat_from_cidrs_to_subnets(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        cidr_1 = '10.0.0.0/24'
        cidr_2 = '********/24'
        cidr_3 = '********/24'
        cidr_4 = '********/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=cidr_1) as insub1, \
                 self.subnet(network=innet, cidr=cidr_2) as insub2, \
                 self.subnet(network=innet, cidr=cidr_3) as insub3, \
                 self.subnet(network=innet, cidr=cidr_4) as insub4, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub1['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              insub2['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              insub3['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              insub4['subnet']['id'], None)

                name_1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name_1,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [cidr_1, cidr_2]}
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name_1, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([cidr_1, cidr_2], esnat['internal_cidrs'])

                name_2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name_2,
                                  'subnets': [insub3['subnet']['id'],
                                              insub4['subnet']['id']]}
                esnat = self.elastic_snat_plugin.update_elastic_snat(
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})
                self.assertEqual(name_2, esnat['name'])
                self.assertEqual([insub3['subnet']['id'],
                                  insub4['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

    def test_update_elastic_snat_with_subnets_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])
                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}
                elastic_snat_2 = {'name': name,
                                  'subnets': [insub['subnet']['id'],
                                              insub['subnet']['id']]}

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.update_elastic_snat,
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_update_elastic_snat_with_internal_cidrs_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', False)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr], esnat['internal_cidrs'])

                elastic_snat_2 = {'name': name,
                                  'internal_cidrs': [internal_cidr,
                                                     '10.0.0.0/30']}
                self.assertRaises(
                    exceptions.FailedToCreateElasticSnatCIDROverlaps,
                    self.elastic_snat_plugin.update_elastic_snat,
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_update_elastic_snat_allow_subnets_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}
                elastic_snat_2 = {'name': name,
                                  'internal_cidrs': [internal_cidr,
                                                     '10.0.0.0/30']}

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])
                self.elastic_snat_plugin.update_elastic_snat(
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_update_elastic_snat_allow_internal_cidrs_overlaps(self):
        cfg.CONF.set_override('allow_elastic_snat_cidrs_overlapping', True)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip1 = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip1['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [internal_cidr]}
                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([internal_cidr], esnat['internal_cidrs'])

                elastic_snat_2 = {'name': name,
                                  'internal_cidrs': [internal_cidr,
                                                     '10.0.0.0/30']}
                esnat = self.elastic_snat_plugin.update_elastic_snat(
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_update_elastic_snat_with_esnat_rules_per_router_exceeded(self):
        cfg.CONF.set_override("max_esnat_rules_per_router", 2)
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        cidr_1 = '10.0.0.0/24'
        cidr_1_1 = '********'
        cidr_1_2 = '********'
        cidr_1_3 = '********'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=cidr_1) as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                name_1 = "elastic_snat_1"
                elastic_snat_1 = {'name': name_1,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'internal_cidrs': [cidr_1_1, cidr_1_2]}
                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name_1, esnat['name'])
                self.assertEqual([], esnat['subnets'])
                self.assertEqual([cidr_1_1, cidr_1_2], esnat['internal_cidrs'])

                name_2 = "elastic_snat_2"
                elastic_snat_2 = {'name': name_2,
                                  'internal_cidrs': [cidr_1_1,
                                                     cidr_1_2,
                                                     cidr_1_3]}
                self.assertRaises(
                    exceptions.FailedToCreateOrUpdateElasticSnat,
                    self.elastic_snat_plugin.update_elastic_snat,
                    ctx, esnat['id'], {api_def.RESOURCE_NAME: elastic_snat_2})

    def test_get_elastic_snat(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                esnat = self.elastic_snat_plugin.get_elastic_snat(
                    ctx, esnat['id'])
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([internal_cidr], esnat['internal_cidrs'])

    def test_get_elastic_snat_not_found(self):
        ctx = context.get_admin_context()
        self.assertRaises(
            exceptions.ElasticSnatNotFound,
            self.elastic_snat_plugin.get_elastic_snat,
            ctx, _uuid())

    def test_get_elastic_snats(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                esnats = self.elastic_snat_plugin.get_elastic_snats(ctx)
                self.assertEqual(name, esnats[0]['name'])
                self.assertEqual([insub['subnet']['id']], esnats[0]['subnets'])
                self.assertEqual([internal_cidr], esnats[0]['internal_cidrs'])

    def test_get_elastic_snats_filter_not_support(self):
        ctx = context.get_admin_context()
        self.assertRaises(
            exceptions.ElasticSnatNotSupportFilterField,
            self.elastic_snat_plugin.get_elastic_snats,
            ctx, filters={"subnets": [_uuid()]})

    def test_delete_elastic_snat(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr='10.0.0.0/24') as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self.elastic_snat_plugin.delete_elastic_snat(
                    ctx, esnat['id'])
                esnats = self.elastic_snat_plugin.get_elastic_snats(ctx)
                self.assertEqual([], esnats)

    def test_sync_elastic_snat_info(self):
        ctx = context.get_admin_context()
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        internal_cidr = '10.0.0.0/24'
        with self.network(**kwargs) as extnet, self.network() as innet:
            with self.subnet(network=extnet, cidr='200.0.0.0/22'), \
                 self.subnet(network=innet, cidr=internal_cidr) as insub, \
                    self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                name = "elastic_snat_1"
                elastic_snat_1 = {'name': name,
                                  'floatingip_id': fip['floatingip']['id'],
                                  'router_id': router['router']['id'],
                                  'subnets': [insub['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              insub['subnet']['id'], None)

                esnat = self.elastic_snat_plugin.create_elastic_snat(
                    ctx, {api_def.RESOURCE_NAME: elastic_snat_1})
                self.assertEqual(name, esnat['name'])
                self.assertEqual([insub['subnet']['id']], esnat['subnets'])
                self.assertEqual([], esnat['internal_cidrs'])

                self.elastic_snat_plugin.sync_elastic_snat_info(
                    ctx, [router['router']])
                rules = router['router']['_elastic_snat_rules']
                self.assertEqual(name, rules[0]['name'])
                self.assertEqual([insub['subnet']['id']], rules[0]['subnets'])
                self.assertEqual([internal_cidr], rules[0]['internal_cidrs'])
                fip_ids = router['router']['fip_managed_by_elastic_snats']
                self.assertEqual([fip['floatingip']['id']], fip_ids)
                fips = router['router']['_elastic_snat_floatingips']
                self.assertEqual(
                    fip['floatingip']['floating_ip_address'],
                    fips[0]['floating_ip_address'])
                fip_cidrs = router['router']['elastic_snat_fip_set']
                self.assertEqual(
                    ['%s/32' % fip['floatingip']['floating_ip_address']],
                    fip_cidrs)
