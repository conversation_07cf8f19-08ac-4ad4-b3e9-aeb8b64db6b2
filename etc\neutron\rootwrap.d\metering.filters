# neutron-rootwrap command filters for nodes on which neutron is
# expected to control network
#
# This file should be owned by (and only-writeable by) the root user

# format seems to be
# cmd-name: filter-name, raw-command, user, args

[Filters]
# ip_lib
ip: IpFilter, ip, root
ip_exec: IpNetnsExecFilter, ip, root
ifconfig: CommandFilter, ifconfig, root
cat: CommandFilter, cat, root
ping: CommandFilter, ping, root
ping6: CommandFilter, ping6, root
fping: CommandFilter, fping, root
fping6: CommandFilter, fping6, root
route: CommandFilter, route, root
neutron-conntrack-check: CommandFilter, neutron-conntrack-check, root
