#. Source the administrative project credentials.
#. Verify creation of the internal high-availability network that handles
   VRRP *heartbeat* traffic.

   .. code-block:: console

      $ openstack network list
      +--------------------------------------+----------------------------------------------------+--------------------------------------+
      | ID                                   | Name                                               | Subnets                              |
      +--------------------------------------+----------------------------------------------------+--------------------------------------+
      | 1b8519c1-59c4-415c-9da2-a67d53c68455 | HA network tenant f986edf55ae945e2bef3cb4bfd589928 | 6843314a-1e76-4cc9-94f5-c64b7a39364a |
      +--------------------------------------+----------------------------------------------------+--------------------------------------+

#. On each network node, verify creation of a ``qrouter`` namespace with
   the same ID.

   Network node 1:

   .. code-block:: console

      # ip netns
      qrouter-b6206312-878e-497c-8ef7-eb384f8add96

   Network node 2:

   .. code-block:: console

      # ip netns
      qrouter-b6206312-878e-497c-8ef7-eb384f8add96

   .. note::

      The namespace for router 1 from :ref:`deploy-lb-selfservice` should
      only appear on network node 1 because of creation prior to enabling
      VRRP.

#. On each network node, show the IP address of interfaces in the ``qrouter``
   namespace. With the exception of the VRRP interface, only one namespace
   belonging to the master router instance contains IP addresses on the
   interfaces.

   Network node 1:

   .. code-block:: console

      # ip netns exec qrouter-b6206312-878e-497c-8ef7-eb384f8add96 ip addr show
      1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1
          link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
          inet 127.0.0.1/8 scope host lo
             valid_lft forever preferred_lft forever
          inet6 ::1/128 scope host
             valid_lft forever preferred_lft forever
      2: ha-eb820380-40@if21: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:78:ba:99 brd ff:ff:ff:ff:ff:ff link-netnsid 0
          inet *************/18 brd *************** scope global ha-eb820380-40
             valid_lft forever preferred_lft forever
          inet ***********/24 scope global ha-eb820380-40
             valid_lft forever preferred_lft forever
          inet6 fe80::f816:3eff:fe78:ba99/64 scope link
             valid_lft forever preferred_lft forever
      3: qr-da3504ad-ba@if24: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:dc:8e:a8 brd ff:ff:ff:ff:ff:ff link-netnsid 0
          inet ************/24 scope global qr-da3504ad-ba
             valid_lft forever preferred_lft forever
          inet6 fe80::f816:3eff:fedc:8ea8/64 scope link
             valid_lft forever preferred_lft forever
      4: qr-442e36eb-fc@if27: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:ee:c8:41 brd ff:ff:ff:ff:ff:ff link-netnsid 0
          inet6 fd00:198:51:100::1/64 scope global nodad
             valid_lft forever preferred_lft forever
          inet6 fe80::f816:3eff:feee:c841/64 scope link
             valid_lft forever preferred_lft forever
      5: qg-33fedbc5-43@if28: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:03:1a:f6 brd ff:ff:ff:ff:ff:ff link-netnsid 0
          inet ************/24 scope global qg-33fedbc5-43
             valid_lft forever preferred_lft forever
          inet6 fd00:203:0:113::21/64 scope global nodad
             valid_lft forever preferred_lft forever
          inet6 fe80::f816:3eff:fe03:1af6/64 scope link
             valid_lft forever preferred_lft forever

   Network node 2:

   .. code-block:: console

      # ip netns exec qrouter-b6206312-878e-497c-8ef7-eb384f8add96 ip addr show
      1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1
          link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
          inet 127.0.0.1/8 scope host lo
             valid_lft forever preferred_lft forever
          inet6 ::1/128 scope host
             valid_lft forever preferred_lft forever
      2: ha-7a7ce184-36@if8: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:16:59:84 brd ff:ff:ff:ff:ff:ff link-netnsid 0
          inet *************/18 brd *************** scope global ha-7a7ce184-36
             valid_lft forever preferred_lft forever
          inet6 fe80::f816:3eff:fe16:5984/64 scope link
             valid_lft forever preferred_lft forever
      3: qr-da3504ad-ba@if11: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:dc:8e:a8 brd ff:ff:ff:ff:ff:ff link-netnsid 0
      4: qr-442e36eb-fc@if14: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UP group default qlen 1000
      5: qg-33fedbc5-43@if15: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP group default qlen 1000
          link/ether fa:16:3e:03:1a:f6 brd ff:ff:ff:ff:ff:ff link-netnsid 0

   .. note::

      The master router may reside on network node 2.

#. Launch an instance with an interface on the additional self-service network.
   For example, a CirrOS image using flavor ID 1.

   .. code-block:: console

      $ openstack server create --flavor 1 --image cirros --nic net-id=NETWORK_ID selfservice-instance2

   Replace ``NETWORK_ID`` with the ID of the additional self-service
   network.

#. Determine the IPv4 and IPv6 addresses of the instance.

   .. code-block:: console

      $ openstack server list
      +--------------------------------------+-----------------------+--------+---------------------------------------------------------------------------+
      | ID                                   | Name                  | Status | Networks                                                                  |
      +--------------------------------------+-----------------------+--------+---------------------------------------------------------------------------+
      | bde64b00-77ae-41b9-b19a-cd8e378d9f8b | selfservice-instance2 | ACTIVE | selfservice2=fd00:198:51:100:f816:3eff:fe71:e93e, ************            |
      +--------------------------------------+-----------------------+--------+---------------------------------------------------------------------------+

#. Create a floating IPv4 address on the provider network.

   .. code-block:: console

      $ openstack floating ip create provider1
      +-------------+--------------------------------------+
      | Field       | Value                                |
      +-------------+--------------------------------------+
      | fixed_ip    | None                                 |
      | id          | 0174056a-fa56-4403-b1ea-b5151a31191f |
      | instance_id | None                                 |
      | ip          | ************                         |
      | pool        | provider1                            |
      +-------------+--------------------------------------+

#. Associate the floating IPv4 address with the instance.

   .. code-block:: console

      $ openstack server add floating ip selfservice-instance2 ************

   .. note::

       This command provides no output.
