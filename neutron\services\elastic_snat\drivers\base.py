#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from oslo_log import log as logging

LOG = logging.getLogger(__name__)


class DriverBase(object):

    def __init__(self, name):
        self.name = name

    def create_elastic_snat(self, context, elastic_snat):
        """Create an elastic_snat.

        This method can be implemented by the specific driver subclass
        to update the backend where necessary with a specific
        elastic_snat object.

        :param context: current running context information
        :param elastic_snat: a elastic_snat objects being created

        """

    def update_elastic_snat(self, context, elastic_snat):
        """Update an elastic_snat.

        This method can be implemented by the specific driver subclass
        to update the backend where necessary with a specific
        elastic_snat object.

        :param context: current running context information
        :param elastic_snat: a elastic_snat objects being updated

        """

    def delete_elastic_snat(self, context, elastic_snat):
        """Delete an elastic_snat.

        This method can be implemented by the specific driver subclass
        to update the backend where necessary with a specific
        elastic_snat object.

        :param context: current running context information
        :param elastic_snat: a elastic_snat objects being deleted

        """
