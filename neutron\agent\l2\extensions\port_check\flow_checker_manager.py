#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from oslo_config import cfg

from neutron.agent.l2.extensions.port_check.flow_checker import dhcp
from neutron.agent.l2.extensions.port_check.flow_checker import metadata
from neutron.agent.l2.extensions.port_check.flow_checker import ra_speaker
from neutron.agent.l2.extensions.port_check.flow_checker import \
    service_datapath as sdp_ext


class ExtensionsFlowCheckManager(object):
    def __init__(self, br_int, phy_br=None, meta_br=None, agent_api=None):
        self.extensions = []
        if 'ra_speaker' in cfg.CONF.agent.extensions:
            self.extensions.append(
                ra_speaker.RASpeakerFlowCheck(br_int))
        if 'dhcp' in cfg.CONF.agent.extensions:
            self.extensions.append(
                dhcp.DHCPAgentFlowCheck(br_int, agent_api))
        if 'metadata_path' in cfg.CONF.agent.extensions:
            self.extensions.append(
                metadata.MetadataPathFlowCheck(br_int, meta_br, agent_api))
        if 'service_datapath' in cfg.CONF.agent.extensions:
            self.extensions.append(
                sdp_ext.ServiceDatapathFlowCheck(br_int, phy_br, agent_api))

    def do_extensions_flow_check(self, context, result_map, ports):
        for ext in self.extensions:
            ext.do_check(context, result_map, ports)
