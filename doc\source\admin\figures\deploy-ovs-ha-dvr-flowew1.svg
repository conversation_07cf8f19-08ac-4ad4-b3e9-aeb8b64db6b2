<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 454 670" width="454pt" height="670pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 17:54:05 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><path d="M 36.346457 340.15748 L 417.19685 340.15748 C 421.61513 340.15748 425.19685 343.7392 425.19685 348.15748 L 425.19685 570.2677 C 425.19685 574.686 421.61513 578.2677 417.19685 578.2677 L 36.346457 578.2677 C 31.928179 578.2677 28.346457 574.686 28.346457 570.2677 L 28.346457 348.15748 C 28.346457 343.7392 31.928179 340.15748 36.346457 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 345.15748)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="145.85293" y="13" textLength="95.14453">Compute Node 2</tspan></text><path d="M 36.346457 56.692913 L 417.19685 56.692913 C 421.61513 56.692913 425.19685 60.274635 425.19685 64.692913 L 425.19685 286.80315 C 425.19685 291.22143 421.61513 294.80315 417.19685 294.80315 L 36.346457 294.80315 C 31.928179 294.80315 28.346457 291.22143 28.346457 286.80315 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="145.85293" y="13" textLength="95.14453">Compute Node 1</tspan></text><text transform="translate(86.85039 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".3515625" y="17" textLength="285.03906">Open vSwitch - High-availability with D</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x="285.23438" y="17" textLength="19.414062">VR</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="32.204102" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="89.157227" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="93.81543" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="111.674805" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="136.74121" y="35" textLength="58.253906">w - East/W</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="194.75488" y="35" textLength="78.041016">est Scenario 1</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.6003628" y="9" textLength="39.492188">Instance 1</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" fill="#fdf5dd"/><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="127.559054" x2="170.07874" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="127.559054" x2="212.59842" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="127.559054" x2="255.11811" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" fill="#2076c8"/><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" fill="#2076c8"/><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><g filter="url(#Shadow)"><path d="M 319.81102 85.03937 L 403.02362 85.03937 C 407.4419 85.03937 411.02362 88.62109 411.02362 93.03937 L 411.02362 181.92126 C 411.02362 186.33954 407.4419 189.92126 403.02362 189.92126 L 319.81102 189.92126 C 315.39274 189.92126 311.81102 186.33954 311.81102 181.92126 L 311.81102 93.03937 C 311.81102 88.62109 315.39274 85.03937 319.81102 85.03937 Z" fill="#fdf5dd"/><path d="M 319.81102 85.03937 L 403.02362 85.03937 C 407.4419 85.03937 411.02362 88.62109 411.02362 93.03937 L 411.02362 181.92126 C 411.02362 186.33954 407.4419 189.92126 403.02362 189.92126 L 319.81102 189.92126 C 315.39274 189.92126 311.81102 186.33954 311.81102 181.92126 L 311.81102 93.03937 C 311.81102 88.62109 315.39274 85.03937 319.81102 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(316.81102 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".73325205" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.952002" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.416846" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="35.217139" y="18" textLength="18.77832">br-int</tspan></text></g><line x1="255.11811" y1="127.559054" x2="325.98425" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><circle cx="184.25197" cy="609.4488" r="8.5039505" fill="#2076c8"/><text transform="translate(194.92126 597.44094)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(54.06299 596.4567)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="42.519685" cy="609.4488" r="8.5039505" fill="#a57706"/><g filter="url(#Shadow)"><path d="M 319.81102 212.59842 L 403.02362 212.59842 C 407.4419 212.59842 411.02362 216.18015 411.02362 220.59842 L 411.02362 281.13386 C 411.02362 285.55213 407.4419 289.13386 403.02362 289.13386 L 319.81102 289.13386 C 315.39274 289.13386 311.81102 285.55213 311.81102 281.13386 L 311.81102 220.59842 C 311.81102 216.18015 315.39274 212.59842 319.81102 212.59842 Z" fill="#fdf5dd"/><path d="M 319.81102 212.59842 L 403.02362 212.59842 C 407.4419 212.59842 411.02362 216.18015 411.02362 220.59842 L 411.02362 281.13386 C 411.02362 285.55213 407.4419 289.13386 403.02362 289.13386 L 319.81102 289.13386 C 315.39274 289.13386 311.81102 285.55213 311.81102 281.13386 L 311.81102 220.59842 C 311.81102 216.18015 315.39274 212.59842 319.81102 212.59842 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(316.81102 217.59842)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.6160645" y="9" textLength="74.058594">Distributed Router </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.979346" y="20" textLength="45.253906">Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="31.725684" y="29" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.964942" y="29" textLength="18.521973">outer</tspan></text></g><path d="M 340.15748 269.29134 C 340.15748 269.29134 317.48031 240.70585 317.48031 212.59842 C 317.48031 184.491 340.15748 170.07874 340.15748 170.07874" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 382.67716 269.29134 C 382.67716 269.29134 405.35433 240.70585 405.35433 212.59842 C 405.35433 184.491 382.67716 170.07874 382.67716 170.07874" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 333.98425 255.11811 L 346.3307 255.11811 C 350.74898 255.11811 354.3307 258.69983 354.3307 263.11811 L 354.3307 275.46457 C 354.3307 279.88284 350.74898 283.46457 346.3307 283.46457 L 333.98425 283.46457 C 329.56597 283.46457 325.98425 279.88284 325.98425 275.46457 L 325.98425 263.11811 C 325.98425 258.69983 329.56597 255.11811 333.98425 255.11811 Z" fill="#2076c8"/><path d="M 333.98425 255.11811 L 346.3307 255.11811 C 350.74898 255.11811 354.3307 258.69983 354.3307 263.11811 L 354.3307 275.46457 C 354.3307 279.88284 350.74898 283.46457 346.3307 283.46457 L 333.98425 283.46457 C 329.56597 283.46457 325.98425 279.88284 325.98425 275.46457 L 325.98425 263.11811 C 325.98425 258.69983 329.56597 255.11811 333.98425 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><g filter="url(#Shadow)"><path d="M 376.50393 255.11811 L 388.8504 255.11811 C 393.26867 255.11811 396.8504 258.69983 396.8504 263.11811 L 396.8504 275.46457 C 396.8504 279.88284 393.26867 283.46457 388.8504 283.46457 L 376.50393 283.46457 C 372.08566 283.46457 368.50393 279.88284 368.50393 275.46457 L 368.50393 263.11811 C 368.50393 258.69983 372.08566 255.11811 376.50393 255.11811 Z" fill="#5959b7"/><path d="M 376.50393 255.11811 L 388.8504 255.11811 C 393.26867 255.11811 396.8504 258.69983 396.8504 263.11811 L 396.8504 275.46457 C 396.8504 279.88284 393.26867 283.46457 388.8504 283.46457 L 376.50393 283.46457 C 372.08566 283.46457 368.50393 279.88284 368.50393 275.46457 L 368.50393 263.11811 C 368.50393 258.69983 372.08566 255.11811 376.50393 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(373.50393 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 155.90551 L 346.3307 155.90551 C 350.74898 155.90551 354.3307 159.48723 354.3307 163.90551 L 354.3307 176.25197 C 354.3307 180.67025 350.74898 184.25197 346.3307 184.25197 L 333.98425 184.25197 C 329.56597 184.25197 325.98425 180.67025 325.98425 176.25197 L 325.98425 163.90551 C 325.98425 159.48723 329.56597 155.90551 333.98425 155.90551 Z" fill="#2076c8"/><path d="M 333.98425 155.90551 L 346.3307 155.90551 C 350.74898 155.90551 354.3307 159.48723 354.3307 163.90551 L 354.3307 176.25197 C 354.3307 180.67025 350.74898 184.25197 346.3307 184.25197 L 333.98425 184.25197 C 329.56597 184.25197 325.98425 180.67025 325.98425 176.25197 L 325.98425 163.90551 C 325.98425 159.48723 329.56597 155.90551 333.98425 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 164.57874)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 376.50393 155.90551 L 388.8504 155.90551 C 393.26867 155.90551 396.8504 159.48723 396.8504 163.90551 L 396.8504 176.25197 C 396.8504 180.67025 393.26867 184.25197 388.8504 184.25197 L 376.50393 184.25197 C 372.08566 184.25197 368.50393 180.67025 368.50393 176.25197 L 368.50393 163.90551 C 368.50393 159.48723 372.08566 155.90551 376.50393 155.90551 Z" fill="#5959b7"/><path d="M 376.50393 155.90551 L 388.8504 155.90551 C 393.26867 155.90551 396.8504 159.48723 396.8504 163.90551 L 396.8504 176.25197 C 396.8504 180.67025 393.26867 184.25197 388.8504 184.25197 L 376.50393 184.25197 C 372.08566 184.25197 368.50393 180.67025 368.50393 176.25197 L 368.50393 163.90551 C 368.50393 159.48723 372.08566 155.90551 376.50393 155.90551 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(373.50393 164.57874)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><circle cx="42.519685" cy="637.79527" r="8.5039505" fill="#5959b7"/><text transform="translate(54.574802 625.7874)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="101.94824">Self-service network 2</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 102, 192.168.2.0/24</tspan></text><text transform="translate(391.1244 293.057) rotate(8.9726266)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#595ab7" x=".087890625" y="20" textLength="28.824219">VNI 102</tspan></text><g filter="url(#Shadow)"><path d="M 149.73228 170.07874 L 232.94488 170.07874 C 237.36316 170.07874 240.94488 173.66046 240.94488 178.07874 L 240.94488 224.44094 C 240.94488 228.85922 237.36316 232.44094 232.94488 232.44094 L 149.73228 232.44094 C 145.314005 232.44094 141.73228 228.85922 141.73228 224.44094 L 141.73228 178.07874 C 141.73228 173.66046 145.314005 170.07874 149.73228 170.07874 Z" fill="#fdf5dd"/><path d="M 149.73228 170.07874 L 232.94488 170.07874 C 237.36316 170.07874 240.94488 173.66046 240.94488 178.07874 L 240.94488 224.44094 C 240.94488 228.85922 237.36316 232.44094 232.94488 232.44094 L 149.73228 232.44094 C 145.314005 232.44094 141.73228 228.85922 141.73228 224.44094 L 141.73228 178.07874 C 141.73228 173.66046 145.314005 170.07874 149.73228 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="9.1277833" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.346533" y="9" textLength="15.980469">VS T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="30.928565" y="9" textLength="49.15625">unnel Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="33.97129" y="18" textLength="21.27002">br-tun</tspan></text></g><line x1="382.67716" y1="127.559054" x2="212.59842" y2="212.59842" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="439.37008" y1="311.81102" x2="170.07874" y2="269.29134" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" fill="#708284"/><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><g filter="url(#Shadow)"><path d="M 376.50393 113.385826 L 388.8504 113.385826 C 393.26867 113.385826 396.8504 116.96755 396.8504 121.385826 L 396.8504 133.73228 C 396.8504 138.15056 393.26867 141.73228 388.8504 141.73228 L 376.50393 141.73228 C 372.08566 141.73228 368.50393 138.15056 368.50393 133.73228 L 368.50393 121.385826 C 368.50393 116.96755 372.08566 113.385826 376.50393 113.385826 Z" fill="#708284"/><path d="M 376.50393 113.385826 L 388.8504 113.385826 C 393.26867 113.385826 396.8504 116.96755 396.8504 121.385826 L 396.8504 133.73228 C 396.8504 138.15056 393.26867 141.73228 388.8504 141.73228 L 376.50393 141.73228 C 372.08566 141.73228 368.50393 138.15056 368.50393 133.73228 L 368.50393 121.385826 C 368.50393 116.96755 372.08566 113.385826 376.50393 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(373.50393 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(10)</tspan></text></g><line x1="170.07874" y1="226.77165" x2="170.07874" y2="269.29134" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 163.90551 255.11811 L 176.25197 255.11811 C 180.67025 255.11811 184.25197 258.69983 184.25197 263.11811 L 184.25197 275.46457 C 184.25197 279.88284 180.67025 283.46457 176.25197 283.46457 L 163.90551 283.46457 C 159.48723 283.46457 155.90551 279.88284 155.90551 275.46457 L 155.90551 263.11811 C 155.90551 258.69983 159.48723 255.11811 163.90551 255.11811 Z" fill="#a57706"/><path d="M 163.90551 255.11811 L 176.25197 255.11811 C 180.67025 255.11811 184.25197 258.69983 184.25197 263.11811 L 184.25197 275.46457 C 184.25197 279.88284 180.67025 283.46457 176.25197 283.46457 L 163.90551 283.46457 C 159.48723 283.46457 155.90551 279.88284 155.90551 275.46457 L 155.90551 263.11811 C 155.90551 258.69983 159.48723 255.11811 163.90551 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(13)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 198.4252 L 176.25197 198.4252 C 180.67025 198.4252 184.25197 202.00692 184.25197 206.4252 L 184.25197 218.77165 C 184.25197 223.18993 180.67025 226.77165 176.25197 226.77165 L 163.90551 226.77165 C 159.48723 226.77165 155.90551 223.18993 155.90551 218.77165 L 155.90551 206.4252 C 155.90551 202.00692 159.48723 198.4252 163.90551 198.4252 Z" fill="#a57706"/><path d="M 163.90551 198.4252 L 176.25197 198.4252 C 180.67025 198.4252 184.25197 202.00692 184.25197 206.4252 L 184.25197 218.77165 C 184.25197 223.18993 180.67025 226.77165 176.25197 226.77165 L 163.90551 226.77165 C 159.48723 226.77165 155.90551 223.18993 155.90551 218.77165 L 155.90551 206.4252 C 155.90551 202.00692 159.48723 198.4252 163.90551 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 368.50393 L 91.2126 368.50393 C 95.630876 368.50393 99.2126 372.08566 99.2126 376.50393 L 99.2126 422.86614 C 99.2126 427.28442 95.630876 430.86614 91.2126 430.86614 L 50.519685 430.86614 C 46.101407 430.86614 42.519685 427.28442 42.519685 422.86614 L 42.519685 376.50393 C 42.519685 372.08566 46.101407 368.50393 50.519685 368.50393 Z" fill="#fdf5dd"/><path d="M 50.519685 368.50393 L 91.2126 368.50393 C 95.630876 368.50393 99.2126 372.08566 99.2126 376.50393 L 99.2126 422.86614 C 99.2126 427.28442 95.630876 430.86614 91.2126 430.86614 L 50.519685 430.86614 C 46.101407 430.86614 42.519685 427.28442 42.519685 422.86614 L 42.519685 376.50393 C 42.519685 372.08566 46.101407 368.50393 50.519685 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 373.50393)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="3.6003628" y="9" textLength="39.492188">Instance 2</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 368.50393 L 275.46457 368.50393 C 279.88284 368.50393 283.46457 372.08566 283.46457 376.50393 L 283.46457 422.86614 C 283.46457 427.28442 279.88284 430.86614 275.46457 430.86614 L 149.73228 430.86614 C 145.314005 430.86614 141.73228 427.28442 141.73228 422.86614 L 141.73228 376.50393 C 141.73228 372.08566 145.314005 368.50393 149.73228 368.50393 Z" fill="#fdf5dd"/><path d="M 149.73228 368.50393 L 275.46457 368.50393 C 279.88284 368.50393 283.46457 372.08566 283.46457 376.50393 L 283.46457 422.86614 C 283.46457 427.28442 279.88284 430.86614 275.46457 430.86614 L 149.73228 430.86614 C 145.314005 430.86614 141.73228 427.28442 141.73228 422.86614 L 141.73228 376.50393 C 141.73228 372.08566 145.314005 368.50393 149.73228 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 373.50393)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="411.02362" x2="170.07874" y2="411.02362" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="411.02362" x2="212.59842" y2="411.02362" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="411.02362" x2="255.11811" y2="411.02362" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 396.8504 L 77.03937 396.8504 C 81.45765 396.8504 85.03937 400.43211 85.03937 404.8504 L 85.03937 417.19685 C 85.03937 421.61513 81.45765 425.19685 77.03937 425.19685 L 64.692913 425.19685 C 60.274635 425.19685 56.692913 421.61513 56.692913 417.19685 L 56.692913 404.8504 C 56.692913 400.43211 60.274635 396.8504 64.692913 396.8504 Z" fill="#5959b7"/><path d="M 64.692913 396.8504 L 77.03937 396.8504 C 81.45765 396.8504 85.03937 400.43211 85.03937 404.8504 L 85.03937 417.19685 C 85.03937 421.61513 81.45765 425.19685 77.03937 425.19685 L 64.692913 425.19685 C 60.274635 425.19685 56.692913 421.61513 56.692913 417.19685 L 56.692913 404.8504 C 56.692913 400.43211 60.274635 396.8504 64.692913 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(23)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 412.5461 C 191.51575 411.02362 194.07345 398.20677 204.30504 400.3937 C 205.25431 396.13067 217.15228 396.82261 217.0745 400.3937 C 224.53489 395.82623 234.06882 404.93367 227.67398 409.50113 C 235.34748 411.71556 227.57715 423.64658 221.27953 421.65354 C 220.77553 424.97546 209.51728 426.13795 208.52912 421.65354 C 202.15412 426.44267 188.86121 419.07912 197.92964 412.5461 Z" fill="#5959b7"/><path d="M 197.92964 412.5461 C 191.51575 411.02362 194.07345 398.20677 204.30504 400.3937 C 205.25431 396.13067 217.15228 396.82261 217.0745 400.3937 C 224.53489 395.82623 234.06882 404.93367 227.67398 409.50113 C 235.34748 411.71556 227.57715 423.64658 221.27953 421.65354 C 220.77553 424.97546 209.51728 426.13795 208.52912 421.65354 C 202.15412 426.44267 188.86121 419.07912 197.92964 412.5461 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.7882012" y="9" textLength="14.203125">(21)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 396.8504 L 176.25197 396.8504 C 180.67025 396.8504 184.25197 400.43211 184.25197 404.8504 L 184.25197 417.19685 C 184.25197 421.61513 180.67025 425.19685 176.25197 425.19685 L 163.90551 425.19685 C 159.48723 425.19685 155.90551 421.61513 155.90551 417.19685 L 155.90551 404.8504 C 155.90551 400.43211 159.48723 396.8504 163.90551 396.8504 Z" fill="#5959b7"/><path d="M 163.90551 396.8504 L 176.25197 396.8504 C 180.67025 396.8504 184.25197 400.43211 184.25197 404.8504 L 184.25197 417.19685 C 184.25197 421.61513 180.67025 425.19685 176.25197 425.19685 L 163.90551 425.19685 C 159.48723 425.19685 155.90551 421.61513 155.90551 417.19685 L 155.90551 404.8504 C 155.90551 400.43211 159.48723 396.8504 163.90551 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(22)</tspan></text></g><g filter="url(#Shadow)"><path d="M 319.81102 368.50393 L 403.02362 368.50393 C 407.4419 368.50393 411.02362 372.08566 411.02362 376.50393 L 411.02362 465.38582 C 411.02362 469.8041 407.4419 473.38582 403.02362 473.38582 L 319.81102 473.38582 C 315.39274 473.38582 311.81102 469.8041 311.81102 465.38582 L 311.81102 376.50393 C 311.81102 372.08566 315.39274 368.50393 319.81102 368.50393 Z" fill="#fdf5dd"/><path d="M 319.81102 368.50393 L 403.02362 368.50393 C 407.4419 368.50393 411.02362 372.08566 411.02362 376.50393 L 411.02362 465.38582 C 411.02362 469.8041 407.4419 473.38582 403.02362 473.38582 L 319.81102 473.38582 C 315.39274 473.38582 311.81102 469.8041 311.81102 465.38582 L 311.81102 376.50393 C 311.81102 372.08566 315.39274 368.50393 319.81102 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(316.81102 373.50393)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".73325205" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.952002" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.416846" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="35.217139" y="18" textLength="18.77832">br-int</tspan></text></g><g filter="url(#Shadow)"><path d="M 319.81102 496.063 L 403.02362 496.063 C 407.4419 496.063 411.02362 499.6447 411.02362 504.063 L 411.02362 564.59842 C 411.02362 569.0167 407.4419 572.59842 403.02362 572.59842 L 319.81102 572.59842 C 315.39274 572.59842 311.81102 569.0167 311.81102 564.59842 L 311.81102 504.063 C 311.81102 499.6447 315.39274 496.063 319.81102 496.063 Z" fill="#fdf5dd"/><path d="M 319.81102 496.063 L 403.02362 496.063 C 407.4419 496.063 411.02362 499.6447 411.02362 504.063 L 411.02362 564.59842 C 411.02362 569.0167 407.4419 572.59842 403.02362 572.59842 L 319.81102 572.59842 C 315.39274 572.59842 311.81102 569.0167 311.81102 564.59842 L 311.81102 504.063 C 311.81102 499.6447 315.39274 496.063 319.81102 496.063 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(316.81102 501.063)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.6160645" y="9" textLength="74.058594">Distributed Router </tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="21.979346" y="20" textLength="45.253906">Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="31.725684" y="29" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.964942" y="29" textLength="18.521973">outer</tspan></text></g><path d="M 340.15748 552.7559 C 340.15748 552.7559 317.48031 524.1704 317.48031 496.063 C 317.48031 467.95556 340.15748 453.5433 340.15748 453.5433" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 382.67716 552.7559 C 382.67716 552.7559 405.35433 524.1704 405.35433 496.063 C 405.35433 467.95556 382.67716 453.5433 382.67716 453.5433" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 333.98425 538.58267 L 346.3307 538.58267 C 350.74898 538.58267 354.3307 542.1644 354.3307 546.58267 L 354.3307 558.92913 C 354.3307 563.3474 350.74898 566.92913 346.3307 566.92913 L 333.98425 566.92913 C 329.56597 566.92913 325.98425 563.3474 325.98425 558.92913 L 325.98425 546.58267 C 325.98425 542.1644 329.56597 538.58267 333.98425 538.58267 Z" fill="#5959b7"/><path d="M 333.98425 538.58267 L 346.3307 538.58267 C 350.74898 538.58267 354.3307 542.1644 354.3307 546.58267 L 354.3307 558.92913 C 354.3307 563.3474 350.74898 566.92913 346.3307 566.92913 L 333.98425 566.92913 C 329.56597 566.92913 325.98425 563.3474 325.98425 558.92913 L 325.98425 546.58267 C 325.98425 542.1644 329.56597 538.58267 333.98425 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/></g><g filter="url(#Shadow)"><path d="M 376.50393 538.58267 L 388.8504 538.58267 C 393.26867 538.58267 396.8504 542.1644 396.8504 546.58267 L 396.8504 558.92913 C 396.8504 563.3474 393.26867 566.92913 388.8504 566.92913 L 376.50393 566.92913 C 372.08566 566.92913 368.50393 563.3474 368.50393 558.92913 L 368.50393 546.58267 C 368.50393 542.1644 372.08566 538.58267 376.50393 538.58267 Z" fill="#2076c8"/><path d="M 376.50393 538.58267 L 388.8504 538.58267 C 393.26867 538.58267 396.8504 542.1644 396.8504 546.58267 L 396.8504 558.92913 C 396.8504 563.3474 393.26867 566.92913 388.8504 566.92913 L 376.50393 566.92913 C 372.08566 566.92913 368.50393 563.3474 368.50393 558.92913 L 368.50393 546.58267 C 368.50393 542.1644 372.08566 538.58267 376.50393 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/></g><g filter="url(#Shadow)"><path d="M 333.98425 439.37008 L 346.3307 439.37008 C 350.74898 439.37008 354.3307 442.9518 354.3307 447.37008 L 354.3307 459.71653 C 354.3307 464.1348 350.74898 467.71653 346.3307 467.71653 L 333.98425 467.71653 C 329.56597 467.71653 325.98425 464.1348 325.98425 459.71653 L 325.98425 447.37008 C 325.98425 442.9518 329.56597 439.37008 333.98425 439.37008 Z" fill="#5959b7"/><path d="M 333.98425 439.37008 L 346.3307 439.37008 C 350.74898 439.37008 354.3307 442.9518 354.3307 447.37008 L 354.3307 459.71653 C 354.3307 464.1348 350.74898 467.71653 346.3307 467.71653 L 333.98425 467.71653 C 329.56597 467.71653 325.98425 464.1348 325.98425 459.71653 L 325.98425 447.37008 C 325.98425 442.9518 329.56597 439.37008 333.98425 439.37008 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/></g><g filter="url(#Shadow)"><path d="M 376.50393 439.37008 L 388.8504 439.37008 C 393.26867 439.37008 396.8504 442.9518 396.8504 447.37008 L 396.8504 459.71653 C 396.8504 464.1348 393.26867 467.71653 388.8504 467.71653 L 376.50393 467.71653 C 372.08566 467.71653 368.50393 464.1348 368.50393 459.71653 L 368.50393 447.37008 C 368.50393 442.9518 372.08566 439.37008 376.50393 439.37008 Z" fill="#2076c8"/><path d="M 376.50393 439.37008 L 388.8504 439.37008 C 393.26867 439.37008 396.8504 442.9518 396.8504 447.37008 L 396.8504 459.71653 C 396.8504 464.1348 393.26867 467.71653 388.8504 467.71653 L 376.50393 467.71653 C 372.08566 467.71653 368.50393 464.1348 368.50393 459.71653 L 368.50393 447.37008 C 368.50393 442.9518 372.08566 439.37008 376.50393 439.37008 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/></g><g filter="url(#Shadow)"><path d="M 149.73228 453.5433 L 232.94488 453.5433 C 237.36316 453.5433 240.94488 457.12503 240.94488 461.5433 L 240.94488 507.9055 C 240.94488 512.3238 237.36316 515.9055 232.94488 515.9055 L 149.73228 515.9055 C 145.314005 515.9055 141.73228 512.3238 141.73228 507.9055 L 141.73228 461.5433 C 141.73228 457.12503 145.314005 453.5433 149.73228 453.5433 Z" fill="#fdf5dd"/><path d="M 149.73228 453.5433 L 232.94488 453.5433 C 237.36316 453.5433 240.94488 457.12503 240.94488 461.5433 L 240.94488 507.9055 C 240.94488 512.3238 237.36316 515.9055 232.94488 515.9055 L 149.73228 515.9055 C 145.314005 515.9055 141.73228 512.3238 141.73228 507.9055 L 141.73228 461.5433 C 141.73228 457.12503 145.314005 453.5433 149.73228 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 458.5433)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="9.1277833" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.346533" y="9" textLength="15.980469">VS T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="30.928565" y="9" textLength="49.15625">unnel Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="33.97129" y="18" textLength="21.27002">br-tun</tspan></text></g><line x1="382.67716" y1="411.02362" x2="212.59842" y2="496.063" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 206.4252 481.88976 L 218.77165 481.88976 C 223.18993 481.88976 226.77165 485.47148 226.77165 489.88976 L 226.77165 502.23622 C 226.77165 506.6545 223.18993 510.23622 218.77165 510.23622 L 206.4252 510.23622 C 202.00692 510.23622 198.4252 506.6545 198.4252 502.23622 L 198.4252 489.88976 C 198.4252 485.47148 202.00692 481.88976 206.4252 481.88976 Z" fill="#708284"/><path d="M 206.4252 481.88976 L 218.77165 481.88976 C 223.18993 481.88976 226.77165 485.47148 226.77165 489.88976 L 226.77165 502.23622 C 226.77165 506.6545 223.18993 510.23622 218.77165 510.23622 L 206.4252 510.23622 C 202.00692 510.23622 198.4252 506.6545 198.4252 502.23622 L 198.4252 489.88976 C 198.4252 485.47148 202.00692 481.88976 206.4252 481.88976 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 490.563)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(17)</tspan></text></g><g filter="url(#Shadow)"><path d="M 376.50393 396.8504 L 388.8504 396.8504 C 393.26867 396.8504 396.8504 400.43211 396.8504 404.8504 L 396.8504 417.19685 C 396.8504 421.61513 393.26867 425.19685 388.8504 425.19685 L 376.50393 425.19685 C 372.08566 425.19685 368.50393 421.61513 368.50393 417.19685 L 368.50393 404.8504 C 368.50393 400.43211 372.08566 396.8504 376.50393 396.8504 Z" fill="#708284"/><path d="M 376.50393 396.8504 L 388.8504 396.8504 C 393.26867 396.8504 396.8504 400.43211 396.8504 404.8504 L 396.8504 417.19685 C 396.8504 421.61513 393.26867 425.19685 388.8504 425.19685 L 376.50393 425.19685 C 372.08566 425.19685 368.50393 421.61513 368.50393 417.19685 L 368.50393 404.8504 C 368.50393 400.43211 372.08566 396.8504 376.50393 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(373.50393 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(18)</tspan></text></g><line x1="170.07874" y1="510.23622" x2="170.07874" y2="552.7559" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 163.90551 481.88976 L 176.25197 481.88976 C 180.67025 481.88976 184.25197 485.47148 184.25197 489.88976 L 184.25197 502.23622 C 184.25197 506.6545 180.67025 510.23622 176.25197 510.23622 L 163.90551 510.23622 C 159.48723 510.23622 155.90551 506.6545 155.90551 502.23622 L 155.90551 489.88976 C 155.90551 485.47148 159.48723 481.88976 163.90551 481.88976 Z" fill="#a57706"/><path d="M 163.90551 481.88976 L 176.25197 481.88976 C 180.67025 481.88976 184.25197 485.47148 184.25197 489.88976 L 184.25197 502.23622 C 184.25197 506.6545 180.67025 510.23622 176.25197 510.23622 L 163.90551 510.23622 C 159.48723 510.23622 155.90551 506.6545 155.90551 502.23622 L 155.90551 489.88976 C 155.90551 485.47148 159.48723 481.88976 163.90551 481.88976 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 490.563)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(16)</tspan></text></g><path d="M 439.37008 311.81102 C 439.37008 311.81102 451.41612 401.58283 439.37008 481.88976 C 427.32404 562.1967 473.14197 575.19885 396.8504 595.2756 C 320.5588 615.35232 170.07874 552.7559 170.07874 552.7559" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 424.7013 313.33351 C 418.2874 311.81102 420.8451 298.99417 431.0767 301.1811 C 432.02596 296.91808 443.92393 297.61001 443.84615 301.1811 C 451.30654 296.61364 460.84048 305.72107 454.44563 310.28853 C 462.11913 312.50296 454.3488 324.43398 448.05118 322.44094 C 447.54718 325.76286 436.28893 326.92535 435.30077 322.44094 C 428.92577 327.23008 415.63287 319.86652 424.7013 313.33351 Z" fill="#a57706"/><path d="M 424.7013 313.33351 C 418.2874 311.81102 420.8451 298.99417 431.0767 301.1811 C 432.02596 296.91808 443.92393 297.61001 443.84615 301.1811 C 451.30654 296.61364 460.84048 305.72107 454.44563 310.28853 C 462.11913 312.50296 454.3488 324.43398 448.05118 322.44094 C 447.54718 325.76286 436.28893 326.92535 435.30077 322.44094 C 428.92577 327.23008 415.63287 319.86652 424.7013 313.33351 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(430.4803 306.31102)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="9" textLength="14.203125">(14)</tspan></text><g filter="url(#Shadow)"><path d="M 163.90551 538.58267 L 176.25197 538.58267 C 180.67025 538.58267 184.25197 542.1644 184.25197 546.58267 L 184.25197 558.92913 C 184.25197 563.3474 180.67025 566.92913 176.25197 566.92913 L 163.90551 566.92913 C 159.48723 566.92913 155.90551 563.3474 155.90551 558.92913 L 155.90551 546.58267 C 155.90551 542.1644 159.48723 538.58267 163.90551 538.58267 Z" fill="#a57706"/><path d="M 163.90551 538.58267 L 176.25197 538.58267 C 180.67025 538.58267 184.25197 542.1644 184.25197 546.58267 L 184.25197 558.92913 C 184.25197 563.3474 180.67025 566.92913 176.25197 566.92913 L 163.90551 566.92913 C 159.48723 566.92913 155.90551 563.3474 155.90551 558.92913 L 155.90551 546.58267 C 155.90551 542.1644 159.48723 538.58267 163.90551 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 547.2559)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(15)</tspan></text></g><text transform="translate(308.18727 583.0059) rotate(8.312635)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#595ab7" x=".087890625" y="20" textLength="28.824219">VNI 102</tspan></text><line x1="269.29134" y1="411.02362" x2="340.15748" y2="411.02362" stroke="#5959b7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 396.8504 L 261.29134 396.8504 C 265.70962 396.8504 269.29134 400.43211 269.29134 404.8504 L 269.29134 417.19685 C 269.29134 421.61513 265.70962 425.19685 261.29134 425.19685 L 248.94488 425.19685 C 244.5266 425.19685 240.94488 421.61513 240.94488 417.19685 L 240.94488 404.8504 C 240.94488 400.43211 244.5266 396.8504 248.94488 396.8504 Z" fill="#5959b7"/><path d="M 248.94488 396.8504 L 261.29134 396.8504 C 265.70962 396.8504 269.29134 400.43211 269.29134 404.8504 L 269.29134 417.19685 C 269.29134 421.61513 265.70962 425.19685 261.29134 425.19685 L 248.94488 425.19685 C 244.5266 425.19685 240.94488 421.61513 240.94488 417.19685 L 240.94488 404.8504 C 240.94488 400.43211 244.5266 396.8504 248.94488 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(20)</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 396.8504 L 346.3307 396.8504 C 350.74898 396.8504 354.3307 400.43211 354.3307 404.8504 L 354.3307 417.19685 C 354.3307 421.61513 350.74898 425.19685 346.3307 425.19685 L 333.98425 425.19685 C 329.56597 425.19685 325.98425 421.61513 325.98425 417.19685 L 325.98425 404.8504 C 325.98425 400.43211 329.56597 396.8504 333.98425 396.8504 Z" fill="#5959b7"/><path d="M 333.98425 396.8504 L 346.3307 396.8504 C 350.74898 396.8504 354.3307 400.43211 354.3307 404.8504 L 354.3307 417.19685 C 354.3307 421.61513 350.74898 425.19685 346.3307 425.19685 L 333.98425 425.19685 C 329.56597 425.19685 325.98425 421.61513 325.98425 417.19685 L 325.98425 404.8504 C 325.98425 400.43211 329.56597 396.8504 333.98425 396.8504 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 405.52362)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(19)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" fill="#2076c8"/><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 333.98425 113.385826 L 346.3307 113.385826 C 350.74898 113.385826 354.3307 116.96755 354.3307 121.385826 L 354.3307 133.73228 C 354.3307 138.15056 350.74898 141.73228 346.3307 141.73228 L 333.98425 141.73228 C 329.56597 141.73228 325.98425 138.15056 325.98425 133.73228 L 325.98425 121.385826 C 325.98425 116.96755 329.56597 113.385826 333.98425 113.385826 Z" fill="#2076c8"/><path d="M 333.98425 113.385826 L 346.3307 113.385826 C 350.74898 113.385826 354.3307 116.96755 354.3307 121.385826 L 354.3307 133.73228 C 354.3307 138.15056 350.74898 141.73228 346.3307 141.73228 L 333.98425 141.73228 C 329.56597 141.73228 325.98425 138.15056 325.98425 133.73228 L 325.98425 121.385826 C 325.98425 116.96755 329.56597 113.385826 333.98425 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(330.98425 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g></g></g></svg>
