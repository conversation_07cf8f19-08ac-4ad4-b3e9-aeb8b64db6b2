#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import re

import netaddr
from neutron_lib import constants as n_const
from oslo_log import log as logging
from osprofiler import profiler

from neutron.agent.common import ip_lib
from neutron.agent.common import ovs_lib
from neutron.agent.linux import interface as interface_driver
from neutron.api.rpc.callbacks.consumer import registry
from neutron.api.rpc.callbacks import events
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc
from neutron.common import coordination
from neutron.common import rpc as n_rpc
from neutron.common import utils as n_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common import constants
from neutron.plugins.ml2.drivers.openvswitch.agent import vlanmanager

LOG = logging.getLogger(__name__)


class PFNPort(object):
    def __init__(self, id, ofport, mac, device_owner):
        self.id = id
        self.mac = mac
        self.ofport = ofport
        self.device_owner = device_owner
        # Subnets
        self.subnets = set()
        # Just save port associating flow(in_port, out_port, ip, mac)
        self.flows = []

    def __str__(self):
        return ("OVSPort: id = %s, ofport = %s, mac = %s, "
                "device_owner = %s, subnets = %s, flows = %s" %
                (self.id, self.ofport, self.mac,
                 self.device_owner, self.subnets,
                 self.flows))

    def add_subnet(self, subnet_id):
        self.subnets.add(subnet_id)

    def remove_subnet(self, subnet_id):
        self.subnets.remove(subnet_id)

    def remove_all_subnets(self):
        self.subnets.clear()

    def get_subnets(self):
        return self.subnets

    def add_flow(self, kwargs):
        self.flows.append(kwargs)

    def remove_flow(self, kwargs):
        self.flows.remove(kwargs)

    def remove_all_flows(self):
        self.flows.clear()

    def get_flows(self):
        return self.flows

    def get_device_owner(self):
        return self.device_owner

    def get_mac(self):
        return self.mac

    def get_ofport(self):
        return self.ofport


@profiler.trace_cls("ovs_pfn_agent")
class OVSPFNNeutronAgent(object):
    '''
        Implements OVS-based private floating network.
    '''
    # history
    #   1.0 Initial version

    REQUIRED_PROTOCOLS = [
        constants.OPENFLOW10,
        constants.OPENFLOW11,
        constants.OPENFLOW12,
        constants.OPENFLOW13,
        constants.OPENFLOW14,
    ]

    def __init__(self, context, plugin_rpc, conf, agent_id, int_br, tun_br,
                 bridge_mappings, phys_brs, int_ofports, phys_ofports,
                 patch_int_ofport=constants.OFPORT_INVALID,
                 patch_tun_ofport=constants.OFPORT_INVALID,
                 host=None, enable_tunneling=False,
                 bridge_vlan_mappings=None,
                 phys_vlan_brs=None, int_vlan_ofports=None):
        self.context = context
        self.plugin_rpc = plugin_rpc
        self.conf = conf
        self.agent_id = agent_id
        self.bridge_mappings = bridge_mappings
        self.phys_brs = phys_brs
        self.int_ofports = int_ofports
        self.phys_ofports = phys_ofports
        self.bridge_vlan_mappings = bridge_vlan_mappings
        self.phys_vlan_brs = phys_vlan_brs
        self.int_vlan_ofports = int_vlan_ofports

        self.reset_ovs_parameters(int_br, tun_br,
                                  patch_int_ofport, patch_tun_ofport)

        self.host = host
        self.enable_tunneling = enable_tunneling

        self.privatefloating_info = self.plugin_rpc.get_privatefloating_info(
                self.context, agent_id=self.agent_id, host=self.host,
                availability_zone=self.conf.AGENT.availability_zone)

        LOG.info("[FPN] privatefloating_info: %s", self.privatefloating_info)

        self.pfn_segmentation_id = (
            self.privatefloating_info.get(
                'privatefloating_network', {}).get('provider:segmentation_id'))

        self.enable_private_floating = False
        self.arp_timeout = 300

        if self.privatefloating_info:
            self.enable_private_floating = \
                self.privatefloating_info.get('privatefloating_enable')
            self.arp_timeout = \
                self.privatefloating_info.get('arp_timeout')

        if self.conf.AGENT.enable_process_pfn_flows:
            self._register_rpc_consumers()
        self.enable_firewall = False
        OVS_DRIVERS = [
            'openvswitch',
            'neutron.agent.linux.openvswitch_firewall:OVSFirewallDriver'
        ]
        if self.conf.SECURITYGROUP.firewall_driver in OVS_DRIVERS:
            self.enable_firewall = True

        self.enable_ovs_stateless_firewall = False
        OVS_DRIVERS = [
            'openvswitch_stateless',
            'neutron.agent.linux.openvswitch_firewall:'
            'OVSStatelessFirewallDriver',
        ]
        if self.conf.SECURITYGROUP.firewall_driver in OVS_DRIVERS:
            self.enable_ovs_stateless_firewall = True

        self.reset_pfn_parameters()

        self._deferred = True

    def _register_rpc_consumers(self):
        registry.register(self._handle_notification,
                          resources.SUBNET)
        self._connection = n_rpc.Connection()
        endpoints = [resources_rpc.ResourcesPushRpcCallback()]
        topic = resources_rpc.resource_type_versioned_topic(
            resources.SUBNET)
        self._connection.create_consumer(topic, endpoints, fanout=True)
        self._connection.consume_in_threads()

    def _handle_notification(self, context, resource_type,
                             subnets, event_type):
        if not self.is_privatefloating_enabled():
            return
        if event_type == events.DELETED:
            return
        for subnet in subnets:
            if subnet.network_id == self.privatefloating_network['id']:
                self._process_subnet_event(
                    context, subnet, event_type)

    @coordination.synchronized('{f_name}-{subnet.network_id}')
    def _process_subnet_event(self, context, subnet, event_type):
        net_id = getattr(subnet, 'network_id', None)
        if not net_id:
            return
        if net_id != self.privatefloating_network['id']:
            return
        ipdevice_obj = self.get_pfn_dev(net_id)

        ips = self.get_pfn_port()

        self.vlan_manager = vlanmanager.LocalVlanManager()
        lvm = self.vlan_manager.get(net_id)
        if event_type == events.UPDATED:
            LOG.info("Get subnet %s update notification, "
                     "updating pfn info", subnet)
            subnet_routes = subnet.host_routes
            new_nexthops = set()
            new_destinations = set()
            new_routes = set()
            for route in subnet_routes:
                if self._check_ip_reachable(route['nexthop'], ips):
                    new_nexthops.add(str(route['nexthop']))
                    new_destinations.add(str(route['destination']))
                    new_routes.add((str(route['nexthop']),
                                    str(route['destination'])))

            existing_destinations = []
            existing_routes = set()
            for routes, cidr in self.subnet_routes.values():
                for nexthop, dest in routes:
                    existing_destinations.append(dest)
                    existing_routes.add((str(nexthop), str(dest)))

            # routes removed
            removed_dests = set()
            for dest_ip in self.route_destinations - new_destinations:
                LOG.info("Get subnet %s host route delete notification, "
                         "deleting pfn subnet route info.", subnet)
                if existing_destinations.count(dest_ip) > 1:
                    # more than one routes to same dest_ip
                    continue
                # delete related ovs flows
                for route in existing_routes:
                    if route[1] == dest_ip:
                        next = route[0]
                        self.remove_subnet_route_flows(dest_ip, next)
                        self.apply_flows()

                removed_dests.add(dest_ip)
                ipdevice_obj.route.delete_route(dest_ip)

            # routes added
            for dest_ip in new_destinations - self.route_destinations:
                LOG.info("Get subnet %s host route add notification, "
                         "adding pfn subnet routes.", subnet)
                self.update_subnet_route_and_flows(
                    lvm, dest_ip, new_routes, ipdevice_obj)

            self.route_destinations = (
                    (self.route_destinations | new_destinations
                     ) - removed_dests)
            self.subnet_routes[subnet.id] = (new_routes, subnet.cidr)
            self.route_nexthops = new_nexthops

    def update_subnet_route_and_flows(self, lvm, dest, new_routes,
                                      ipdevice_obj):
        for route in new_routes:
            if route[1] == dest:
                nexthop = route[0]
                ipdevice_obj.route.add_route(dest, nexthop)
                ports = self.plugin_rpc.get_ports_by_vnic_type_and_host(
                    self.context, 'normal', self.host)
                for port in ports:
                    if port['device_owner'] == \
                            n_const.DEVICE_OWNER_PRIVATEFLOATING:
                        vifport = self.int_br.br.get_vif_port_by_id(
                            port['id'])
                        pfnport = PFNPort(
                            vifport.vif_id, vifport.ofport, vifport.vif_mac,
                            n_const.DEVICE_OWNER_PRIVATEFLOATING)
                        self.update_subnet_route_flows(
                            nexthop, dest, lvm, pfnport)
                        self.apply_flows()

    def update_subnet_route_flows(self, nexthop, dest, lvm, pfnport):
        nexthop_value = netaddr.IPAddress(nexthop).value
        col_kwargs = {
            'table': constants.PFN_RULES_ROUTE_EGRESS_TABLE,
            'proto': 'ip',
            'priority': 20,
            'in_port': self.pfip_physnet_ofport,
            'nw_dst': dest,
            'actions': "load:%s->reg2, resubmit(,%d)" % (
                "0x{:04x}".format(nexthop_value),
                constants.PFN_RULES_EGRESS_TABLE
            )
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

        col_kwargs = {
            'table': constants.PFN_RULES_ROUTE_EGRESS_TABLE,
            'proto': 'ip',
            'priority': 10,
            'nw_dst': dest,
            'actions': ("load:%s->reg2,mod_vlan_vid:%d,resubmit(,%d)") % (
                "0x{:04x}".format(nexthop_value),
                lvm.vlan,
                constants.PFN_RULES_EGRESS_TABLE)
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)
        # Inter-node case end

        action = ("resubmit(,%d)" % (
            constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
                  if self.conf.AGENT.explicitly_egress_direct else "NORMAL")

        # arp response in integrate bridge
        col_kwargs = {
            'table': constants.PFN_ARP_RESPONSER_TABLE,
            'priority': 20,
            'in_port': self.pfip_physnet_ofport,
            'proto': 'arp',
            'arp_op': 2,
            'arp_spa': nexthop,
            'actions': "learn(table=%d,priority=10,hard_timeout=%d,"
                       "fin_idle_timeout=60,fin_hard_timeout=%d,"
                       "reg2=%s,load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                       "load:0x%s->NXM_OF_ETH_SRC[],"
                       "load:%d->OXM_OF_VLAN_VID[],"
                       "load:%d->NXM_OF_IN_PORT[],"
                       "output:NXM_OF_IN_PORT[]),%s" % (
                           constants.PFN_EGRESS_TRAFFIC_TABLE,
                           self.arp_timeout,
                           self.arp_timeout,
                           "0x{:04x}".format(nexthop_value),
                           self.ns_privatefloating_mac.replace(':', ''),
                           lvm.vlan,
                           pfnport.ofport,
                           action
                       )
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

        col_kwargs = {
            'table': constants.PFN_ARP_RESPONSER_TABLE,
            'priority': 10,
            'proto': 'arp',
            'arp_op': 2,
            'arp_spa': nexthop,
            'actions': "learn(table=%d,priority=10,hard_timeout=%d,"
                       "fin_idle_timeout=60,fin_hard_timeout=%d,"
                       "reg2=%s,load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                       "load:0x%s->NXM_OF_ETH_SRC[],"
                       "load:%d->OXM_OF_VLAN_VID[],"
                       "load:%d->NXM_OF_IN_PORT[],"
                       "output:NXM_OF_IN_PORT[]),%s" % (
                           constants.PFN_EGRESS_TRAFFIC_TABLE,
                           self.arp_timeout,
                           self.arp_timeout,
                           "0x{:04x}".format(nexthop_value),
                           self.ns_privatefloating_mac.replace(':', ''),
                           lvm.vlan,
                           self.pfip_physnet_ofport,
                           action
                       )
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

    def get_pfn_dev(self, net_id):
        ns_name = "pfip-%s" % net_id
        port_id = self.ns_privatefloating_port.get('id')
        tap_name = "tap%s" % port_id
        tap_name = tap_name[:14]
        ipdevice_obj = ip_lib.IPDevice(tap_name, ns_name)
        return ipdevice_obj

    def get_pfn_port(self):
        ips = []
        for fixed_ip in self.ns_privatefloating_port.get('fixed_ips', []):
            if fixed_ip['subnet_id'] in self.privatefloating_subnet_dict:
                cidr = (self.privatefloating_subnet_dict[
                    fixed_ip['subnet_id']]['cidr'])
                mask = cidr[cidr.rindex('/') + 1:len(cidr)]
                ip = '%s/%s' % (fixed_ip['ip_address'], mask)
                ips.append(ip)
        return ips

    def remove_subnet_route_flows(self, dest_ip, next):
        col_kwargs = {
            'table': constants.PFN_RULES_ROUTE_EGRESS_TABLE,
            'proto': 'ip',
            'nw_dst': dest_ip
        }
        self._delete_flows(**col_kwargs)

        col_kwargs = {
            'table': constants.PFN_ARP_RESPONSER_TABLE,
            'proto': 'arp',
            'arp_spa': next,
            'arp_op': 2
        }
        self._delete_flows(**col_kwargs)

    def reset_ovs_parameters(self, int_br, tun_br,
                             patch_int_ofport, patch_tun_ofport):
        '''Reset the openvswitch parameters'''
        self.int_br = self.initialize_bridge(int_br)
        self.tun_br = tun_br
        self.patch_int_ofport = patch_int_ofport
        self.patch_tun_ofport = patch_tun_ofport
        # Normally, patch_int_ofport is pfip_physnet_ofport
        self.pfip_physnet_ofport = constants.OFPORT_INVALID

    def reset_pfn_parameters(self):
        '''Reset the PFN parameters'''
        self.local_ports = {}
        self.privatefloating_subnets = {}
        self.ns_privatefloating_port = {}
        self.privatefloating_network = {}
        self.privatefloating_subnet_dict = {}
        self.ns_privatefloating_mac = ''
        self._update_cookie = None

    def is_privatefloating_enabled(self):
        return self.enable_private_floating

    def _check_pfn_physnet_ofport(self):
        if self.pfip_physnet_ofport == ovs_lib.INVALID_OFPORT:
            if self.privatefloating_network:
                physnet = self.privatefloating_network.get(
                    'provider:physical_network')
                self.pfip_physnet_ofport = self.int_ofports[physnet]
                if self.bridge_vlan_mappings:
                    vlan_range = n_utils.is_segment_id_in_vlan_range(
                        self.pfn_segmentation_id, self.bridge_vlan_mappings)
                    if vlan_range:
                        phys_br = n_utils.get_bridge_by_vlan_range(
                            self.phys_vlan_brs, vlan_range)
                        int_port = self.int_vlan_ofports[phys_br.br_name]
                        self.pfip_physnet_ofport = int_port

    @staticmethod
    def initialize_bridge(int_br):
        int_br.add_protocols(*OVSPFNNeutronAgent.REQUIRED_PROTOCOLS)
        return int_br.deferred(full_ordered=True, use_bundle=True)

    def _add_flow(self, **kwargs):
        if self._update_cookie:
            kwargs['cookie'] = self._update_cookie

        LOG.debug("[FPN] add flow: %s ", kwargs)
        if self._deferred:
            self.int_br.add_flow(**kwargs)
        else:
            self.int_br.br.add_flow(**kwargs)

    def apply_flows(self):
        if self._deferred:
            self.int_br.apply_flows()

    def _delete_flows(self, **kwargs):
        new_kwargs = {}

        # Filter invalid item
        for key, value in kwargs.items():
            if key != 'priority':
                if key == 'actions':
                    # Exclude () content
                    valid_action = re.sub("\\(.*?\\)", "", value)
                    for action in valid_action.split(","):
                        pattern = r'output:'
                        match_obj = re.match(pattern, action)
                        if match_obj is not None:
                            port = re.sub(pattern, "", action)
                            new_kwargs['out_port'] = port
                else:
                    new_kwargs[key] = value

        LOG.debug("[FPN] delete flow: %s ", new_kwargs)

        if self._deferred:
            self.int_br.delete_flows(**new_kwargs)
        else:
            self.int_br.br.delete_flows(**new_kwargs)

    def _init_privatefloating_flows(self):
        '''
            Initialize flows for privatefloating in integrate bridge
        '''
        action = ("resubmit(,%d)" % (
            constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
            if self.conf.AGENT.explicitly_egress_direct else "NORMAL")
        # Default flow for arp responser
        col_kwargs = {
            'table': constants.PFN_ARP_RESPONSER_TABLE,
            'priority': 0,
            'proto': 'arp',
            'actions': action
        }
        self._add_flow(**col_kwargs)

        # Default flow for private floating egress process
        col_kwargs = {
            'table': constants.PFN_BASE_EGRESS_TABLE,
            'priority': 0,
            'actions': action
        }
        self._add_flow(**col_kwargs)

        # Default flow for route
        col_kwargs = {
            'table': constants.PFN_RULES_ROUTE_EGRESS_TABLE,
            'priority': 0,
            'actions': action
        }
        self._add_flow(**col_kwargs)

        # Default flow for egress traffic
        col_kwargs = {
            'table': constants.PFN_EGRESS_TRAFFIC_TABLE,
            'priority': 0,
            'actions': action
        }
        self._add_flow(**col_kwargs)

        # Default flow for ingress traffic
        col_kwargs = {
            'table': constants.PFN_INGRESS_TRAFFIC_TABLE,
            'priority': 0,
            'actions': action
        }
        self._add_flow(**col_kwargs)

        # Default flow for private floating ingress process
        col_kwargs = {
            'table': constants.LOCAL_SWITCHING,
            'priority': 1,
            'actions': "resubmit(,%d)" % (constants.PFN_BASE_INGRESS_TABLE)
        }
        self._add_flow(**col_kwargs)

        col_kwargs = {
            'table': constants.PFN_BASE_INGRESS_TABLE,
            'priority': 0,
            'actions': "resubmit(,%d)" % (constants.DVR_PRE_QOS_TABLE)
        }
        self._add_flow(**col_kwargs)

        # [Egress] Decide process pipeline
        # [Ingress] see _bind_pf_port function
        if self.enable_firewall:
            # Enable ovs firewall case: need to redirect last flow
            # security group egress(accepted traffic) -> privatefloating
            # entrance
            col_kwargs = {
                'table': constants.ACCEPTED_EGRESS_TRAFFIC_TABLE,
                'priority': 5,
                'actions': "resubmit(,%d)" % (constants.PFN_BASE_EGRESS_TABLE)
            }
            self._add_flow(**col_kwargs)

            if not self.conf.AGENT.explicitly_egress_direct:
                table = constants.PFN_BASE_EGRESS_TABLE
                # Expecial traffic for port without security group
                col_kwargs = {
                    'table': constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
                    'priority': 5,
                    'actions': "resubmit(,%d)" % table
                }
                self._add_flow(**col_kwargs)
        else:
            # No ovs firewall case
            # Egress entrance
            col_kwargs = {
                'table': constants.TRANSIENT_TABLE,
                'priority': 5,
                'actions': "resubmit(,%d)" % (constants.PFN_BASE_EGRESS_TABLE)
            }
            self._add_flow(**col_kwargs)

    def setup_privatefloating(self):
        if not self.is_privatefloating_enabled():
            return

        self.privatefloating_network = self.privatefloating_info.get(
            'privatefloating_network', {})
        if not self.privatefloating_network:
            LOG.warning(
                "[FPN] privatefloating is enabled but "
                "privatefloating network is not exits!")
            return

        # Create a new namespace and a ovs(internal) device in it.
        network_id = self.privatefloating_network.get('id')
        ns_name = "pfip-%s" % network_id

        self.ns_privatefloating_port = self.privatefloating_info.get(
            'privatefloating_port', {})
        if not self.ns_privatefloating_port:
            LOG.warning(
                "[FPN] privatefloating is enabled but "
                "privatefloating port is not exits!")
            return

        port_id = self.ns_privatefloating_port.get('id')
        interfaceObj = interface_driver.OVSInterfaceDriver(self.conf)
        tap_name = "tap%s" % port_id
        tap_name = tap_name[:14]
        mac_address = self.ns_privatefloating_port.get('mac_address')
        # this mac is important!!!
        self.ns_privatefloating_mac = mac_address

        interfaceObj.plug(network_id, port_id, tap_name, mac_address,
                          namespace=ns_name)

        # set ipv4.ip_forward
        ip_wrapper_root = ip_lib.IPWrapper()
        ip_wrapper = ip_wrapper_root.ensure_namespace(ns_name)
        ip_wrapper.netns.execute(['sysctl', '-w', 'net.ipv4.ip_forward=1'])

        # Handle l3(cidr) settings in this namespace
        for subnet1 in self.privatefloating_network.get('subnets_detail', []):
            LOG.debug("[FPN] private floating subnet: \n%s", subnet1)
            self.privatefloating_subnet_dict[subnet1['id']] = subnet1

        ips = self.get_pfn_port()

        LOG.debug("[FPN] init privatefloating ip %s", ips)
        interfaceObj.init_l3(tap_name, ips, namespace=ns_name)
        self.subnet_routes = {}
        self.route_destinations = set()
        self.route_nexthops = set()
        # Handle route settings in this namespace
        ipdevice_obj = ip_lib.IPDevice(tap_name, ns_name)

        for subnet1 in self.privatefloating_network.get('subnets_detail', []):
            LOG.debug("[FPN] private floating subnet detail: \n%s", subnet1)
            subnet_routes = subnet1['host_routes']
            routes = set()
            for route in subnet_routes:
                dest = route['destination']
                nexthop = route['nexthop']
                # nexthop_value = netaddr.IPAddress(nexthop).value
                if self._check_ip_reachable(nexthop, ips):
                    ipdevice_obj.route.add_route(dest, nexthop)
                    routes.add((str(nexthop), str(dest)))
                    self.route_destinations.add(str(dest))
                    self.route_nexthops.add(str(nexthop))
            self.subnet_routes[subnet1['id']] = (routes, subnet1['cidr'])

        # Check physnet port for private floating
        self._check_pfn_physnet_ofport()

        # Init flows
        self._init_privatefloating_flows()

    def _check_ip_reachable(self, dest, ips):
        for ip in ips:
            ip_net = netaddr.IPNetwork(ip)
            if dest in ip_net:
                return True
        return False

    def _bind_npf_port(self, port, fixed_ips, device_owner, net_uuid,
                       local_vlan_map):
        '''
            Handle none-privatefloating type port,
            like normal port(device_owner=nova-comuter)
            such as:
                * port(tapxxx) which have two subnet,
                  privatefloating subnet and normal subnet
                * port(tapxxx) which have one subnet which
                  privatefloating network
        '''
        if not port:
            return
        server_default_az = self.privatefloating_info.get(
            'default_availability_zone')
        az_privatefloating_network = self.privatefloating_info.get(
            'availability_zone_privatefloating_network')
        agent_az = self.conf.AGENT.availability_zone
        if az_privatefloating_network and agent_az not in \
                az_privatefloating_network.keys() and \
                agent_az != server_default_az:
            LOG.warning("Ovs Agent side has not set availability zone or "
                        "it sets wrong, which need to update it.")
        pfnport = PFNPort(port.vif_id, port.ofport, port.vif_mac, device_owner)

        # collect all of the ipv4 addresses and cidrs that belong to the port
        # Not support ipv6 now!!!
        fixed_ipv4s = [
            f for f in fixed_ips if netaddr.IPNetwork(
                f['ip_address']).version == 4]

        LOG.info("[FPN] fixed_ips: %s", fixed_ipv4s)

        # Delete flows first when port updates fixed_ipv4s
        if port.vif_id in self.local_ports:
            self.delete_pfnport_flows(port)

        if len(fixed_ipv4s) >= 2:
            # port(tapxxx) which have two subnet, privatefloating subnet and
            # normal subnet
            LOG.debug(
                "[FPN] privatefloating_subnet_dict: %s",
                self.privatefloating_subnet_dict)

            subs = []
            used_fixed_ipv4s = []
            for ip in reversed(fixed_ipv4s):
                sub = ip['subnet_id']
                if sub not in subs:
                    subs.append(sub)
                    used_fixed_ipv4s.append(ip)
            if len(subs) == 1:
                return
            pfnport.add_subnet(used_fixed_ipv4s[0]['subnet_id'])
            pfnport.add_subnet(used_fixed_ipv4s[1]['subnet_id'])

            privatefloating_ip = ''
            fixed_ip = ''

            # Find privatefloating-ip from ip which subnet is privatefloating
            # subnet.
            subnet_dict = self.privatefloating_subnet_dict
            if used_fixed_ipv4s[0]['subnet_id'] in subnet_dict:
                privatefloating_ip = used_fixed_ipv4s[0]['ip_address']
                fixed_ip = used_fixed_ipv4s[1]['ip_address']
            elif used_fixed_ipv4s[1]['subnet_id'] in subnet_dict:
                privatefloating_ip = used_fixed_ipv4s[1]['ip_address']
                fixed_ip = used_fixed_ipv4s[0]['ip_address']

            if privatefloating_ip:
                # Add port egress traffic path
                col_kwargs = {
                    'table': constants.PFN_BASE_EGRESS_TABLE,
                    'priority': 5,
                    'in_port': port.ofport,
                    'actions': "resubmit(,%d)" % (
                        constants.PFN_RULES_ROUTE_EGRESS_TABLE)}
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)

                # Add port egress traffic path, translate fixed-ip to
                # privatefloating-ip
                col_kwargs = {
                    'table': constants.PFN_RULES_EGRESS_TABLE,
                    'priority': 20,
                    'proto': 'ip',
                    'in_port': port.ofport,
                    'nw_src': fixed_ip,
                    'actions': "mod_nw_src:%s,resubmit(,%d)" % (
                        privatefloating_ip,
                        constants.PFN_EGRESS_TRAFFIC_TABLE
                    )
                }
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)

                # Decide process pipeline
                if self.enable_firewall:
                    # Add port ingress traffic path, translate
                    # privatefloating-ip to fixed-ip
                    col_kwargs = {
                        'table': constants.PFN_RULES_INGRESS_TABLE,
                        'priority': 20,
                        'proto': 'ip',
                        'nw_dst': privatefloating_ip,
                        'actions': "mod_nw_dst:%s,mod_dl_src:%s,mod_dl_dst:%s,"
                        "resubmit(,%d)" % (
                            fixed_ip,
                            self.ns_privatefloating_mac,
                            port.vif_mac,
                            constants.PFN_INGRESS_TRAFFIC_TABLE
                        )
                    }
                    self._add_flow(**col_kwargs)
                    pfnport.add_flow(col_kwargs)

                    # Redirect to firewall ingress entrace
                    col_kwargs = {
                        'table': constants.PFN_INGRESS_TRAFFIC_TABLE,
                        'priority': 20,
                        'dl_dst': port.vif_mac,
                        # With bundle action strip_vlan action should have
                        # a dl_vlan match rule. For IP traffic from private
                        # floating network, the vlan_id is not changed, it
                        # is still the physical vlan id of
                        # privatefloating-net's.
                        # So here needs to match pf-net's physical vlan id.
                        'dl_vlan': self.pfn_segmentation_id,
                        'actions': "load:%s->NXM_NX_REG5[],"
                        "load:%s->NXM_NX_REG6[],strip_vlan,resubmit(,%d)" % (
                            "0x{:x}".format(port.ofport),
                            "0x{:x}".format(local_vlan_map.vlan),
                            constants.BASE_INGRESS_TABLE
                        )
                    }
                    self._add_flow(**col_kwargs)
                    pfnport.add_flow(col_kwargs)

                    detail = self.plugin_rpc.get_device_details(
                        None, port.vif_id, None, host=self.host)
                    if (not detail.get('port_security_enabled', False) and not
                            detail.get('security_groups', [])):
                        col_kwargs = {
                            'table': constants.BASE_INGRESS_TABLE,
                            'priority': 1,
                            'dl_dst': port.vif_mac,
                            'reg6': "0x{:x}".format(local_vlan_map.vlan),
                            'actions': "output:%d" % (port.ofport)
                        }
                        self._add_flow(**col_kwargs)
                        pfnport.add_flow(col_kwargs)

                    # In scenarios where the physical bridge of the floating
                    # network and the port network is different, add this flow
                    # for ingress traffic to normal
                    port_bridge = self.phys_brs.get(
                        detail['physical_network']).br_name
                    port_vlan_range = n_utils.is_segment_id_in_vlan_range(
                        detail['segmentation_id'], self.bridge_vlan_mappings)
                    if port_vlan_range:
                        port_bridge = n_utils.get_bridge_by_vlan_range(
                            self.bridge_vlan_mappings, port_vlan_range)

                    pfn_phy_bridge = self.phys_brs.get(
                        self.privatefloating_network.get(
                            'provider:physical_network')).br_name
                    pfn_vlan_range = n_utils.is_segment_id_in_vlan_range(
                        self.pfn_segmentation_id, self.bridge_vlan_mappings)
                    if pfn_vlan_range:
                        pfn_phy_bridge = n_utils.get_bridge_by_vlan_range(
                            self.bridge_vlan_mappings, pfn_vlan_range)
                    if port_bridge != pfn_phy_bridge:
                        table = constants.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE
                        col_kwargs = {
                            'table': table,
                            'priority': 20,
                            'reg5': "0x{:x}".format(port.ofport),
                            'in_port': self.pfip_physnet_ofport,
                            'actions': "mod_vlan_vid:%d,normal" %
                                       local_vlan_map.vlan
                        }
                        self._add_flow(**col_kwargs)
                        pfnport.add_flow(col_kwargs)
                else:
                    # Add port ingress traffic path, translate
                    # privatefloating-ip to fixed-ip
                    col_kwargs = {
                        'table': constants.PFN_RULES_INGRESS_TABLE,
                        'priority': 20,
                        'proto': 'ip',
                        'nw_dst': privatefloating_ip,
                        # With bundle action strip_vlan action should have
                        # a dl_vlan match rule. For IP traffic from private
                        # floating network, the vlan_id is not changed, it
                        # is still the physical vlan id of
                        # privatefloating-net's.
                        # So here needs to match pf-net's physical vlan id.
                        'dl_vlan': self.pfn_segmentation_id,
                        'actions': "mod_nw_dst:%s,mod_dl_src:%s,mod_dl_dst:%s,"
                        "strip_vlan,output:%d" % (
                            fixed_ip,
                            self.ns_privatefloating_mac,
                            port.vif_mac,
                            port.ofport
                        )
                    }
                    self._add_flow(**col_kwargs)
                    pfnport.add_flow(col_kwargs)

                if self.enable_ovs_stateless_firewall:
                    detail = self.plugin_rpc.get_device_details(
                        None, port.vif_id, None, host=self.host)
                    port_security = detail.get('port_security_enabled', False)
                    port_security_groups = detail.get('security_groups', [])
                    direct_tb = constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
                    uni_cast = "00:00:00:00:00:00/01:00:00:00:00:00"
                    reg_net = "0x{:x}".format(local_vlan_map.vlan)
                    accept_egress_tb = constants.BASE_INGRESS_TABLE
                    for subnet in self.privatefloating_network.get(
                            'subnets_detail', []):
                        subnet_routes = subnet['host_routes']
                        for route in subnet_routes:
                            dest_net = netaddr.IPNetwork(route['destination'])
                            if dest_net.version == n_const.IP_VERSION_6:
                                continue
                            col_kwargs = {
                                'table': accept_egress_tb,
                                'priority': 200,
                                'dl_vlan': local_vlan_map.vlan,
                                'proto': 'ip',
                                'nw_dst': route['destination'],
                                'actions': "strip_vlan,resubmit(,%d)" % (
                                    constants.PFN_BASE_EGRESS_TABLE)
                            }
                            self._add_flow(**col_kwargs)
                            pfnport.add_flow(col_kwargs)

                            if (not port_security and not
                                    port_security_groups):
                                col_kwargs = {
                                    'table': direct_tb,
                                    'priority': 11,
                                    'proto': 'ip',
                                    'dl_src': port.vif_mac,
                                    'nw_dst': route['destination'],
                                    'dl_dst': uni_cast,
                                    'reg6': reg_net,
                                    'actions': "resubmit(,%d)" % (
                                        constants.PFN_BASE_EGRESS_TABLE)
                                }
                                self._add_flow(**col_kwargs)
                                pfnport.add_flow(col_kwargs)

                mac_value = self.ns_privatefloating_mac

                # Cross-node case(router nexthop in annother node)
                # [ARP] arp request packet(VM-privatefloating_ip)
                # from patch port response pfip namespace MAC
                col_kwargs = {
                    'table': constants.PFN_ARP_RESPONSER_TABLE,
                    'priority': 20,
                    'proto': 'arp',
                    'arp_op': 1,
                    'in_port': self.pfip_physnet_ofport,
                    'arp_tpa': privatefloating_ip,
                    'actions': "move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                    "mod_dl_src:%s,"
                    "load:0x2->NXM_OF_ARP_OP[],"
                    "move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],"
                    "move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],"
                    "load:0x%s->NXM_NX_ARP_SHA[],"
                    "load:%s->NXM_OF_ARP_SPA[],in_port" %
                    (self.ns_privatefloating_mac,
                     mac_value.replace(':', ''),
                     "0x{:04x}".format(
                         netaddr.IPAddress(privatefloating_ip).value))}
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)

                # Inter-node case(router nexthop in this node)
                # [ARP] arp request packet(VM-privatefloating_ip)
                # in integrate bridge response port MAC
                col_kwargs = {
                    'table': constants.PFN_ARP_RESPONSER_TABLE,
                    'priority': 10,
                    'proto': 'arp',
                    'arp_op': 1,
                    'arp_tpa': privatefloating_ip,
                    'actions': "move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],"
                    "mod_dl_src:%s,"
                    "load:0x2->NXM_OF_ARP_OP[],"
                    "move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],"
                    "move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],"
                    "load:0x%s->NXM_NX_ARP_SHA[],"
                    "load:%s->NXM_OF_ARP_SPA[],in_port" %
                    (port.vif_mac,
                     port.vif_mac.replace(':', ''),
                     "0x{:04x}".format(
                         netaddr.IPAddress(privatefloating_ip).value))}
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)

        elif len(fixed_ipv4s) == 1:
            if net_uuid == self.privatefloating_network.get('id'):
                # normal port(tapxxx) which have one subnet(privatefloating
                # network)
                pfnport.add_subnet(fixed_ipv4s[0]['subnet_id'])

                fixed_ip_address = fixed_ipv4s[0]['ip_address']

                # Add port egress traffic fast path, skip arp flood
                # Match all vlan ID packets and strip or pop its vlan head
                col_kwargs = {
                    'table': constants.PFN_EGRESS_TRAFFIC_TABLE,
                    'priority': 20,
                    'proto': 'ip',
                    'vlan_tci': '0x1000/0x1000',
                    'reg2': "0x{:04x}".format(
                        netaddr.IPAddress(fixed_ip_address).value),
                    'actions': "mod_dl_dst:%s,strip_vlan,output:%d" % (
                        port.vif_mac,
                        port.ofport
                    )
                }
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)

                # Add port ingress traffic entrance
                col_kwargs = {
                    'table': constants.PFN_BASE_INGRESS_TABLE,
                    'priority': 10,
                    'proto': 'arp',
                    'in_port': port.ofport,
                    'actions': "resubmit(,%d)" % (
                        constants.PFN_ARP_RESPONSER_TABLE)}
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)

                col_kwargs = {
                    # 'table': constants.LOCAL_SWITCHING,
                    # this port type flows in LOCAL_SWITCHING table will been
                    # cleaned ???
                    'table': constants.PFN_BASE_INGRESS_TABLE,
                    'priority': 5,
                    'in_port': port.ofport,
                    'actions': "resubmit(,%d)" % (
                        constants.PFN_RULES_INGRESS_TABLE)
                }
                self._add_flow(**col_kwargs)
                pfnport.add_flow(col_kwargs)
            else:
                if port.vif_id in self.local_ports:
                    self.delete_pfnport_flows(port)

        else:
            if port.vif_id in self.local_ports:
                self.delete_pfnport_flows(port)
            LOG.warning("[FPN] not found ipv4 address!!!")

        LOG.info("[FPN] pfnport: %s", pfnport)
        self.local_ports[port.vif_id] = pfnport

    def delete_pfnport_flows(self, port):
        pfnport = self.local_ports[port.vif_id]
        delete_flows = pfnport.get_flows()
        for flow in delete_flows:
            self._delete_flows(**flow)

    def _bind_pf_port(self, port, local_vlan_map, segmentation_id):
        '''
            Handle privatefloating type
            port(device_owner=network:privatefloating).
            such as:
                * port(tapxxx) which have one privatefloating
                  ip in pfip namespace
        '''
        if not self.ns_privatefloating_port:
            return

        pfnport = PFNPort(port.vif_id, port.ofport, port.vif_mac,
                          n_const.DEVICE_OWNER_PRIVATEFLOATING)

        # Egress flows
        # Default egress flow: ip
        col_kwargs = {
            'table': constants.PFN_EGRESS_TRAFFIC_TABLE,
            'dl_vlan': local_vlan_map.vlan,
            'priority': 5,
            'proto': 'ip',
            'actions': "strip_vlan,mod_dl_dst:%s,output:%d" % (
                self.ns_privatefloating_mac,
                port.ofport
            )
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

        # Private floating netwrok handle, only one subnet normally
        for subnet_id in self.privatefloating_network.get('subnets', []):
            pfnport.add_subnet(subnet_id)

        for subnet in self.privatefloating_network.get('subnets_detail', []):
            LOG.debug("[FPN] private floating subnet detail: \n%s", subnet)
            subnet_routes = subnet['host_routes']

            # Route table
            for route in subnet_routes:
                nexthop = route['nexthop']
                target = route['destination']
                dest_net = netaddr.IPNetwork(target)
                if dest_net.version == n_const.IP_VERSION_6:
                    continue
                self.update_subnet_route_flows(
                    nexthop, target, local_vlan_map, pfnport)

        # Ingress flows
        # Default ingress flow
        col_kwargs = {
            'table': constants.PFN_RULES_INGRESS_TABLE,
            'priority': 0,
            'dl_vlan': segmentation_id,
            'proto': 'ip',
            'actions': "mod_vlan_vid:%d,normal" % local_vlan_map.vlan
        }
        self._add_flow(**col_kwargs)

        # Cross-node case
        # Ingress entrance, all ingress traffic after privatefloating handle
        col_kwargs = {
            'table': constants.LOCAL_SWITCHING,
            'priority': 30,
            'proto': 'arp',
            'in_port': self.pfip_physnet_ofport,
            'dl_vlan': segmentation_id,
            # For arp because it will hit the final NORMAL action
            # to tap device in pfip-namespace. So the vlan id
            # should be changed to local vlan id of private-fip-network.
            'actions': "mod_vlan_vid:%d,resubmit(,%d)" % (
                local_vlan_map.vlan,
                constants.PFN_BASE_INGRESS_TABLE
            )
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

        col_kwargs = {
            'table': constants.LOCAL_SWITCHING,
            'priority': 30,
            'proto': 'ip',
            'in_port': self.pfip_physnet_ofport,
            'dl_vlan': segmentation_id,
            # For IP traffic it will hit finall output
            # (or strip vlan and modify reg5/6 to go through
            #  the conntrack table) to VM port
            # by directly changing the L2 MACs and L3 IPs.
            # So here left the physical vlan as it is.
            'actions': "resubmit(,%d)" % (
                constants.PFN_BASE_INGRESS_TABLE
            )
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

        col_kwargs = {
            'table': constants.PFN_BASE_INGRESS_TABLE,
            'priority': 10,
            'proto': 'arp',
            'in_port': self.pfip_physnet_ofport,
            'actions': "resubmit(,%d)" % (constants.PFN_ARP_RESPONSER_TABLE)
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)

        if self.conf.AGENT.explicitly_egress_direct:
            table = constants.PFN_ARP_RESPONSER_TABLE
            col_kwargs = {
                'table': constants.PFN_BASE_INGRESS_TABLE,
                'priority': 10,
                'proto': 'arp',
                'in_port': port.ofport,
                'actions': "resubmit(,%d)" % table
            }
            self._add_flow(**col_kwargs)
            pfnport.add_flow(col_kwargs)

        col_kwargs = {
            'table': constants.PFN_BASE_INGRESS_TABLE,
            'priority': 5,
            'in_port': self.pfip_physnet_ofport,
            'actions': "resubmit(,%d)" % (constants.PFN_RULES_INGRESS_TABLE)
        }
        self._add_flow(**col_kwargs)
        pfnport.add_flow(col_kwargs)
        # Cross-node case end

        # Inter-node case don't need handle

        LOG.info("[FPN] pfnport: %s", pfnport)
        self.local_ports[port.vif_id] = pfnport

    def bind_port_to_pfn(self, port, net_uuid, local_vlan_map, segmentation_id,
                         fixed_ips, device_owner):
        if not self.enable_private_floating:
            return

        LOG.info("[FPN] bind port: %s", port)

        if device_owner == n_const.DEVICE_OWNER_PRIVATEFLOATING:
            self._bind_pf_port(port, local_vlan_map, segmentation_id)
        else:
            self._bind_npf_port(port, fixed_ips, device_owner, net_uuid,
                                local_vlan_map)

    def unbind_port_from_pfn(self, port):
        if not self.enable_private_floating:
            return

        LOG.info("[FPN] unbind port: %s", port)

        pfnport = self.local_ports[port.vif_id]
        LOG.info("[FPN] pfnport: %s", pfnport)
        flows = pfnport.get_flows()

        for flow in flows:
            self._delete_flows(**flow)

        # release port state
        self.local_ports.pop(port.vif_id, None)
